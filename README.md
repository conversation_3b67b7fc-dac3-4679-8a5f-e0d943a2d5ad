# This is just a test README file it has nothing to do with project

## Project setup

```python
npm ci
```

## Compiles and hot-reloads for development

```python
npm run dev
```

## Compiles and minifies for production

```python
npm run build
```

## Lints and fixes files

```python
npm run lint
```

## Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

export NODE_OPTIONS=--openssl-legacy-provider

