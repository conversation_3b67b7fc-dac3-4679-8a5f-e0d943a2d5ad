diff --git a/node_modules/@quasar/quasar-ui-qcalendar/dist/index.esm.js b/node_modules/@quasar/quasar-ui-qcalendar/dist/index.esm.js
index 0624cea..1280246 100644
--- a/node_modules/@quasar/quasar-ui-qcalendar/dist/index.esm.js
+++ b/node_modules/@quasar/quasar-ui-qcalendar/dist/index.esm.js
@@ -4159,6 +4159,7 @@ var QCalendarDay = defineComponent({
       emittedValue = ref(props.modelValue),
       size = reactive({ width: 0, height: 0 }),
       dragOverHeadDayRef = ref(false),
+      dragOverPrependDayRef = ref(false),
       dragOverInterval = ref(false),
       // keep track of last seen start and end dates
       lastStart = ref(null),
@@ -4912,6 +4913,7 @@ var QCalendarDay = defineComponent({
         class: 'q-calendar-day__day-container'
       }, [
         isSticky.value === true && props.noHeader !== true && __renderHead(),
+        __renderPrependSlot(),
         h('div', {
           style: {
             display: 'flex',
@@ -4925,6 +4927,167 @@ var QCalendarDay = defineComponent({
       ])
     }
 
+    function __renderPrependSlot () {
+      return h('div', {
+        roll: 'presentation',
+        class: {
+          'q-calendar-day__head': true,
+        },
+        style: {
+          marginRight: scrollWidth.value + 'px'
+        }
+      }, [
+        __renderPrependIntervals(),
+        __renderPrependsColumn()
+      ])
+    }
+
+    function __renderPrependsColumn () {
+      return h('div', {
+        ref: headerColumnRef,
+        class: {
+          'q-calendar-day__head--days__column': true
+        }
+      }, [
+        __renderPrependsRow()
+      ])
+    }
+
+    function __renderPrependsRow () {
+      return h('div', {
+        class: {
+          'q-calendar-day__head--days__weekdays': true
+        }
+      }, [
+        ...__renderPrepends()
+      ])
+    }
+
+    function __renderPrepends () {
+      if (days.value.length === 1 && parseInt(props.columnCount, 10) > 0) {
+        return Array.apply(null, new Array(parseInt(props.columnCount, 10)))
+          .map((_, i) => i + parseInt(props.columnIndexStart, 10))
+          .map(columnIndex => __renderPrepend(days.value[ 0 ], columnIndex))
+      }
+      else {
+        return days.value.map(day => __renderPrepend(day))
+      }
+    }
+
+    function __renderPrepend (day, columnIndex) {
+      const prependDaySlot = slots[ 'prepend-day' ];
+      const activeDate = props.noActiveDate !== true && __isActiveDate(day);
+
+      const scope = getScopeForSlot(day, columnIndex);
+      scope.activeDate = activeDate;
+      scope.droppable = dragOverPrependDayRef.value === day.date;
+      scope.disabled = (props.disabledWeekdays ? props.disabledWeekdays.includes(day.weekday) : false);
+
+      const width = isSticky.value === true ? props.cellWidth : computedWidth.value;
+      const styler = props.weekdayStyle || dayStyleDefault;
+      const style = {
+        width,
+        maxWidth: width,
+        minWidth: width,
+        ...styler({ scope })
+      };
+      if (isSticky.value === true) {
+        style.minWidth = width;
+      }
+      const weekdayClass = typeof props.weekdayClass === 'function' ? props.weekdayClass({ scope }) : {};
+      const isFocusable = props.focusable === true && props.focusType.includes('weekday');
+      const key = day.date + (columnIndex !== undefined ? '-' + columnIndex : '');
+
+      const data = {
+        key,
+        ref: (el) => { datesRef.value[ key ] = el; },
+        tabindex: isFocusable === true ? 0 : -1,
+        class: {
+          'q-calendar-day__head--day': true,
+          ...weekdayClass,
+          ...getRelativeClasses(day),
+          'q-active-date': activeDate,
+          'q-calendar__hoverable': props.hoverable === true,
+          'q-calendar__focusable': isFocusable === true
+        },
+        style,
+        onFocus: (e) => {
+          if (isFocusable === true) {
+            focusRef.value = key;
+          }
+        },
+        // onKeydown: (e) => {
+        //   if (day.disabled !== true
+        //     && isKeyCode(e, [ 13, 32 ])) {
+        //     e.stopPropagation();
+        //     e.preventDefault();
+        //   }
+        // },
+        // onKeyup: (e) => {
+        //   // allow selection of date via Enter or Space keys
+        //   if (day.disabled !== true
+        //     && isKeyCode(e, [ 13, 32 ])) {
+        //     emittedValue.value = day.date;
+        //   }
+        // },
+        // ...getDefaultMouseEventHandlers('-prepend-day', event => {
+        //   return { scope, event }
+        // }),
+        onDragenter: (e) => {
+          if (props.dragEnterFunc !== undefined && typeof props.dragEnterFunc === 'function') {
+            props.dragEnterFunc(e, 'prepend-day', scope) === true
+              ? dragOverPrependDayRef.value = day.date
+              : dragOverPrependDayRef.value = '';
+          }
+        },
+        onDragover: (e) => {
+          if (props.dragOverFunc !== undefined && typeof props.dragOverFunc === 'function') {
+            props.dragOverFunc(e, 'prepend-day', scope) === true
+              ? dragOverPrependDayRef.value = day.date
+              : dragOverPrependDayRef.value = '';
+          }
+        },
+        onDragleave: (e) => {
+          if (props.dragLeaveFunc !== undefined && typeof props.dragLeaveFunc === 'function') {
+            props.dragLeaveFunc(e, 'prepend-day', scope) === true
+              ? dragOverPrependDayRef.value = day.date
+              : dragOverPrependDayRef.value = '';
+          }
+        },
+        onDrop: (e) => {
+          if (props.dropFunc !== undefined && typeof props.dropFunc === 'function') {
+            props.dropFunc(e, 'prepend-day', scope) === true
+              ? dragOverPrependDayRef.value = day.date
+              : dragOverPrependDayRef.value = '';
+          }
+        }
+      };
+
+      return h('div', data, [
+        prependDaySlot !== undefined && prependDaySlot({ scope }),
+        useFocusHelper()
+      ])
+    }
+
+    function __renderPrependIntervals() {
+      const slot = slots['prepend-intervals']
+
+      const scope = {
+        timestamps: days.value,
+        days: days.value, // deprecated
+        date: props.modelValue
+      }
+
+      return h('div', {
+        class: {
+          'q-calendar-day__intervals-column': true,
+          'q-calendar__sticky': isSticky.value === true
+        },
+      }, [
+        slot && slot({ scope })
+      ])
+    }
+
     function __renderDays () {
       if (days.value.length === 1 && parseInt(props.columnCount, 10) > 0) {
         return Array.apply(null, new Array(parseInt(props.columnCount, 10)))
