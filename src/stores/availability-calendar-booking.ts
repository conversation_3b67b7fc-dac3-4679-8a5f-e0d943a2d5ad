import { defineStore } from 'pinia'
import { ref } from 'vue'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

// Updated Schedule Board API interfaces based on API V2 documentation
interface ScheduleBoardParams {
  id_clinic: number
  id_item_service: number
  date_start: string // YYYY-MM-DD
  date_end: string // YYYY-MM-DD
  id_employee?: number
  time_start?: string // HH:MM
  time_end?: string // HH:MM
}

// New API V2 column data structure
interface ColumnData {
  display: string
  can_book?: boolean
}

// Updated SubRow interface for API V2
interface ScheduleBoardSubRow {
  level: 'L1' | 'L2' | 'L3' | 'L4'
  label: string
  slot_max?: number
  booked?: number
  available_capacity?: number
  available_status?: 'AVAILABLE' | 'CLOSED' | 'NOT_AVAILABLE'
  can_book?: boolean
  id_item_service?: number
  id_booking_item?: number
  columns: Record<string, ColumnData> // time -> slot data
}


interface ScheduleBoardRow {
  type: 'date_group'
  date: string
  full_date: string // YYYY-MM-DD format
  sub_rows: ScheduleBoardSubRow[]
}

interface ScheduleBoardData {
  time_headers: string[]
  rows: ScheduleBoardRow[]
}

interface ScheduleBoardResponse {
  code: number
  success: boolean
  message: string
  data: {
    filters: ScheduleBoardParams
    schedule_board: ScheduleBoardData
    summary: {
      total_slots: number
      available_slots: number
      dates_count: number
      time_headers_count: number
      availability_rate: number
    }
  }
}


const useAvailabilityCalendarBookingStore = defineStore('availability-calendar-booking', () => {
  // State
  const scheduleBoardData = ref<ScheduleBoardResponse['data'] | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getScheduleBoardData = () => scheduleBoardData.value
  const isLoading = () => loading.value
  const getError = () => error.value

  // Actions

  /**
   * Fetch Schedule Board data using the new V2 API
   */
  const fetchScheduleBoardData = async (params: ScheduleBoardParams): Promise<ScheduleBoardResponse | null> => {
    try {
      loading.value = true
      error.value = null

      // Validate required parameters
      if (!params.id_clinic || !params.id_item_service || !params.date_start || !params.date_end) {
        throw new Error('Required parameters: id_clinic, id_item_service, date_start, date_end')
      }

      // Build API parameters
      const apiParams: any = {
        id_clinic: params.id_clinic,
        id_item_service: params.id_item_service,
        date_start: params.date_start,
        date_end: params.date_end
      }

      // Add optional parameters
      if (params.id_employee) {
        apiParams.id_employee = params.id_employee
      }
      if (params.time_start) {
        apiParams.time_start = params.time_start
      }
      if (params.time_end) {
        apiParams.time_end = params.time_end
      }

      // Make API call - sesuai dengan dokumentasi endpoint baru
      const response = await mtUtils.callApi(
        selectOptions.reqMethod.GET,
        '/booking/tx-slots/schedule-board/',
        apiParams
      )

      // Process response
      if (response) {
        scheduleBoardData.value = response
        return response
      } else {
        throw new Error(response?.message || 'Failed to fetch Schedule Board data')
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Error fetching schedule board data'
      error.value = errorMessage
      console.error('Schedule Board Store Error:', err)
      console.error('API Parameters:', params)
      console.error('Full Error Details:', {
        message: err.message,
        status: err.status,
        response: err.response,
        stack: err.stack
      })
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }


  /**
   * Get L4 bookable slots for display
   */
  const getBookableSlots = () => {
    if (!scheduleBoardData.value?.schedule_board) return []

    const bookableSlots: any[] = []
    const { rows, time_headers } = scheduleBoardData.value.schedule_board

    rows.forEach((row) => {
      const date = row.full_date // YYYY-MM-DD

      row.sub_rows.forEach((subRow) => {
        // Only L4 rows are bookable according to new API documentation
        if (subRow.level !== 'L4') return

        time_headers.forEach((timeHeader) => {
          const cellData = subRow.columns[timeHeader]
          
          // Skip empty cells or unavailable slots
          if (!cellData || cellData.display === '-' || !cellData.can_book) return
          
          // Extract time information from display text
          const match = cellData.display.match(/(\d{1,2}:\d{2})\s*~\s*(\d{1,2}:\d{2})/)
          if (match) {
            const startTime = match[1]
            const endTime = match[2]
            
            bookableSlots.push({
              date,
              timeHeader,
              startTime,
              endTime,
              display: cellData.display,
              can_book: cellData.can_book,
              level: subRow.level,
              label: subRow.label,
              slot_max: subRow.slot_max,
              booked: subRow.booked,
              available_capacity: subRow.available_capacity,
              available_status: subRow.available_status,
              id_item_service: subRow.id_item_service,
              id_booking_item: subRow.id_booking_item
            })
          }
        })
      })
    })

    return bookableSlots
  }

  // Clear store data
  const clearData = () => {
    scheduleBoardData.value = null
    error.value = null
    loading.value = false
  }

  // Get summary statistics
  const getSummary = () => {
    return scheduleBoardData.value?.summary || null
  }

  // Get available time slots for a specific date
  const getAvailableSlotsForDate = (date: string) => {
    if (!scheduleBoardData.value?.schedule_board) return []
    
    const dateRow = scheduleBoardData.value.schedule_board.rows.find(row => row.full_date === date)
    if (!dateRow) return []
    
    const availableSlots: any[] = []
    
    dateRow.sub_rows.forEach(subRow => {
      // Focus on L4 rows which are the final bookable slots
      if (subRow.level === 'L4') {
        Object.entries(subRow.columns).forEach(([timeHeader, cellData]) => {
          if (cellData && cellData.can_book && cellData.display !== '-') {
            const match = cellData.display.match(/(\d{1,2}:\d{2})\s*~\s*(\d{1,2}:\d{2})/)
            if (match) {
              availableSlots.push({
                timeHeader,
                startTime: match[1],
                endTime: match[2],
                display: cellData.display,
                can_book: cellData.can_book,
                level: subRow.level,
                slot_max: subRow.slot_max,
                booked: subRow.booked,
                available_capacity: subRow.available_capacity,
                id_item_service: subRow.id_item_service,
                id_booking_item: subRow.id_booking_item
              })
            }
          }
        })
      }
    })
    
    return availableSlots
  }

  // Get all dates in the current range
  const getDateRange = () => {
    return scheduleBoardData.value?.schedule_board.rows.map(row => row.full_date) || []
  }

  return {
    // State
    scheduleBoardData,
    loading,
    error,
    
    // Getters
    getScheduleBoardData,
    isLoading,
    getError,
    getSummary,
    getBookableSlots,
    getAvailableSlotsForDate,
    getDateRange,
    
    // Actions
    fetchScheduleBoardData,
    clearData
  }
})

export default useAvailabilityCalendarBookingStore