import { defineStore } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import { sortBy } from 'lodash'
import { CarteConfig } from '@/types/types'

export const useCarteConfigStore = defineStore('carte_config', {
  state: () => ({
    all_carte_config: [] as CarteConfig[],
    all_parent_carte_config: [] as CarteConfig[],
    carte_config: {} as CarteConfig,
  }),
  getters: {
    getAllCarteConfig: (state) => state.all_carte_config,
    getAllParentCarteConfig: (state) => state.all_parent_carte_config,
    getCarteConfig: (state) => state.carte_config,
  },

  persist: true,
  actions: {
    async fetchCarteConfig(data: any | null = null){
      let res: CarteConfig[] = await mtUtils.callApi(
        selectOptions.reqMethod.GET,
        'mst/carte-config',
        data
      )
      if (res) {
        this.all_carte_config = sortBy(res, 'display_order')
        if (this.all_carte_config.filter((v) => v.type_config_layer == 1).length > 0) {
          this.all_parent_carte_config = this.all_carte_config.filter((v) => v.type_config_layer == 1)
        }
      }
    },

    async submitCarteConfig(data: object) {
      let res = await mtUtils.callApi(
        selectOptions.reqMethod.POST,
        'mst/carte-config',
        data
      )
      if (res) {
        this.carte_config = res
      }
    },

    async fetchCarteConfigById(id_carte_config: number | string) {
      let res = await mtUtils.callApi(
        selectOptions.reqMethod.GET,
        `/mst/carte-config/${id_carte_config}`
      )
      if (res) {
        this.carte_config = res
      }
    },
    async updateCarteConfig(id_carte_config: number | string, data: object) {
      let res = await mtUtils.callApi(
        selectOptions.reqMethod.PUT,
        `/mst/carte-config/${id_carte_config}`,
        data
      )
      if (res) {
        this.carte_config = res
      }
    },

    async destroyCarteConfig(id_carte_config: number | string) {
      let res = await mtUtils.callApi(
        selectOptions.reqMethod.DELETE,
        `/mst/carte-config/${id_carte_config}`
      )
      return res ? true : false
    },
  }
})

export default useCarteConfigStore
