import { defineStore } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

export type EmployeeWorkSchedule = {
  id_employee_workschedule?: number;
  type_weekday: number;
  date_booking_special?: string | null;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: number | boolean;
  id_booking_item?: number | null;
}

export type Employee = {
  id_employee: number;
  name_display?: string;
  employee_workschedule_list: EmployeeWorkSchedule[];
}

export type WorkScheduleRequest = {
  id_clinic: number;
  employee_list: Employee[];
}

export type WorkScheduleResponse = {
  id_clinic: number;
  employee_list: Employee[];
}

// Interface for the delete work schedules request body
export type DeleteWorkScheduleRequest = {
  id_employee_workschedule_list: number[];
}

// Interface for the delete work schedules response
export type DeleteWorkScheduleResponse = {
  data: {
    deleted_count: number;
  };
  message: string;
  code: number;
  status: number;
}

// Additional types from booking-items.ts related to work schedules
export type CommonWorkScheduleRequest = {
  id_clinic: number
  employee_list: {
    id_employee: number
    employee_workschedule_list: EmployeeWorkSchedule[]
  }[]
}

export type CommonWorkScheduleResponse = {
  data: {
    id_clinic: number
    employee_list: {
      id_employee: number
      name_display: string
      employee_workschedule_list: EmployeeWorkSchedule[]
    }[]
  }
  message: string
  code: number
}

// Define types for the unified scheduling API based on documentation
export interface SchedulingApiResponse {
  meta: {
    clinic_id: string;
    period_type: string;
    date_range: {
      start: string;
      end: string;
    }
  };
  employees: Array<{
    name_display: string;
    type_occupation: number;
    flg_calendar: boolean;
  }>;
  business_hour_slots_info: Array<{
    name_business_hour: string;
    type_business_day: number;
  }>;
  business_days?: WeeklyScheduleDay[];
  monthly_days?: MonthlyScheduleDay[];
  special_days?: Array<{
    date: string;
    type_business_day: number;
    name_business_hour: string;
    business_hour_slot_id: number;
    type_weekday: number;
  }>;
}

export interface TimeSlotData {
  slot_number: number;
  business_time: {
    start: string;
    end: string;
  };
  checkin_time: {
    start: string;
    end: string;
  };
  ticket_issue_time: {
    start: string;
    end: string;
  };
  ticket_limit: number | null;
}

export interface BusinessHourSlot {
  type_business_day: number;
  name_business_hour: string;
  display_order: number;
  time_slots: TimeSlotData[];
}

export interface EmployeeScheduleData {
  id_employee: number;
  name_display: string;
  type_occupation: number;
  flg_calendar: boolean;
  schedules: Array<{
    id_employee_workschedule?: number;
    time_workschedule_start: string;
    time_workschedule_end: string;
    time_workschedule_start2?: string;
    time_workschedule_end2?: string;
    time_workschedule_start3?: string;
    time_workschedule_end3?: string;
    flg_whole_dayoff: boolean;
  }>;
}

export interface WeeklyScheduleDay {
  day_index: number;
  type_weekday: number;
  id_business_day?: number;
  business_hour_slot: BusinessHourSlot;
  slot_name: string;
  slot_type: number;
  employee_schedules: EmployeeScheduleData[];
}

export interface MonthlyScheduleDay {
  date: string;
  day_of_week: number;
  type_weekday: number;
  business_hour_slot: BusinessHourSlot;
  slot_name: string;
  slot_type: number;
  day_name: string;
  special_schedule: any;
  employee_schedules: EmployeeScheduleData[];
}

export type WorkScheduleDetailResponse = {
  id_employee_workschedule: number;
  id_employee: number;
  type_weekday: number;
  date_booking_special: string | null;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  min_rest: number;
  employee: {
    id_employee: number;
    name_display: string;
    type_occupation: number;
  };
}

export enum UpdateMode {
  NORMAL = 1,
  OFF = 2
}
export const useWorkScheduleStore = defineStore('workSchedules', {
  state: () => ({
    workSchedules: null as WorkScheduleResponse | null,
    recentWorkSchedule: null as WorkScheduleResponse | null,
    
    // Additional state from booking-items.ts
    commonWorkSchedules: null as CommonWorkScheduleResponse | null,
    schedulingData: null as SchedulingApiResponse | null,
    weeklySchedulingData: [] as WeeklyScheduleDay[],
    monthlySchedulingData: [] as MonthlyScheduleDay[],
    workSchedulePdfData: null as any,
    workScheduleDetail: null as WorkScheduleDetailResponse | null,
  }),
  
  getters: {
    getWorkSchedules: (state) => state.workSchedules,
    getRecentWorkSchedule: (state) => state.recentWorkSchedule,
    
    // Helper getter to retrieve work schedules for a specific employee
    getEmployeeWorkSchedules: (state) => (id_employee: number) => {
      if (state.workSchedules?.employee_list) {
        const employee = state.workSchedules.employee_list.find(emp => emp.id_employee === id_employee)
        return employee?.employee_workschedule_list || []
      }
      return []
    },
    
    // Additional getters from booking-items.ts
    getCommonWorkSchedules: (state) => state.commonWorkSchedules,
    getSchedulingData: (state) => state.schedulingData,
    getWorkSchedulePdfData: (state) => state.workSchedulePdfData,
    getWorkScheduleDetail: (state) => state.workScheduleDetail,
  },
  
  actions: {
    async fetchWorkSchedules(clinic_id: number, params?: {
      booking_item_id?: number;
      id_employee?: number;
      type_weekday?: number;
      date_booking_special?: string;
      flg_whole_dayoff?: boolean | number;
    }) {
      try {
        const requestParams = {
          id_clinic: clinic_id,
          ...params
        }

        const response = await mtUtils.callApi(
          selectOptions.reqMethod.GET, 
          '/mst/work-schedules',
          requestParams
        )
        
        if (response) {
          this.workSchedules = response;
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch employee work schedules. Please try again later.')
      }
    },
    
    async createOrUpdateWorkSchedules(workScheduleData: WorkScheduleRequest) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST,
          '/mst/work-schedules',
          workScheduleData
        )
        
        if (response) {
          this.recentWorkSchedule = response;
          // Update the main workSchedules if it's for the same clinic
          if (this.workSchedules && this.workSchedules.id_clinic === workScheduleData.id_clinic) {
            this.workSchedules = response;
          }
          return response;
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to update employee work schedules. Please try again later.')
      }
      return null;
    },
    
    /**
     * Delete multiple work schedules by their IDs
     */
    async deleteWorkSchedules(scheduleIds: number[]) {
      try {
        const data: DeleteWorkScheduleRequest = {
          id_employee_workschedule_list: scheduleIds
        }
        
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST,
          '/mst/work-schedules/delete',
          data
        )
        
        if (response) {
          // If we have workSchedules loaded, update them by removing the deleted schedules
          if (this.workSchedules && this.workSchedules.employee_list) {
            // For each employee, filter out the deleted schedules
            this.workSchedules.employee_list = this.workSchedules.employee_list.map(employee => {
              return {
                ...employee,
                employee_workschedule_list: employee.employee_workschedule_list.filter(
                  schedule => !scheduleIds.includes(schedule.id_employee_workschedule as number)
                )
              };
            });
            
            // Filter out employees with no remaining schedules if needed
            // Uncomment this if you want to remove employees with no schedules
            // this.workSchedules.employee_list = this.workSchedules.employee_list.filter(
            //   employee => employee.employee_workschedule_list.length > 0
            // );
          }
          
          mtUtils.autoCloseAlert('シフト情報が削除されました。')
          return response;
        }
      } catch (error) {
        mtUtils.autoCloseAlert('シフト情報の削除に失敗しました。')
        console.error('Failed to delete work schedules:', error)
      }
      return null;
    },
    
    // Helper method to get the correct type_weekday value
    getWeekdayTypeCode(day: string, isSpecialDate: boolean = false): number {
      if (isSpecialDate) return 99;
      
      const weekdays: Record<string, number> = {
        'common': 1,
        'monday': 11,
        'tuesday': 12,
        'wednesday': 13,
        'thursday': 14,
        'friday': 15,
        'saturday': 16,
        'sunday': 17
      };
      
      return weekdays[day.toLowerCase()] || 1;
    },
    
    // Helper method to format data for a single employee schedule
    formatEmployeeSchedule(
      id_employee: number,
      schedules: EmployeeWorkSchedule[]
    ): Employee {
      return {
        id_employee,
        employee_workschedule_list: schedules
      };
    },

    // Additional actions from booking-items.ts
    /**
     * Fetch common work schedules for employees with calendar flag
     */
    async fetchCommonWorkSchedules(clinic_id: number, params?: {
      booking_item_id?: number;
      employee_id?: number;
      type_weekday?: number;
      date_booking_special?: string;
      flg_whole_dayoff?: boolean | number;
    }) {
      try {
        const requestParams = {
          id_clinic: clinic_id,
          ...params
        }

        const response = await mtUtils.callApi(
          selectOptions.reqMethod.GET, 
          '/mst/work-schedules', 
          requestParams
        )
        if (response) {
          this.commonWorkSchedules = response
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch common work schedules.')
        console.error(error)
        return null
      }
    },

    /**
     * Create or update common work schedules
     */
    async createOrUpdateCommonWorkSchedules(data: CommonWorkScheduleRequest) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST, 
          '/mst/work-schedules', 
          data
        )
        if (response) {
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to update common work schedules.')
        console.error(error)
        return null
      }
    },

    /**
     * Helper method to get the weekly schedule for a specific doctor
     * Used for the ViewWeeklyShift component
     */
    getWeeklyScheduleForDoctor(doctorId: number) {
      // First check workSchedules
      if (this.workSchedules?.employee_list) {
        const doctorEntry = this.workSchedules.employee_list.find(
          emp => emp.id_employee === doctorId
        )
        
        if (doctorEntry?.employee_workschedule_list?.length) {
          return doctorEntry.employee_workschedule_list
        }
      }
      
      // Then check common schedules
      if (this.commonWorkSchedules?.data?.employee_list) {
        const doctorEntry = this.commonWorkSchedules.data.employee_list.find(
          emp => emp.id_employee === doctorId
        )
        
        if (doctorEntry?.employee_workschedule_list?.length) {
          return doctorEntry.employee_workschedule_list
        }
      }
      
      return []
    },

    /**
     * Fetch scheduling data from the unified API
     */
    async fetchSchedulingData(params: { 
      clinic_id: number
      period_type: 'weekly' | 'monthly'
      start_date: string
      end_date?: string
      booking_item_id?: number
    }) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.GET,
          '/mst/scheduling',
          params
        )
        
        if (response) {
          // Store the actual scheduling data from the nested structure
          this.schedulingData = response
          if (params.period_type === 'weekly') {
            this.weeklySchedulingData = response.business_days || []
          } else if (params.period_type === 'monthly') {
            this.monthlySchedulingData = response.monthly_days || []
          }
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('シフトスケジュールの取得に失敗しました。')
        console.error('Failed to fetch scheduling data:', error)
      }
      return null
    },

    /**
     * Alternative method to fetch scheduling data using POST
     * For more complex queries that may exceed URL limits
     */
    async fetchSchedulingDataPost(params: { 
      clinic_id: number
      period_type: 'weekly' | 'monthly'
      start_date: string
      end_date?: string
      booking_item_id?: number
    }) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST,
          '/mst/scheduling',
          params
        )

        if (response) {
          // Store the actual scheduling data from the nested structure
          this.schedulingData = response
          if (params.period_type === 'weekly') {
            this.weeklySchedulingData = response.business_days || []
          } else if (params.period_type === 'monthly') {
            this.monthlySchedulingData = response.monthly_days || []
          }
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('シフトスケジュールの取得に失敗しました。')
        console.error('Failed to fetch scheduling data:', error)
      }
      return null
    },

    /**
     * Helper method to get special days data
     * @returns {Array} Array of special days
     */
    getSpecialDays() {
      if (!this.schedulingData || !this.schedulingData.special_days) {
        return []
      }
      return this.schedulingData.special_days || []
    },

    /**
     * Helper method to get formatted weekly scheduling data
     * @returns {WeeklyScheduleDay[]} Array of weekly schedule days
     */
    getWeeklySchedulingData() {
      if (!this.schedulingData || this.schedulingData.meta?.period_type !== 'weekly') {
        return []
      }
      return this.schedulingData.business_days || []
    },

    /**
     * Helper method to get formatted monthly scheduling data
     * @returns {MonthlyScheduleDay[]} Array of monthly schedule days
     */
    getMonthlySchedulingData() {
      if (!this.schedulingData || this.schedulingData.meta?.period_type !== 'monthly') {
        return []
      }
      return this.schedulingData.monthly_days || []
    },

    /**
     * Fetch work schedule PDF data
     * @param params Object with id_clinic, start_date, and end_date
     */
    async fetchWorkSchedulePdfData(params: { 
      id_clinic: number
      start_date: string
      end_date: string
    }) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.GET,
          '/mst/work-schedule-pdf',
          params
        )
        
        if (response) {
          this.workSchedulePdfData = response
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('スケジュールPDFデータの取得に失敗しました。')
        console.error('Failed to fetch work schedule PDF data:', error)
      }
      return null
    },

    /**
     * Generate and download work schedule PDF
     * @param data PDF data from fetchWorkSchedulePdfData
     * @param fileName Name of the PDF file to download
     */
    async generateWorkSchedulePdf(data: any, fileName: string = 'work-schedule.pdf') {
      try {
        // Import the PDF component dynamically to avoid circular dependencies
        const GetPdfWorkSchedule = (await import('@/pages/employeeAvailability/components/GetPdfWorkSchedule.vue')).default
        
        // Show the PDF component in a modal
        const pdfModal = await mtUtils.smallPopup(GetPdfWorkSchedule, {
          pdfData: data,
          fileName
        })
        
        return pdfModal
      } catch (error) {
        mtUtils.autoCloseAlert('PDFの生成に失敗しました。')
        console.error('Failed to generate PDF:', error)
        return null
      }
    },

    /**
     * Fetch work schedule by ID
     * @param id Work schedule ID
     */
    async fetchWorkScheduleById(id: number) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.GET,
          `/mst/work-schedules/${id}`
        )
        
        if (response) {
          this.workScheduleDetail = response
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('シフトスケジュールの取得に失敗しました。')
        console.error('Failed to fetch work schedule:', error)
      }
      return null
    },
  }
})

export default useWorkScheduleStore 