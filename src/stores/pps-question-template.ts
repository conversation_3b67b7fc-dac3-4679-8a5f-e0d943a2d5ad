import { defineStore } from 'pinia'
import { api } from '@/boot/axios'

export interface DiseaseRelate {
  id_disease: number
  name_disease: string
  name_disease_en: string
}

// Type for template references that can be either an ID or a template object
export type TemplateReference =
  | number
  | null
  | {
      id_pps_qs_template: number
      name_button: string
      memo_explanation?: string
    }

export interface PPSQuestionTemplate {
  id_pps_qs_template: number
  id_disease_relate: number | null
  disease_relate?: DiseaseRelate
  id_pps_qs_template_pre: TemplateReference
  id_pps_qs_template_post: TemplateReference
  name_button: string
  memo_explanation: string
  flg_composition: boolean
  type_qs_composition: number | null
  flg_active: boolean
  display_order: number | null
  flg_delete: boolean
  flg_pps_available: boolean
  datetime_insert: string
  datetime_update: string | null
  id_clinic: number
  id_employee_insert: number
  id_employee_update: number | null
  questions_count: number
}

export interface PPSQuestion {
  id_pps_question?: number
  text_question: string
  memo_short?: string
  memo_extra?: string
  flg_start: boolean
  display_order: number
  id_clinic: number
  type_qs_option?: number | null
  choices: PPSQuestionChoice[]
}

export interface PPSQuestionChoice {
  id_pps_choice?: number
  text_choice: string
  text_short?: string
  id_pps_question_next: number | null
  display_order: number
  id_clinic: number
}

// Helper function to extract template ID from a TemplateReference
export const extractTemplateId = (value: TemplateReference): number | null => {
  if (value === null || value === undefined) {
    return null
  }

  if (typeof value === 'number') {
    return value
  }

  if (typeof value === 'object' && value.id_pps_qs_template) {
    return value.id_pps_qs_template
  }

  return null
}

export const usePPSQuestionTemplateStore = defineStore(
  'pps_question_template',
  {
    state: () => ({
      pps_question_templates: [] as PPSQuestionTemplate[],
      filtered_templates: [] as PPSQuestionTemplate[],
      selected_template: {} as PPSQuestionTemplate,
      template_questions: [] as PPSQuestion[],
      total_templates_count: 0,
      current_page: 1,
      next_page: null as string | null,
      previous_page: null as string | null,
      last_page: null as string | null,
      composition_types: [] as Array<{ label: string; value: number }>,
      qs_option_types: [] as Array<{ label: string; value: number }>,
      filters: {
        page: 1,
        page_size: 10,
        search: '',
        id_disease: null as number | null
      }
    }),

    getters: {
      getPPSQuestionTemplates: (state) => state.pps_question_templates,
      getFilteredTemplates: (state) => state.filtered_templates,
      getSelectedTemplate: (state) => state.selected_template,
      getTemplateQuestions: (state) => state.template_questions,
      getTotalTemplatesCount: (state) => state.total_templates_count,
      getCurrentPage: (state) => state.current_page,
      getNextPage: (state) => state.next_page,
      getPreviousPage: (state) => state.previous_page,
      getLastPage: (state) => state.last_page,
      getFilters: (state) => state.filters,
      getCompositionTypes: (state) => state.composition_types,
      getQsOptionTypes: (state) => state.qs_option_types
    },

    actions: {
      setSelectedTemplate(template: PPSQuestionTemplate) {
        this.selected_template = template
      },

      setFilters(filters: {
        page?: number
        page_size?: number
        search?: string
        id_disease?: number | null
        date_start?: string
        date_end?: string
      }) {
        this.filters = { ...this.filters, ...filters }
      },

      resetFilters() {
        this.filters = {
          page: 1,
          page_size: 10,
          search: '',
          id_disease: null
        }
      },

      selectTemplate(id: number | null = null) {
        this.selected_template = id
          ? this.pps_question_templates.find(
              (v) => v.id_pps_qs_template === id
            ) || ({} as PPSQuestionTemplate)
          : ({} as PPSQuestionTemplate)
      },

      filterTemplatesByDisease(id_disease: number | null) {
        if (this.pps_question_templates.length) {
          if (id_disease) {
            this.filtered_templates = this.pps_question_templates.filter(
              (template) => {
                if (template.disease_relate) {
                  return template.disease_relate.id_disease === id_disease
                }
                return template.id_disease_relate === id_disease
              }
            )
          } else {
            this.filtered_templates = this.pps_question_templates
          }
        }
      },

      fetchPPSQuestionTemplates() {
        return new Promise((resolve, reject) => {
          api
            .get('/pps_question_templates', { params: this.filters })
            .then((response) => {
              const processedData = response.data.data.map((template: any) => {
                if (
                  template.disease_relate &&
                  typeof template.id_disease_relate === 'number'
                ) {
                  return template
                } else if (
                  template.id_disease_relate &&
                  typeof template.id_disease_relate === 'object'
                ) {
                  return {
                    ...template,
                    disease_relate: template.id_disease_relate,
                    id_disease_relate: template.id_disease_relate.id_disease
                  }
                }
                return template
              })

              this.pps_question_templates = processedData
              this.filtered_templates = this.pps_question_templates
              this.total_templates_count = response.data.count || 0
              this.current_page = response.data.current || 1
              this.next_page = response.data.next
              this.previous_page = response.data.previous
              this.last_page = response.data.last

              if (this.pps_question_templates.length > 0) {
                this.selected_template = this.pps_question_templates[0]
              }

              resolve(response)
            })
            .catch((error) => {
              console.error('Error fetching PPS question templates:', error)
              reject(error)
            })
        })
      },

      fetchPPSQuestionTemplateById(id: number) {
        return new Promise((resolve, reject) => {
          api
            .get(`/pps_question_templates/${id}`)
            .then((response) => {
              this.selected_template = response.data.data
              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error fetching PPS question template with id ${id}:`,
                error
              )
              reject(error)
            })
        })
      },

      createPPSQuestionTemplate(data: Partial<PPSQuestionTemplate>) {
        // Prepare the data for API submission by creating a new object
        const apiData: Record<string, any> = {}

        // Copy data to apiData, transforming objects to IDs where needed
        Object.entries(data).forEach(([key, value]) => {
          if (
            key === 'id_disease_relate' &&
            value &&
            typeof value === 'object'
          ) {
            // Convert DiseaseRelate object to its ID
            apiData[key] = (value as DiseaseRelate).id_disease
          } else if (
            (key === 'id_pps_qs_template_pre' ||
              key === 'id_pps_qs_template_post') &&
            value !== null
          ) {
            // Extract ID from TemplateReference
            apiData[key] = extractTemplateId(value as TemplateReference)
          } else {
            apiData[key] = value
          }
        })

        return new Promise((resolve, reject) => {
          api
            .post('/pps_question_templates', apiData)
            .then((response) => {
              // Refresh the list after creating a new template
              this.fetchPPSQuestionTemplates().then(() => {
                resolve(response)
              })
            })
            .catch((error) => {
              console.error('Error creating PPS question template:', error)
              reject(error)
            })
        })
      },

      updatePPSQuestionTemplate(
        id: number,
        data: Partial<PPSQuestionTemplate>
      ) {
        // Prepare the data for API submission by creating a new object
        const apiData: Record<string, any> = {}

        // Copy data to apiData, transforming objects to IDs where needed
        Object.entries(data).forEach(([key, value]) => {
          if (
            key === 'id_disease_relate' &&
            value &&
            typeof value === 'object'
          ) {
            // Convert DiseaseRelate object to its ID
            apiData[key] = (value as DiseaseRelate).id_disease
          } else if (
            (key === 'id_pps_qs_template_pre' ||
              key === 'id_pps_qs_template_post') &&
            value !== null
          ) {
            // Extract ID from TemplateReference
            apiData[key] = extractTemplateId(value as TemplateReference)
          } else {
            apiData[key] = value
          }
        })

        return new Promise((resolve, reject) => {
          api
            .put(`/pps_question_templates/${id}`, apiData)
            .then((response) => {
              // Update the template in the local state
              const index = this.pps_question_templates.findIndex(
                (template) => template.id_pps_qs_template === id
              )
              if (index !== -1) {
                this.pps_question_templates[index] = {
                  ...this.pps_question_templates[index],
                  ...response.data.data
                }
                this.filtered_templates = [...this.pps_question_templates]
                this.selected_template = response.data.data
              }
              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error updating PPS question template with id ${id}:`,
                error
              )
              reject(error)
            })
        })
      },

      deletePPSQuestionTemplate(id: number) {
        return new Promise((resolve, reject) => {
          api
            .delete(`/pps_question_templates/${id}`)
            .then((response) => {
              // Remove the template from the local state
              this.pps_question_templates = this.pps_question_templates.filter(
                (template) => template.id_pps_qs_template !== id
              )
              this.filtered_templates = this.filtered_templates.filter(
                (template) => template.id_pps_qs_template !== id
              )

              // Reset selected template if it was the deleted one
              if (this.selected_template.id_pps_qs_template === id) {
                this.selected_template =
                  this.pps_question_templates[0] || ({} as PPSQuestionTemplate)
              }

              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error deleting PPS question template with id ${id}:`,
                error
              )
              reject(error)
            })
        })
      },

      toggleTemplateActive(id: number, active: boolean) {
        return this.updatePPSQuestionTemplate(id, { flg_active: active })
      },

      loadMoreTemplates() {
        if (this.next_page) {
          this.filters.page += 1
          return this.fetchPPSQuestionTemplates()
        }
        return Promise.resolve(null)
      },

      // New actions for complete template API
      fetchCompleteTemplate(id: number) {
        return new Promise((resolve, reject) => {
          api
            .get(`/pps_complete_template/${id}`)
            .then((response) => {
              if (response?.data?.data) {
                const templateData = response.data.data
                this.selected_template = {
                  id_pps_qs_template: templateData.id_pps_qs_template,
                  id_disease_relate: templateData.id_disease_relate,
                  id_pps_qs_template_pre: templateData.id_pps_qs_template_pre,
                  id_pps_qs_template_post: templateData.id_pps_qs_template_post,
                  name_button: templateData.name_button,
                  memo_explanation: templateData.memo_explanation,
                  flg_composition: templateData.flg_composition,
                  type_qs_composition: templateData.type_qs_composition,
                  flg_active: templateData.flg_active || false,
                  display_order: templateData.display_order,
                  flg_delete: templateData.flg_delete || false,
                  flg_pps_available: templateData.flg_pps_available || false,
                  datetime_insert: templateData.datetime_insert || '',
                  datetime_update: templateData.datetime_update || null,
                  id_clinic: templateData.id_clinic,
                  id_employee_insert: templateData.id_employee_insert || 0,
                  id_employee_update: templateData.id_employee_update || null,
                  questions_count: templateData.questions?.length || 0
                }

                this.template_questions = templateData.questions || []
              }
              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error fetching complete template with id ${id}:`,
                error
              )
              reject(error)
            })
        })
      },

      saveCompleteTemplate(data: {
        id_pps_qs_template?: number
        id_clinic: number
        questions: PPSQuestion[]
      }) {
        // Determine if we're creating or updating
        const isUpdate = data.id_pps_qs_template !== undefined
        const url = isUpdate
          ? `/pps_complete_template/${data.id_pps_qs_template}`
          : '/pps_complete_template'

        // Make a copy of data to avoid modifying the original
        const apiData = { ...data }

        return new Promise((resolve, reject) => {
          const apiMethod = isUpdate ? api.put : api.post

          apiMethod(url, apiData)
            .then((response) => {
              // Refresh the list after saving
              this.fetchPPSQuestionTemplates().then(() => {
                if (response?.data?.data) {
                  // Update local state with the new data
                  const templateData = response.data.data
                  if (isUpdate) {
                    // Find and update the template in the local arrays
                    const index = this.pps_question_templates.findIndex(
                      (t) => t.id_pps_qs_template === data.id_pps_qs_template
                    )
                    if (index !== -1) {
                      this.pps_question_templates[index] = {
                        ...this.pps_question_templates[index],
                        ...templateData
                      }
                    }
                  }

                  // Update selected template and questions
                  this.selected_template = templateData
                  this.template_questions = templateData.questions || []
                }
                resolve(response)
              })
            })
            .catch((error) => {
              console.error('Error saving complete template:', error)
              reject(error)
            })
        })
      },

      // New actions for deleting individual questions and choices
      deleteQuestion(template_id: number, question_id: number) {
        return new Promise((resolve, reject) => {
          api
            .delete(
              `/pps_question_templates/${template_id}/questions/${question_id}`
            )
            .then((response) => {
              // Remove the question from template_questions state
              this.template_questions = this.template_questions.filter(
                (question) => question.id_pps_question !== question_id
              )

              // Update questions count in selected template
              if (this.selected_template.id_pps_qs_template === template_id) {
                this.selected_template.questions_count =
                  this.template_questions.length
              }

              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error deleting question with id ${question_id} from template ${template_id}:`,
                error
              )
              reject(error)
            })
        })
      },

      deleteChoice(
        template_id: number,
        question_id: number,
        choice_id: number
      ) {
        return new Promise((resolve, reject) => {
          api
            .delete(
              `/pps_question_templates/${template_id}/questions/${question_id}/choices/${choice_id}`
            )
            .then((response) => {
              // Find the question and remove the choice from it
              const questionIndex = this.template_questions.findIndex(
                (question) => question.id_pps_question === question_id
              )

              if (questionIndex !== -1) {
                // Remove the choice from the question's choices
                this.template_questions[questionIndex].choices =
                  this.template_questions[questionIndex].choices.filter(
                    (choice) => choice.id_pps_choice !== choice_id
                  )
              }

              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error deleting choice with id ${choice_id} from question ${question_id} in template ${template_id}:`,
                error
              )
              reject(error)
            })
        })
      },

      // New method to add a question to a template
      addQuestion(template_id: number, questionData: Partial<PPSQuestion>) {
        return new Promise((resolve, reject) => {
          // Ensure we have defaults for all required fields
          const apiData = {
            text_question: questionData.text_question || '',
            memo_short: questionData.memo_short || '',
            flg_start: questionData.flg_start || false,
            display_order: questionData.display_order || 1,
            id_clinic: questionData.id_clinic || 2, // Default clinic ID
            type_qs_option: questionData.type_qs_option || null
          }

          api
            .post(`/pps_question_templates/${template_id}/questions`, apiData)
            .then((response) => {
              // Add the new question to the template_questions state
              if (response?.data?.data) {
                const newQuestion = response.data.data
                this.template_questions.push(newQuestion)

                // Update questions count in selected template
                if (this.selected_template.id_pps_qs_template === template_id) {
                  this.selected_template.questions_count =
                    this.template_questions.length
                }
              }

              resolve(response)
            })
            .catch((error) => {
              console.error(
                `Error adding new question to template ${template_id}:`,
                error
              )
              reject(error)
            })
        })
      },

      // Fetch composition types from API
      fetchQsCompositionTypes() {
        return new Promise((resolve, reject) => {
          api
            .get('/enums/type_qs_composition')
            .then((response) => {
              if (response?.data?.data) {
                // Transform the API response into the expected format
                // API response format: { "1": "単独 (SINGLE_TEMPLATE)", "2": "前・後を加える (COMBINE_PRE_POST)", ... }
                this.composition_types = Object.entries(response.data.data).map(
                  ([value, label]) => ({
                    label: label as string,
                    value: parseInt(value)
                  })
                )
              }
              resolve(response)
            })
            .catch((error) => {
              console.error('Error fetching composition types:', error)
              reject(error)
            })
        })
      },

      fetchQsOptionTypes() {
        return new Promise((resolve, reject) => {
          api
            .get('/enums/type_qs_option')
            .then((response) => {
              if (response?.data?.data) {
                this.qs_option_types = Object.entries(response.data.data).map(
                  ([value, label]) => ({
                    label: label as string,
                    value: parseInt(value)
                  })
                )
              }
              resolve(response)
            })
            .catch((error) => {
              console.error('Error fetching QS option types:', error)
              reject(error)
            })
        })
      },

      // New method to fetch interview preview
      fetchInterviewPreview(id: number) {
        return new Promise((resolve, reject) => {
          api
            .get(`/pps_complete_template/${id}/interview-preview`)
            .then((response) => {
              resolve(response.data)
            })
            .catch((error) => {
              console.error(
                `Error fetching interview preview for template ${id}:`,
                error
              )
              reject(error)
            })
        })
      }
    }
  }
)

export default usePPSQuestionTemplateStore
