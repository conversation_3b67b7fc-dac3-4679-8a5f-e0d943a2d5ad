import { defineStore } from 'pinia'
import { api } from '@/boot/axios'

export type ItemServiceSearchType = {
  name_item_service: string
  name_short: string
  name_service_item_unit: string
  type_medicine_category: number
  type_medicine_regulation: number
  id_manufacturer: number
  type_medicine_species: number
}

export enum Overwrites {
  NEW = 1,
  FULL_OVERWRITE = 99,
  PARTIAL_OVERWRITE = 55
}

export const vettyMasterImageBaseUrl = 'https://vcm-api.vetty.clinic/assets/'

export const useVettyMasterStore = defineStore('vetty-master', {
  state: () => ({
    itemServices: [],
    itemCmdServices: []
  }),
  getters: {
    getItemServices: (state) => state.itemServices,
    getItemCmdServices: (state) => state.itemCmdServices
  },
  actions: {
    fetchCmdItemServiceList(data: ItemServiceSearchType) {
      return new Promise((resolve, reject) => {
        api.get('mst/SearchCMDItemServiceList', { params: data })
        .then((res) => {
          this.itemCmdServices = res.data.data
          resolve(res.data.data)
        })
        .catch((err) => {
          reject(err)
        })
      })
    },
    getItemServiceByCode(codeMstIs: string) {
      return new Promise((resolve, reject) => {
        api.get(`mst/get-item-service-by-mst-code/${codeMstIs}`)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
      })
    },
    importMedicines(data: any) {
      return new Promise((resolve, reject) => {
        api.post('mst/import-medicine', data)
        .then((res) => {
          resolve(res.data)
        })
        .catch((err) => {
          reject(err)
        })
      })
    }
  }
})

export default useVettyMasterStore