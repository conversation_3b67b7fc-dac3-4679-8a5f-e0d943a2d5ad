import { defineStore } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import { 
  EmployeeWorkSchedule, 
  BusinessHourSlot as WorkScheduleBusinessHourSlot
} from './work-schedules'

// Type definitions based on PRD

// Define interface for business hour slot
export interface BusinessHourSlot {
  id_business_hour_slot: number
  name_business_hour: string
  label?: string
  value?: number
  [key: string]: any // For other properties that might exist
}

// Define interface for business day from API
export interface BusinessDay {
  id_business_day: number
  type_weekday: number
  id_business_hour_slot: number
  id_clinic: number
  flg_delete: boolean
  [key: string]: any
}

// Define interface for the days array items
export interface DayItem {
  id: number
  name: string
  checked: boolean
  type_weekday: number
  id_business_day: number | null
}

export type BookableEmployee = {
  id_booking_item_employee?: number
  id_employee_book: number
  id_employee_book_id?: number
  flg_booking_available_employee: number | boolean
  flg_workschedule_considered: number | boolean
  flg_workschedule_per_item_considered: number | boolean
  employee_workschedule_list?: EmployeeWorkSchedule[]
}

export type BookingDaySlot = {
  id_booking_day_slot?: number
  type_weekday: number
  date_booking_special?: string | null
  time_bookable_start: string
  time_bookable_end: string
  slot_max?: number | null
  flg_delete?: boolean | number
  datetime_insert?: string
  datetime_update?: string | null
  id_clinic_id?: number
  id_employee_insert_id?: number
  id_employee_update_id?: number | null
  id_booking_item_id?: number
  [key: string]: any // For other properties
}

export type Term = {
  id_term?: number
  type_reserve?: number
  title_term?: string
  memo_term?: string
  flg_available?: boolean
  display_order?: number
  flg_delete?: boolean
  datetime_insert?: string
  datetime_update?: string
  id_employee_insert?: number
  id_employee_update?: number
}

export type BookingItem = {
  id_booking_item?: number
  id_clinic: number
  id_item_service: number
  type_booking_method: number | null
  id_terms_id?: number | null
  flg_business_hour_ignored: number | boolean | null
  flg_booking_available_item: number | boolean | null
  flg_book_per_employee: number | boolean | null
  days_bookable_after: number | null
  days_bookable_before: number | null
  hours_bookable_before?: number | null
  label_unavailable?: string | null
  id_business_hour_slot?: number | string | null
  interval_min?: number | null
  time_business_start?: string | null
  time_business_end?: string | null
  flg_additional_questions: number | boolean | null
  flg_inapp_purchase: number | boolean | null
  flg_open_message: number | boolean | null
  date_apply_bgn?: string | null
  date_apply_end?: string | null
  json_additional_questions?:
    | Array<{
        key: string
        value: string
      }>
    | string
  json_booking_item?:
    | {
        additional_info: Array<{
          key: string
          value: any
        }>
      }
    | string
  bookable_employee_list?: BookableEmployee[]
  booking_day_slot_list?: {
    common_slots?: BookingDaySlot[]
    monday_slots?: BookingDaySlot[]
    tuesday_slots?: BookingDaySlot[]
    wednesday_slots?: BookingDaySlot[]
    thursday_slots?: BookingDaySlot[]
    friday_slots?: BookingDaySlot[]
    saturday_slots?: BookingDaySlot[]
    sunday_slots?: BookingDaySlot[]
    special_day_list?: BookingDaySlot[]
  }
  term?: Term
  [key: string]: any // For other properties
}

export type ItemService = {
  // Item service details from m_item_service + unit + price
  id_item_service: number
  name_item_service: string
  price: number
  // Add other item service fields as needed
}

export type BookingItemResponse = {
  item_service: ItemService
  booking_item: BookingItem
  bookable_employee_list: {
    booking_item_employee: BookableEmployee
    employee_workschedule_list: EmployeeWorkSchedule[]
  }[]
  booking_day_slot_list: {
    common_slots: BookingDaySlot[]
    monday_slots: BookingDaySlot[]
    tuesday_slots: BookingDaySlot[]
    wednesday_slots: BookingDaySlot[]
    thursday_slots: BookingDaySlot[]
    friday_slots: BookingDaySlot[]
    saturday_slots: BookingDaySlot[]
    sunday_slots: BookingDaySlot[]
    special_day_list: BookingDaySlot[]
  }
}

export const useBookingItemStore = defineStore('bookingItems', {
  state: () => ({
    bookingItems: [] as BookingItem[],
    currentBookingItem: null as BookingItemResponse | null,
    recentBookingItem: null as BookingItem | null,
    bookableItemService: [],
    singleBookingItem: null as BookingItem | null
  }),

  getters: {
    getBookingItems: (state) => state.bookingItems,
    getCurrentBookingItem: (state) => state.currentBookingItem,
    getRecentBookingItem: (state) => state.recentBookingItem,
    getBookableItemService: (state) => state.bookableItemService,
  },
  
  actions: {
    async fetchBookingItems(data: { id_clinic?: number } = {}) {
      try {
        const response = await mtUtils.callApi(selectOptions.reqMethod.GET, '/mst/booking/items', data)
        if (response) {
          this.bookingItems = response.data || []
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch booking items. Please try again later.')
      }
    },

    async fetchBookingItem(id_booking_item: number) {
      try {
        const response = await mtUtils.callApi(selectOptions.reqMethod.GET, `/mst/booking/items/${id_booking_item}`)
        if (response && response.data) {
          this.currentBookingItem = response.data
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch booking item details. Please try again later.')
      }
    },

    async fetchBookingItemByFilter(filter: { id_clinic?: number; id_item_service?: number }) {
      try {
        const response = await mtUtils.callApi(selectOptions.reqMethod.GET, '/mst/booking/items/filter', filter)
        if (response) {
          this.currentBookingItem = response[0] || null
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch booking items. Please try again later.')
      }
    },

    async fetchBookingItemByType(filter: { id_clinic?: number; id_item_service?: number }) {
      try {
        const response = await mtUtils.callApi(selectOptions.reqMethod.GET, '/mst/booking/items/type', filter)
        console.log(response)
        if (response) {
          this.bookableItemService = response || null
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to fetch booking items. Please try again later.')
      }
    },

    async createBookingItem(bookingItemData: BookingItem) {
      try {
        const response = await mtUtils.callApi(selectOptions.reqMethod.POST, '/mst/booking/items', bookingItemData)
        if (response) {
          this.recentBookingItem = response
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to create booking item. Please try again later.')
      }
      return null
    },

    async updateBookingItem(id_booking_item: number, bookingItemData: BookingItem) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.PUT,
          `/mst/booking/items/${id_booking_item}`,
          bookingItemData
        )
        if (response) {
          this.recentBookingItem = response
          // Update in the list if it exists there
          const index = this.bookingItems.findIndex((item) => item.id_booking_item === id_booking_item)
          if (index !== -1) {
            this.bookingItems[index] = response
          }
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to update booking item. Please try again later.')
      }
    },

    /**
     * Update booking day slots for a specific booking item
     * This is a dedicated endpoint as per PRD section 5.5.1
     */
    async updateBookingDaySlots(id_booking_item: number, bookingDaySlotData: { booking_day_slot_list: any }) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.PUT,
          `/mst/booking/day-slots/${id_booking_item}`,
          bookingDaySlotData
        )
        if (response) {
          // Update the current booking item if it matches
          if (this.currentBookingItem?.booking_item.id_booking_item === id_booking_item) {
            this.currentBookingItem.booking_day_slot_list = response.booking_day_slot_list
          }
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to update booking day slots. Please try again later.')
      }
      return null
    },

    /**
     * Create booking day slots
     * This is a dedicated endpoint as per PRD section 5.5.1
     */
    async createBookingDaySlots(bookingDaySlotData: { id_booking_item: number; booking_day_slot_list: any }) {
      try {
        const response = await mtUtils.callApi(
          selectOptions.reqMethod.POST,
          `/mst/booking/day-slots`,
          bookingDaySlotData
        )
        if (response) {
          // Update the current booking item if it matches
          if (this.currentBookingItem?.booking_item.id_booking_item === bookingDaySlotData.id_booking_item) {
            this.currentBookingItem.booking_day_slot_list = response.booking_day_slot_list
          }
          return response
        }
      } catch (error) {
        mtUtils.autoCloseAlert('Failed to create booking day slots. Please try again later.')
      }
      return null
    },

    // Add new method to delete booking day slots by filter
    async deleteBookingDaySlotsByFilter(params: {
      type_weekday: number
      id_booking_item?: number
      id_clinic?: number
      date_booking_special?: string
    }) {
      try {
        // Build query string from params
        const queryParams = new URLSearchParams()
        if (params.type_weekday) queryParams.append('type_weekday', params.type_weekday.toString())
        if (params.id_booking_item) queryParams.append('id_booking_item', params.id_booking_item.toString())
        if (params.id_clinic) queryParams.append('id_clinic', params.id_clinic.toString())
        if (params.date_booking_special) queryParams.append('date_booking_special', params.date_booking_special)

        const queryString = queryParams.toString()

        const response = await mtUtils.callApi(selectOptions.reqMethod.DELETE, `/mst/booking/day-slots?${queryString}`)

        return response
      } catch (error) {
        mtUtils.autoCloseAlert('予約時間枠の削除に失敗しました。')
        console.error(error)
        return null
      }
    }
  }
})

export default useBookingItemStore
