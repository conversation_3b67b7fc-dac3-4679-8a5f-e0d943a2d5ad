<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import OptionModal from '@/components/OptionModal.vue'
import UploadFile from '@/components/UploadFile.vue'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import { typeEmpInfo, typeDisplay, typeEmpInfoRead } from '@/utils/enum'
import {
  changeToggleDropdownNames,
  updateBtnLabel,
  getDateTimeNow,
  aahUtilsGetEmployeeName
} from '@/utils/aahUtils'
import mtUtils from '@/utils/mtUtils'
import { imageResize } from '@/utils/helper'
import useCommonStore from '@/stores/common'
import useEmpInfoStore from '@/stores/empInfo'
import useEmployeeStore from '@/stores/employees'
import { CliCommon, empInfoType } from '@/types/types'
import { groupBy } from 'lodash'
import useCliCommonStore from '@/stores/cli-common'
import { storeToRefs } from 'pinia'
import { useCharacterCount } from '@/utils/useCharCount'

const empInfoStore = useEmpInfoStore()
const employeeStore = useEmployeeStore()
const cliCommonStore = useCliCommonStore()
const { getEmpInfo } = storeToRefs(empInfoStore)

const emits = defineEmits(['close'])
const closeModal = () => {
  // Reset store state
  empInfoStore.resetEmpInfo()

  // Reset all form data to initial state
  resetFormData()

  emits('close')
}

const resetFormData = () => {
  // Reset main form data
  empInfoData.value = {
    id_emp_info: null,
    title: null,
    type_emp_info: 1,
    type_display: 3,
    type_department: null,
    type_emp_info_read: 1,
    id_employee_posted: defaultEmployee,
    datetime_posted: getDateTimeNow(),
    memo_emp_info: '',
    id_employee: [],
  }

  // Reset other reactive state
  selectedFile.value = null
  isEdit.value = false
  empInfoReadList.value = []
  statusButton.value = 0
  departmentToggleError.value = false

  // Reset file upload component by incrementing key
  fileUploadKey.value++

  // Note: typeDepartmentList is not reset as it's populated from store data
}

const props = defineProps<{
  data: empInfoType,
  searchData: Function,
  reinitDashoard: Function
}>()

const typeDepartmentList = ref([])
const targetRef = ref(), selectedFile = ref(null), isEdit = ref(false), empInfoReadList = ref([])
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))
const employeeList = ref([...employeeStore.getAllEmployees])
const fileUploadKey = ref<number>(0)
let observer = null // it will watch DOM changes
// const showEmailIcon = ref(false)

const empInfoData = ref({
  id_emp_info: null,
  title: null,
  type_emp_info: 1,
  type_display: 3,
  type_department: null,
  type_emp_info_read: 1,
  id_employee_posted: defaultEmployee,
  datetime_posted: getDateTimeNow(),
  memo_emp_info: '',
  id_employee: [],
})

const departmentToggleError = ref(false)

const variable = ref(), foreColor = ref('#ffff00')
const statusButton = ref(0)
const memoRef = computed({
  get: () => empInfoData.value.memo_emp_info,
  set: (val: string) => { empInfoData.value.memo_emp_info = val }
});

const maxChars = 500;
const memoValidation = useCharacterCount(memoRef, maxChars, '最大500文字をご入力できます')
const isCharCountInvalid = computed(() => !memoValidation.isCharCountValid.value)
const submit = async () => {
  // set parameter type_display based on statusButton value
  if (statusButton.value == 1) {
    empInfoData.value.type_display = 1
  }
  else if (statusButton.value == 2) {
    // set parameter type_display based on statusButton value
    if (empInfoData.value.type_emp_info_read === 3) {
      if (!empInfoData.value.type_department) {
        departmentToggleError.value = true
        return
      }
    }
    empInfoData.value.type_display = 3
  }
  else if (statusButton.value == 3) {
    // set parameter type_display based on statusButton value
    if (empInfoData.value.type_emp_info_read === 3) {
      if (!empInfoData.value.type_department) {
        departmentToggleError.value = true
        return
      }
    }
    // empInfoData.value.type_display = 3
    empInfoData.value.flg_mail_send = 1
  }
  else if (statusButton.value == 4) {
    empInfoData.value.type_display = 2
  }

  if(empInfoData.value.datetime_posted){
    empInfoData.value.datetime_posted = empInfoData.value.datetime_posted.replace(/\//g, '-')
    if(empInfoData.value.datetime_posted.length <= 10) empInfoData.value.datetime_posted += `${getDateTimeNow().slice(10)}`
  }
  // TODO; will remove later
  if(props.data?.id_emp_info) {
    empInfoData.value.type_department = 1
    empInfoData.value.type_emp_info_read = 1
  }
  delete empInfoData.value.file_url
  let formData = new FormData()
  for(const [key, value] of Object.entries(empInfoData.value)) {
    formData.append(key, value)
  }
  if(selectedFile.value) formData.append('file_path1', selectedFile.value)
  if(props.data && props.data?.id_emp_info) {
    await empInfoStore.updateEmpInfo(props.data.id_emp_info, formData)
    mtUtils.autoCloseAlert(aahMessages.success)
    props.searchData()
    closeModal()
  }
  else {
    await empInfoStore.submitEmpInfo(formData)
    mtUtils.autoCloseAlert(aahMessages.success)
    if (props.searchData && typeof props.searchData === 'function') props.searchData()
    if (props.reinitDashoard && typeof props.reinitDashoard === 'function') props.reinitDashoard()
    closeModal()
  }
  statusButton.value = 0
}

const assignPageData = (data: empInfoType) => {
  for(const key in empInfoData.value) {
    empInfoData.value[key] = data[key]
  }
  if(data.file_path1) {
    empInfoData.value.file_url = data.file_url
  }
  if(data.emp_info_read && data.emp_info_read.length > 0) {
    empInfoReadList.value = groupBy(data.emp_info_read, 'flg_read')
  }
}

const colorClicked = () => {
  const edit = targetRef.value
  edit.runCmd('foreColor', foreColor.value)
  edit.focus()
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
  let selectedOption = menuOptions.find((i) => i.isChanged == true)
  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm('本当に削除しますか？', '確認')
        .then((confirmation) => {
          if (confirmation) {
            empInfoStore.destroyEmpInfo(empInfoData.value.id_emp_info).then(() => {
              props.searchData()
              closeModal()
              mtUtils.autoCloseAlert(aahMessages.success)
            })
          }
        })
    }
  }
}

const sendEmail = async () => {
  await mtUtils.confirm("", "対象ユーザーにメールを送信しますか?", "送信する").then(async (confirmation) => {
    if(confirmation == true){
      const resp = await empInfoStore.sendEmail(empInfoData.value.id_emp_info)
      if(resp.message == "EMAIL_SENT_SUCCESSFULLY"){
        mtUtils.autoCloseAlert('メールを送信しました！')
      }else if(resp.message == "EMPLOYEES_EMAILS_NOT_FOUND"){
        mtUtils.autoCloseAlert('送信先メールアドレスが見つかりません')
      }
    }    
  })
}

const employeesWithDisplayOrder = computed(() => {
  // 1. For efficiency, create a lookup map from the employee store.
  const employeeDetailsMap = new Map(
    employeeStore.getEmployees.map(emp => [emp.id_employee, emp])
  );

  // 2. Map over your primary list and then sort the result.
  return getEmpInfo.value.emp_info_read
    .map(employee => {
      // Find the corresponding full details from the map
      const details = employeeDetailsMap.get(employee.id_employee);

      // Return a new object with all original properties plus the new one.
      return {
        ...employee, // Copy all original properties from the employee object
        display_order: details ? details.display_order : null, // Add display_order, use null as a fallback
        name_display: details ? details.name_display : null,
        image_path1: details ? details.image_path1 : null
      };
    })
    .sort((a, b) => {
      // 3. Sort the array by `display_order`.
      // This handles cases where display_order might be null or undefined.
      if (a.display_order === null || a.display_order === undefined) return 1;
      if (b.display_order === null || b.display_order === undefined) return -1;
      return a.display_order - b.display_order;
    });
});

const showEmailIcon = computed(() => {
  return empInfoData.value.type_display == 3
})

const handleEmpInfoRead = (value: number) => {
  if(value !== 3) {
    empInfoData.value.type_department = null
  }
  if(value !== 4) {
    empInfoData.value.id_employee = []
  }
}

onMounted(async () => {
  // Reset all state data first before populating any data (create or update)
  empInfoStore.resetEmpInfo()
  resetFormData()

  if(cliCommonStore.getCliCommonTypeDepartmentList && cliCommonStore.getCliCommonTypeDepartmentList.length && cliCommonStore.getCliCommonTypeDepartmentList.length > 0) {
    typeDepartmentList.value = cliCommonStore.getCliCommonTypeDepartmentList.map((item: CliCommon) => {
      return {
        label: item.name_cli_common,
        value: item.code_func1
      }
    })
  }

  console.log('props.data.id_emp_info :>> ', props.data.id_emp_info);
  if (props.data && props.data?.id_emp_info) {
    isEdit.value = true
    assignPageData(props.data)
    ++fileUploadKey.value
  }
  console.log('getEmpInfo :>> ', getEmpInfo);
  // mutation oberserver for updating q-editor toolbar labels
  const observerCallback = (mutationList, observer) => {
    for (let mutation of mutationList) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach((node) => {
          if (
            node.nodeType === 1 &&
            (node.matches('[role="menu"]') ||
              node.matches('.q-editor__toolbar-group'))
          ) {
            changeToggleDropdownNames()
          }
        })
      }
    }
  }
  observer = new MutationObserver(observerCallback)
  observer.observe(document.body, { childList: true, subtree: true })
})
</script>

<template>
  <q-form @submit.prevent="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
        院内連絡
      </q-toolbar-title>
      <q-btn flat round v-if="showEmailIcon && isEdit" @click="sendEmail">
        <q-icon size="sm" name="email" />
      </q-btn>
      <q-btn flat round v-if="props.data && props.data?.id_emp_info" @click="openMenu">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
    </MtModalHeader>
    <q-card-section class="content q-px-xl">
        <div class="form-container">
          <!-- Section 1: 何を達成しますか？ (What to achieve?) -->
          <div class="form-section form-section-1">
            <div class="section-content">
              <div class="row q-col-gutter-md q-mb-md">
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="btn-toggle-departement col-12 mb-8px">
                  <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 10px'" v-model="empInfoData.type_emp_info" no-caps unelevated clearable
                    color="white" text-color="primary" label="対応部署 *" :options="typeEmpInfo">
                  </q-btn-toggle>
                </div>
                <div class="col-10" :class="{ 'col-12': !isEdit }">
                  <MtInputForm
                    type="text"
                    v-model="empInfoData.title"
                    label="タイトル *"
                    required
                    :rules="[aahValidations.validationRequired]"
                    tabindex="4"
                  />
                </div>
                <div v-if="isEdit" class="col-2">
                  <q-chip v-if="isEdit" style="background-color: #3C7AD6;" text-color="white" >
                    <span v-if="empInfoData.type_display === 1">下書き</span>
                    <span v-if="empInfoData.type_display === 2">非表示中</span>
                    <span v-if="empInfoData.type_display === 3">公開中</span>
                  </q-chip>
                </div>
              </div>
            </div>
          </div>

          <!-- Vertical Divider -->
          <q-separator vertical class="section-divider" />

          <!-- Section 2: 誰に質問しますか？ (Who to ask?) -->
          <div class="form-section form-section-2">
            <div class="section-content">
              <div  class="row q-col-gutter-md">
                <div v-if="!isEdit" class="col-12">
                <template v-if="!(isEdit)">
                  <div class="">
                    <div class="col-12 btn-toggle-customer">
                      <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 18px'" v-model="empInfoData.type_emp_info_read" no-caps unelevated class="outline-btn-toggle"
                      color="white" text-color="primary" :rules="[aahValidations.validationRequired]" :options="typeEmpInfoRead" @update:model-value="handleEmpInfoRead"
                      />
                    </div>
                  </div>
                  <div class="q-mt-md" v-if="empInfoData.type_emp_info_read === 3">
                    <div class="btn-toggle-departement mb-8px" :class="{ 'btn-toggle-departement-error': departmentToggleError }">
                      <q-btn-toggle :toggle-color="'#3C7AD6'" :size="'15px'" :padding="'0px 10px'" v-model="empInfoData.type_department" no-caps unelevated clearable
                        color="white" text-color="primary" label="対応部署 *" :rules="[aahValidations.validationRequired]" required :options="typeDepartmentList">
                      </q-btn-toggle>
                    </div>
                  </div>
                  <div v-if="empInfoData.type_emp_info_read === 4" class="row q-col-gutter-md q-mt-xs q-mb-lg">
                    <div class="col-6">
                      <MtFormMultipleSelection
                        v-model="empInfoData.id_employee"
                        :options="employeeList"
                        label="担当者"
                        :required="statusButton === 2 || statusButton === 3"
                        autofocus
                        tabindex="1"
                        :rules="statusButton === 2 || statusButton === 3 ? [aahValidations.validationSelection] : ''"
                      />
                    </div>
                  </div>
                </template>
                </div>
              </div>
              <div v-if="empInfoData.type_emp_info_read === 3 || empInfoData.type_emp_info_read === 4" class="q-mb-sm" />
              <div v-else class="q-mt-md"></div>
              <!-- When !isEdit: 1 row with each field col-6 -->
              <div v-if="!isEdit" class="row q-col-gutter-md q-mb-md">
                <div class="col-6">
                  <MtFormInputDate
                    v-model:date="empInfoData.datetime_posted"
                    label="作成日時"
                    type="date"
                  />
                </div>

                <div class="col-6">
                  <InputEmployeeOptGroup
                    v-model:selected="empInfoData.id_employee_posted"
                    department-selected=""
                    label="作成者"
                    show-select-default-employee
                    required
                    :rules="[aahValidations.validationSelection]"
                  />
                </div>
              </div>

              <!-- When isEdit: 2 rows with each field col-6 -->
              <div v-if="isEdit" class="q-mb-md">
                <div class="row q-col-gutter-md q-mb-md">
                  <div class="col-6">
                    <MtFormInputDate
                      v-model:date="empInfoData.datetime_posted"
                      label="作成日時"
                      type="date"
                    />
                  </div>
                </div>

                <div class="row q-col-gutter-md">
                  <div class="col-6">
                    <InputEmployeeOptGroup
                      v-model:selected="empInfoData.id_employee_posted"
                      department-selected=""
                      label="作成者"
                      show-select-default-employee
                      required
                      :rules="[aahValidations.validationSelection]"
                    />
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
    
    


      
    <div class="row q-col-gutter-md q-my-sm">
      <div class="col">
        <q-editor
        :toolbar="[
          ['left', 'center', 'right', 'justify'],
          ['bold', 'italic', 'strike', 'underline'],
          ['undo', 'redo'],
          ['token'],
          [
            {
              label: $q.lang.editor.formatting,
              icon: $q.iconSet.editor.formatting,
              list: 'no-icons',
              options: ['p', 'h2', 'h3', 'h4', 'h5']
            }
          ]
        ]"
        ref="targetRef"
        toolbar-bg="primary"
        toolbar-text-color="white"
        toolbar-toggle-color="accent-700"
        height="40vh"
        class="editor"
        v-model="empInfoData.memo_emp_info"
        @update:model-value="updateBtnLabel"
      >
        <template v-slot:token>
          <q-color
            @click="colorClicked()"
            v-model="foreColor"
            no-header
            no-footer
            default-view="palette"
            :palette="['#000000', '#FF0000', '#0000FF', '#008000', '#505050']"
            unelevated
            class="q-mt-sm bg-primary color-picker"
          />
        </template>
      </q-editor>
      <div class="q-mt-sm flex justify-end text-caption">
          <span :class="{ 'text-negative': isCharCountInvalid }">
            {{ memoValidation.charCount }}文字 / {{ maxChars }}文字
          </span>
      </div>
        
      <div v-if="isCharCountInvalid" class="text-negative text-caption custom-margin-bottom" role="alert" aria-live="assertive">
        {{ memoValidation.validationMessage.value.replace(/['"]+/g, '') }}
      </div>
      </div>
      <div class="col-2 uploaded-file">
        <div>
        <UploadFile
          :key="fileUploadKey"
          :rules="[aahValidations.validationRequired]"
          :fileUrl="empInfoData.file_url"
          @fileUploaded="(file: File) => selectedFile = file"
          @fileRemoved="() => {selectedFile = empInfoData.file_path1 = ''}"
        />
        </div>
      </div>
    </div>
    <div class="row q-col-gutter-md q-mt-xs">
      
    </div>
    <div v-if="getEmpInfo?.emp_info_read?.filter((data) => { return data.flg_read })?.length" class="row q-col-gutter-xl">
      <div class="col">
        <q-chip dense class="emp-chip-tosca" text-color="black">
          <div class="flex-1 justify-center text-center">確認済</div>
        </q-chip>
        <div class="flex items-center q-mt-xs">
          <span class="q-mb-xs flex items-center" v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return data.flg_read })" :key="idx"> 
            <img class="q-mr-xs emp-info-image" v-if="item.image_path1" :src="item.image_path1" />
            <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
            <span class="q-mr-md ">{{ item.name_display }} </span>
          </span>
        </div>
      </div>
    </div>

    <div v-if="getEmpInfo?.emp_info_read?.filter((data) => { return !data.flg_read })?.length" class="row q-mt-md">
      <div class="col">
        <q-chip dense class="bg-accent-500 emp-chip" text-color="black">
          <div class="flex-1 justify-center text-center">未読</div>
        </q-chip>
        <div class="flex items-center q-mt-xs">
          <span class="flex items-center q-mb-xs" v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return !data.flg_read })" :key="idx"> 
            <img class="q-mr-xs emp-info-image" v-if="item.image_path1" :src="item.image_path1" />
            <q-icon v-else name="account_circle" size="sm" class="text-grey-500 q-mr-xs " />
            <span class="q-mr-md ">{{ item.name_display }} </span>
          </span>
        </div>
      </div>
    </div>

    </q-card-section>
     <q-card-section class="q-bt bg-white">

      <div class="text-center modal-btn-emp flex justify-center">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>閉じる</span>
        </q-btn>
        <div class="separator-emp bg-grey-400 q-mx-sm"></div>
        <q-btn
          unelevated
          color="primary"
          tabindex="15"
          type="submit"
          :disabled="isCharCountInvalid"
          @click="statusButton = 1"
        >
          <span>下書き保存</span>
        </q-btn>
        <div class="separator-emp bg-grey-400 q-mx-sm"></div>
        <q-btn
          unelevated
          tabindex="16"
          color="primary"
          class="text-white"
          type="submit"
          :disabled="isCharCountInvalid"
          @click="statusButton = 2"
        >
          <span>公開</span>
        </q-btn>

        <q-btn
          unelevated
          tabindex="17"
          color="primary"
          class="text-white q-ml-md"
          type="submit"
          :disabled="isCharCountInvalid"
          width="70px"
          @click="statusButton = 3"
        >
          <span>公開＆送信</span>
        </q-btn>
        <div class="separator-emp bg-grey-400 q-mx-sm"></div>
         <q-btn
          unelevated
          color="primary"
          tabindex="18"
          type="submit"
          :disabled="isCharCountInvalid"
          @click="statusButton = 4"
        >
          <span>非表示</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>  
</template>
<style lang="scss" scoped>
.color-picker {
  max-width: 20px;
  box-shadow: none;
  border-radius: 0;
}
.editor {
  line-height: 1.7;
  :deep(.q-editor__toolbar-group) {
    &:last-child {
      margin-left: -90px;
    }
  }
}
.custom-margin-bottom {
  margin-top: -23px;
}


:deep(.btn-toggle-customer .q-btn-group > .q-btn-item) {
  border-radius: 5px;
  background-color: #3C7AD6;
  width: 113px;
}

:deep(.btn-toggle-customer .q-btn-group) {
  flex-wrap: wrap!important;
}

:deep(.mb-8px .q-btn-group > .q-btn-item) {
  margin-bottom: 8px;
}

:deep(.btn-toggle-departement .q-btn-group > .q-btn-item) {
  border-radius: 4px;
  flex-wrap: nowrap!important;
  white-space: nowrap!important;
  margin-right: 8px;
  background-color: #3C7AD6;
}

:deep(.btn-toggle-departement .q-btn-group) {
  flex-wrap: wrap!important;
}

:deep(.btn-toggle-departement .q-btn-group) {
  flex-wrap: wrap!important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="false"]) {
  border: 1px solid #9e9e9e !important;
}

// Default border for all buttons in the toggle
:deep(.btn-toggle-departement .q-btn[aria-pressed="true"]) {
  border: 1px solid #3C7AD6 !important;
}

// Red border when the q-btn-toggle is in an error state
:deep(.btn-toggle-departement-error .q-btn[aria-pressed="false"]) {
  border-color: red !important;
}

// Red border when the q-btn-toggle is in an error state
:deep(.btn-toggle-departement-selected .q-btn) {
  border: #3C7AD6 1px solid!important;
}

.outline-btn-toggle {
  outline: 1px solid #3C7AD6;
  padding: 2px;
}
// MODAL FOOTER BUTTON GLOBAL STYLES //
.modal-btn-emp {
  .q-btn {
    width: 160px;
    min-height: 38px;
    // width for mobile screens
    @media screen and (max-width: 800px) {
      width: 200px;
    }
    @media screen and (max-width: 500px) {
      width: 100%;
    }
  }
}
.separator-emp {
  height: 38px;
  width: 1px;
}
.emp-chip {
  margin-left: -2px;
  width: 76px;
}
.emp-chip-tosca {
  background-color: $tosca;
  margin-left: -2px;
  width: 76px;
}

// Form sections styling
.form-section {
  background-color: white;
  padding: 20px 20px 0 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;

  .section-header {
    flex-shrink: 0;
  }

  .section-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.form-container {
  display: flex;
  align-items: stretch;
  /* Make sure the container has enough space, e.g., 100% of its parent */
  width: 100%;
  position: relative;
}

.form-section-1 {
  /* Calculate: 60% of the total width, minus half of the gap */
  flex: 0 0 calc(50% - 10px); /* 10px is half of the 20px gap */
}

.form-section-2 {
  /* Calculate: 40% of the total width, minus half of the gap */
  flex: 0 0 calc(50% - 10px); /* 10px is half of the 20px gap */
}

// Vertical divider styling
.section-divider {
  height: auto;
  margin: 0;

  &.q-separator--vertical {
    width: 1px;
    background-color: #e0e0e0;
  }
}
</style>