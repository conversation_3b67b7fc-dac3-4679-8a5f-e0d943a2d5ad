<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, computed } from 'vue'
import { storeToRefs } from 'pinia'

// Directly Imported Components (from the `components` folder)
import MtModalHeader from '@/components/MtModalHeader.vue'
import OptionModal from '@/components/OptionModal.vue'

// Lazy-loaded Modals and Other Components
const UpdateEmpInfoModal = defineAsyncComponent(() => import('./UpdateEmpInfoModal.vue'))
const ZoomImageModal = defineAsyncComponent(() => import('../message/ZoomImageModal.vue'))
const EmployeeConfirmationModal = defineAsyncComponent(() => import('./EmployeeConfirmationModal.vue'))
import UploadFile from '@/components/UploadFile.vue'

// Store imports
import useEmpInfoStore from '@/stores/empInfo'
import useEmployeeStore from '@/stores/employees'
import { useDashboardStore } from '@/stores/dashboard'

// Utilities
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import { aahUtilsGetEmployeeName, convertLinkInMemo, dateFormat } from '@/utils/aahUtils'

// Types and Enums
import { empInfoType, EmployeeType } from '@/types/types'
import { typeEmpInfo, typeDisplay, typeEmpInfoRead } from '@/utils/enum'

// Props and Emits
const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

const props = defineProps<{
  data: empInfoType,
  attr: Object
}>()

// Stores
const empInfoStore = useEmpInfoStore()
const employeeStore = useEmployeeStore()
const dashboardStore = useDashboardStore()
const { getEmpInfo } = storeToRefs(empInfoStore)

// Reactive Data
const empInfoReadList = ref([])
const selectedEmployee = ref(null)
const markingAsRead = ref(false)

const empInfoData = ref({
  id_emp_info: null,
  title: null,
  type_emp_info: 1,
  type_display: 1,
  type_department: null,
  type_emp_info_read: null,
  id_employee_posted: null,
  datetime_posted: null,
  memo_emp_info: '',
  file_url: null
})

const assignPageData = (data: empInfoType) => {
  for(let key in empInfoData.value) {
    empInfoData.value[key] = data[key]
  }
  if(props.data && props.data.file_path1) {
    empInfoData.value.file_path1_name = getFileName(props.data.file_path1)
  }
  // if(props.data.read_list && props.data.read_list > 0) {
  //   empInfoReadList.value = groupBy(props.data.read_list, 'flg_read')
  // }
}

const getFileName = (filePath: string) => {
  const fileName = filePath.split('/')
  return fileName[fileName.length - 1]
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: '編集',
      name: 'edit',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    },
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
  let selectedOption = menuOptions.find((i) => i.isChanged == true)
  if (selectedOption) {
    if(selectedOption.name == 'edit') {
      mtUtils.popup(UpdateEmpInfoModal, {data: props.data})
    }
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm('本当に削除しますか？', '確認')
        .then((confirmation) => {
          if (confirmation) {
            empInfoStore.destroyEmpInfo(empInfoData.value.id_emp_info).then(() => {
              // Remove from dashboard emp_info_list immediately for real-time UI update
              dashboardStore.removeEmpInfoFromDashboard(empInfoData.value.id_emp_info)

              empInfoStore.fetchEmpInfos({type_display: 3})
              emits('close')
              mtUtils.autoCloseAlert(aahMessages.success)
            })
          }
        })
    }
  }
}

const getEmployeeName = (empId:number) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, String(empId))
}

const getEmployeeProfileImage = (empId:number) => {
  return employeeStore.getEmployees.find((emp) => emp.id_employee === empId)?.image_path1
}

const setRecordAsReaded = async () => {
  markingAsRead.value = true
  try {
    let data = {
      id_emp_info: empInfoData.value.id_emp_info,
      id_employee: JSON.parse(localStorage.getItem('id_employee')),
      flg_read: true
    }
    const res:any = await empInfoStore.updateEmpInfoRead(data)
    if(res) {
      props.attr.isConfirmed = true
      closeModal()
    }
  } finally {
    markingAsRead.value = false
  }
}

// Loading state for individual employees
const loadingEmployees = ref(new Set<number>())

// Update employee read status locally
const updateEmployeeReadStatus = (employeeId: number, newStatus: boolean) => {
  // Find the employee in the emp_info_read array and update their flg_read status
  const empInfoRead = getEmpInfo.value.emp_info_read
  if (empInfoRead) {
    const employeeIndex = empInfoRead.findIndex(emp => emp.id_employee === employeeId)
    if (employeeIndex !== -1) {
      empInfoRead[employeeIndex].flg_read = newStatus
    }
  }
}

// Handle employee click to toggle read/unread status directly
const handleEmployeeClick = async (employee: any) => {
  // Prevent multiple clicks while processing
  if (loadingEmployees.value.has(employee.id_employee)) {
    return
  }

  // Check if empInfoData is available
  if (!empInfoData.value?.id_emp_info) {
    mtUtils.autoCloseAlert('エラーが発生しました')
    return
  }

  try {
    // Add to loading state
    loadingEmployees.value.add(employee.id_employee)

    // Get current status and determine new status
    const currentStatus = employee.flg_read
    const newStatus = !currentStatus

    // Prepare API data
    const data = {
      id_emp_info: empInfoData.value.id_emp_info,
      id_employee: employee.id_employee,
      flg_read: newStatus
    }

    // Call API to update status
    const res = await empInfoStore.updateEmpInfoRead(data)

    if (res) {
      // Update local state immediately
      updateEmployeeReadStatus(employee.id_employee, newStatus)

      // Mark that changes were made so dashboard will refresh when modal closes
      props.attr.isConfirmed = true

      // Show success feedback
      const statusText = newStatus ? '確認済' : '未読'
      mtUtils.autoCloseAlert(`${employee.name_display}さんを${statusText}にしました`)
    } else {
      // Show error feedback
      mtUtils.autoCloseAlert('エラーが発生しました')
    }
  } catch (error) {
    console.error('Error updating employee read status:', error)
    mtUtils.autoCloseAlert('エラーが発生しました')
  } finally {
    // Remove from loading state
    loadingEmployees.value.delete(employee.id_employee)
  }
}



const getEmployeeDetail = (employee: EmployeeType) => {
  return {
    name_display: aahUtilsGetEmployeeName(employeeStore.getAllEmployees, employee.id_employee.toString()),
    image_path1: employeeStore.getEmployees.find((i) => i.id_employee == employee.id_employee)?.image_path1
  }  
}

const employeesWithDisplayOrder = computed(() => {
  // 1. For efficiency, create a lookup map from the employee store.
  const employeeDetailsMap = new Map(
    employeeStore.getEmployees.map(emp => [emp.id_employee, emp])
  );

  // 2. Map over your primary list and then sort the result.
  return getEmpInfo.value.emp_info_read
    .map(employee => {
      // Find the corresponding full details from the map
      const details = employeeDetailsMap.get(employee.id_employee);

      // Return a new object with all original properties plus the new one.
      return {
        ...employee, // Copy all original properties from the employee object
        display_order: details ? details.display_order : null, // Add display_order, use null as a fallback
        name_display: details ? details.name_display : null,
        image_path1: details ? details.image_path1 : null
      };
    })
    .sort((a, b) => {
      // 3. Sort the array by `display_order`.
      // This handles cases where display_order might be null or undefined.
      if (a.display_order === null || a.display_order === undefined) return 1;
      if (b.display_order === null || b.display_order === undefined) return -1;
      return a.display_order - b.display_order;
    });
});

// Check if current user has already read the message
const isCurrentUserRead = computed(() => {
  const currentUserId = JSON.parse(localStorage.getItem('id_employee') || '0')
  return getEmpInfo.value.emp_info_read?.some(emp =>
    emp.id_employee === currentUserId && emp.flg_read
  ) || false
})

const openImageViewModal = async (file: File) => {
  await mtUtils.imageViewPopup(ZoomImageModal, {
    files: file,
    singleImage: true
  })
}

const isMobile = ref(false);
// Enhanced device detection for better cross-platform support
const detectDeviceAndBrowser = () => {
  // Detect mobile devices based on screen width
  isMobile.value = window.innerWidth < 1368;
};
// It's also a good idea to re-run this function on window resize
window.addEventListener('resize', detectDeviceAndBrowser);

onMounted(() => {
  detectDeviceAndBrowser();

  if(props.data) {
    assignPageData(props.data)
  }
})

</script>
<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
      {{ 
        typeEmpInfo.find((emp) => {
          return emp.value === empInfoData.type_emp_info
        })?.label
      }}      
    </q-toolbar-title>
    <q-btn flat round  @click="openMenu">
      <q-icon size="xs" name="more_horiz" />
    </q-btn>
  </MtModalHeader>
  <q-card-section class="q-pa-lg content">
      <div class="">
        <div class="title2">
          {{ empInfoData.title }}
        </div>
        <div class="flex items-center q-mt-sm">
          <div class="flex items-center">
            <img v-if="getEmployeeProfileImage(empInfoData.id_employee_posted)" :src="getEmployeeProfileImage(empInfoData.id_employee_posted)" class="q-mr-sm emp-info-image" />
            <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
            <span class="text-grey-500">{{ getEmployeeName(empInfoData.id_employee_posted) }}</span>
          </div>
          <div class="">
            <span class="caption1 regular text-grey-500 q-mr-xs q-ml-md">連絡日:</span>
            <span class="text-grey-500">{{ dateFormat(empInfoData.datetime_posted, 'YYYY年MM月DD日  HH:mm') }}</span>
          </div>
        </div>
        <div class="row q-mt-md">
          <div class=" col-10 line-height-1_5 q-pr-md" v-html="convertLinkInMemo(empInfoData.memo_emp_info)" />
          <div class="col-2 q-pl-md">
            <template v-if="props.data.file_url">
              <UploadFile
                :isOnlyView="true"
                :fileUrl="props.data.file_url"
              />
            </template>
          </div>
        </div>
      </div>
      <q-separator class="q-my-md q-px-lg" />
      <div class="row q-mt-lg" v-if="getEmpInfo.emp_info_read.length">
      <div class="row q-mb-md" v-if="getEmpInfo?.emp_info_read?.filter((data) => { return data.flg_read })?.length">
          <div class="col" >
            <q-chip dense class="emp-chip-tosca" text-color="black">
              <div class="flex-1 justify-center text-center">確認済</div>
            </q-chip>
            <div class="flex items-center q-mt-xs">
              <div
                class="cursor-pointer employee-item-confirm"
                :class="{ 'employee-loading': loadingEmployees.has(item.id_employee) }"
                v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return data.flg_read })"
                :key="idx"
                @click="handleEmployeeClick(item)"
              >
                <div class="relative-position flex items-center">
                  <img class="emp-info-image q-mb-xs" v-if="item.image_path1" :src="item.image_path1" />
                  <q-icon v-else name="account_circle" :size="isMobile ? 'xl': 'sm'" class="text-grey-500" />
                  <!-- Loading spinner overlay -->
                  <q-spinner
                    v-if="loadingEmployees.has(item.id_employee)"
                    color="primary"
                    size="20px"
                    class="absolute-center"
                  />
                </div>
                <div class="q-ml-xs truncate-lines lines-1">{{item.name_display || '-'}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="row q-mb-md" v-if="getEmpInfo?.emp_info_read?.filter((data) => { return !data.flg_read })?.length">
          <div class="col">
            <q-chip dense class="bg-accent-500 emp-chip" text-color="black">
              <div class="flex-1 justify-center text-center">未読</div>
            </q-chip>
            <div class="flex items-center q-mt-xs">
              <div
                class="cursor-pointer employee-item"
                :class="{ 'employee-loading': loadingEmployees.has(item.id_employee) }"
                v-for="(item, idx) in employeesWithDisplayOrder.filter((data) => { return !data.flg_read })"
                :key="idx"
                @click="handleEmployeeClick(item)"
              >
                <div class="relative-position flex items-center">
                  <img class="emp-info-image q-mb-xs" v-if="item.image_path1" :src="item.image_path1" />
                  <q-icon v-else name="account_circle" :size="isMobile ? 'xl': 'sm'" class="text-grey-500" />
                  <!-- Loading spinner overlay -->
                  <q-spinner
                    v-if="loadingEmployees.has(item.id_employee)"
                    color="primary"
                    size="20px"
                    class="absolute-center"
                  />
                </div>
                <div class="q-ml-xs truncate-lines lines-1">{{item.name_display || '-'}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
</template>

<style lang="scss" scoped>
.emp-chip {
  margin-left: -2px;
  width: 76px;
}
.emp-chip-tosca {
  background-color: $tosca;
  margin-left: -2px;
  width: 76px;
}

/* Employee item confirmed (read) - now clickable */
.employee-item-confirm {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin-right: 8px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: rgba($tosca, 0.3);
    transform: translateY(-1px);
  }

  &.employee-loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

/* Employee item unread - enhanced visual feedback */
.employee-item {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin-right: 8px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: $grey-400;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &.employee-loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

/* --- iPad Media Query --- */
/* Targets typical iPad portrait and landscape views */
@media (max-width: 1366px) {
  .employee-item {
    padding: 2px;
    /* Sets the gap between grid items */
    margin-bottom: 16px; /* vertical-gap horizontal-gap */
    display: flex;
    margin-right: 0px;
    flex-direction: column;
    align-items: center;
    width: 100px;
  }
  .employee-item-confirm {
    padding: 4px;
    /* Sets the gap between grid items */
    margin-bottom: 16px; /* vertical-gap horizontal-gap */
    display: flex;
    margin-right: 0px;
    flex-direction: column;
    width: 100px;
  }
  .emp-info-image {
    width: 39px;
    height: 39px;
  }
}
.line-height-1_5 {
  line-height: 1.5;
}
</style>