<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import MtFormInputText from '@/components/form/MtFormInputText.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtSearchCustomer from '@/components/MtSearchCustomer.vue'
import useCustomerStore from '@/stores/customers'
import useQueueTicketStore from '@/stores/queue_ticket'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import {
  concatenate,
  copyText,
  formatDate,
  formatJPPhone,
  getCustomerNameHonorific,
  getDateTimeNow,
  getDateToday
} from '@/utils/aahUtils'
import OptionModal from '@/components/OptionModal.vue'
import useActionStore from '@/stores/action'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import ViewPetDetailModal from '../master/customerPet/ViewPetDetailModal.vue'
import useEmployeeStore from '@/stores/employees'
import useCliCommonStore from '@/stores/cli-common'
import { statusQueueTicket, timeHourMinute, typePetGender, typeProcessTime, typeTel } from '@/utils/enum'
import { setPageTitle } from '@/utils/pageTitleHelper'
import { CustomerType, InsurancePlanType, PetType, ServiceDetailType } from '@/types/types'
import useInsurancePlanStore from '@/stores/insurance_plans'
import { ITEM_SERVICE_CODE_CATEGORY_VACCINATION } from '@/utils/const/constServiceDetail'
import MtInputForm from '@/components/form/MtInputForm.vue'
import useRequestStore from '@/stores/requests'
import dayjs from 'dayjs'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import customerUtils from '@/pages/master/customerPet/customerUtils'
import useClinicStore from '@/stores/clinics'
import useCommonStore from '@/stores/common'
import MtFormInputNumber from '@/components/form/MtFormInputNumber.vue'
import MInputTime from '@/components/form/MInputTime.vue'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
import selectOptions from '@/utils/selectOptions'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'
import MtPetSelectionModal from '@/components/customers/MtPetSelectionModal.vue'
import _ from 'lodash'

const actionStore = useActionStore()
const router = useRouter()

const submitBtn = ref()
const selected_pets = ref([])
const id_pet = ref('')
const inputDoctor = ref()
const jpFormattedPhones = reactive([])
const ticketData = ref({
  id_customer: 0,
  code_customer: null,
  type_visit_purpose_ticket: [],
  id_employee_doctor: '',
  memo_customer: '',
  memo_admin: '',
  flg_apply_insurance: false,
  flg_appointment: false,
  flg_new_customer: false,
  flg_visit_for_pet: false,
  flg_tel_requested: false,
  flg_web_payment_requested: false,
  flg_parking_wait: false,
  id_pet: [],
  datetime_issued: getDateTimeNow(),
  type_status_queue_ticket: 1, // cli_common data where code_cli_common = 4 ; it may vary from clinic to clinic
  type_process_time: 20,
  pet_name_ui: [],
  request: [],
  datetime_estimate: null,
  process_order: 1,
  queue_detail: {}
})

const isEdit = ref(false)
const showProcessTimeEdit = ref(false)
const showDateTimeEstimateEdit = ref(false)

const isPet = ref(false)
const customerList = ref([])
const customerListDefault = reactive([])
const petList: any = ref([])
const petListDefault: any = reactive([])
const selectedCustomer = ref<CustomerType>()
const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))
const type_visit_purpose_ticket_list = ref([])
const isFromBlankQt = ref<boolean>(false)

const customerStore = useCustomerStore()
const queueTicketStore = useQueueTicketStore()
const cliCommonStore = useCliCommonStore()

const insurancePlanStore = useInsurancePlanStore()
const { getInsurancePlans } = storeToRefs(insurancePlanStore)
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getCustomerOption } = storeToRefs(customerStore)

const clinicIdFromLs = computed(() => localStorage.getItem('id_clinic'))
const cliCommonQTVisitPurposeListById = computed(() => {
  return getCliCommonQTVisitPurposeList.value.filter((item: any) => item.id_clinic == (clinicIdFromLs.value ?? 0)).map(
    (item) => ({ ...item, value: item.id_cli_common })
  )
})

const requestOptionList = ref([])
const quickCustomer = ref(false)

const emits = defineEmits(['close'])
const props = defineProps({ data: Object })
const closeModal = () => {
  emits('close')
  setPageTitle('受付・整理券')
}
function assignPageData(data: any) {
  if (data) {
    for (let key in data) {
      ticketData.value[key] = data[key]
    }
  }
  if (data.datetime_estimate) {
    ticketData.value.datetime_estimate = dayjs(data.datetime_estimate).format("HH:mm")
  }
}

const getCustomerInfoLabelProps = (customer) => {
  return {
    code: customer?.code_customer,
    fullKanaName: `${customer?.name_kana_family} ${customer?.name_kana_first}`,
    fullName: `${customer?.name_family} ${customer?.name_first}`,
    colorType: customer?.type_customer_color,
  }
}

const onClickChip = async (item: any) => {
  await mtUtils.popup(ViewPetDetailModal, {
    id_customer: ticketData.value.id_customer,
    id_pet: item.value
  })
}

const createQuickCustomer = async () => {
  data.value.type_visit_purpose_ticket = type_visit_purpose_ticket_list.value.join(',')

  await customerUtils.getNextCustomerCode(data.value, { value: null })

  if (data.value.code_customer === null) {
    delete data.value.code_customer
  }
  data.value.id_employee_request = defaultEmployee
  let response = await mtUtils.promiseAllWithLoader([customerStore.submitQuickCustomerWithQT(data.value)])
  await fetchCustomersWithPets()
  return
}

const fetchCustomersWithPets = async (event = null, page: number = 1) => {
  if (event) {
    await customerStore.fetchCustomersWithPets({ search: event, page })
  }
  else {
    await customerStore.fetchCustomersWithPets({ page })
  }  
  customerList.value.length = 0
  customerListDefault.length = 0
  customerList.value = [...customerStore.getCustomerListWithPetsOptions]
  customerListDefault.push(...customerStore.getCustomerListWithPetsOptions)
}

const formatEstimateDatetime = (timeStr: string) => {
  if (!timeStr) {
    return null
  }
  const now = new Date();
  const [hours, minutes] = timeStr.split(":");

  now.setHours(parseInt(hours), parseInt(minutes), 0); // set to 08:00:00 (example)

  // Format to YYYY-MM-DD hh:mm:ss
  const pad = (n: any) => String(n).padStart(2, '0');
  const formatted = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())} ${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
  return formatted
}

const submit = async () => {

  if (quickCustomer.value) {
    await createQuickCustomer()
    closeModal()
    return
  }
  
  if (ticketData.value.type_process_time == '') {
    delete ticketData.value.type_process_time
  }

  ticketData.value.type_visit_purpose_ticket = type_visit_purpose_ticket_list.value

  const title_request = handleAutoRequestTitle(customerStore.getCustomerOption, ticketData.value?.id_employee_doctor)
  const estimateDatetime = formatEstimateDatetime(ticketData.value?.datetime_estimate?.label ?? ticketData.value?.datetime_estimate)
  const type_process_time = ticketData.value.type_process_time?.value ?? ticketData.value.type_process_time
  
  if (ticketData.value.id_queue_ticket) {
    const payload = {
      ...ticketData.value,
      datetime_estimate: estimateDatetime,
      type_process_time,
      title_request
    }


    if (ticketData.value.id_request) {
      await useRequestStore().updateRequest(ticketData.value.id_request, {
        id_queue_ticket: payload.id_queue_ticket
      })
    }
    
    await queueTicketStore.updateQueueTicketList(
      ticketData.value.id_queue_ticket,
      payload
    )
    closeModal()
    await mtUtils.autoCloseAlert(aahMessages.success)
  } else {
    let is_bring_order = false

    const confirmation = await mtUtils.callApi(selectOptions.reqMethod.GET, 'check_order_exist', {
      id_customer: ticketData.value.id_customer
    })

    if (confirmation) {
      const confirm = await mtUtils.confirm(
        '対象の保険プランを変更すると取得した承認番号をクリアします。\n\n保険プランを変更しますか？',
        '確認',
        'No',
        'OK'
      )
      if (confirm && confirm == 'edit') is_bring_order = true
    }
    
    await queueTicketStore.submitQueueTicketList({
      ...ticketData.value,
      datetime_estimate: estimateDatetime,
      type_process_time,
      title_request,
      is_bring_order
    })
    closeModal()
    await mtUtils.autoCloseAlert(aahMessages.success)
  }
}
const openMenu = async () => {
  let menuOptions = [
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      const confirmation = await mtUtils.confirm(
        aahMessages.delete_ask,
        aahMessages.delete
      )
      if (confirmation) {
        await queueTicketStore.destroyQueueTicketList(
          ticketData.value.id_queue_ticket
        )
        await mtUtils.autoCloseAlert(aahMessages.success)
      }
      closeModal()
    }
  }
}

function selectingPet(newPetIds) {
  // 1) Remove any existing keys that are NOT in newPetIds
  if (newPetIds) {
    const newPetIdsAsString = newPetIds.map(String)

    Object.keys(ticketData.value.queue_detail).forEach(key => {
      // Convert key to a number if needed (assuming your keys are numeric IDs)
      if (!newPetIdsAsString.includes(key)) {
        delete ticketData.value.queue_detail[key]
      }
    })

    // 2) Add new keys for IDs that are in newPetIds but not currently in queue_ticket
    newPetIdsAsString.forEach(id => {
      if (!(id in ticketData.value.queue_detail)) {
        // Create whatever default object structure you need:
        ticketData.value.queue_detail[id] = {
          id_pet: id,
          type_purpose_list: [],
          type_doctor_list: []
        }
      }
    })
  }

  ticketData.value.pet_name_ui.length = 0
  if (
    ticketData.value &&
    ticketData.value.id_pet &&
    ticketData.value.id_pet.length > 0
  ) {
    ticketData.value.id_pet?.map((id_pet) => {
      const temp = petListDefault.find((pet) => pet.value == id_pet)
      if (temp && temp.label) {
        ticketData.value.pet_name_ui.push({ name_pet: temp?.label, id_pet: temp.value })
      }
    })
  }
}

const qStatusGroupA = [1, 2, 3]
const qStatusGroupB = [4, 90, 99]
const selectedQStatus = ref(ticketData.value.type_status_queue_ticket ?? null)
const switchQStatusGroup = ref<'A' | 'B' | null>(null)
const onSwitchQStatusGroup = () => {
  switchQStatusGroup.value = switchQStatusGroup.value === 'A' ? 'B' : 'A'
}

const activeGroup = computed(() => {
  selectStatus(ticketData.value?.type_status_queue_ticket)
  if (switchQStatusGroup.value) return switchQStatusGroup.value
  if (qStatusGroupB.includes(ticketData.value.type_status_queue_ticket)) {
    switchQStatusGroup.value = 'B'
    return 'B'
  }
  switchQStatusGroup.value = 'A'
  return 'A'
})

const visibleStatuses = computed(() => {
  const group = activeGroup.value === 'A' ? qStatusGroupA : qStatusGroupB
  return statusQueueTicket.filter((s) => group.includes(s.value))
})

function selectStatus(value) {
  selectedQStatus.value = value
  ticketData.value.type_status_queue_ticket = value
}

const getStatusQueueTicket = (type_status_queue_ticket) =>
  statusQueueTicket.find((v) => v.value === type_status_queue_ticket)?.label

const filterQueueTicket = async () => {

  await customerStore.selectCustomer(ticketData.value.id_customer)

  const customerName = getCustomerNameHonorific(customerStore.getCustomer)
  const newStatus = getStatusQueueTicket(
    ticketData.value.type_status_queue_ticket
  )

  // Open the confirmation modal with the ticket data
  await openConfirmationModal(customerName, newStatus, ticketData.value)
}
const modalData = ref({ customerName: '', newStatus: '' })

const openConfirmationModal = async (customerName, newStatus, ticketData) => {
  modalData.value = { customerName, newStatus }
  // Open the modal and pass ticketData as a prop
  const title_request = handleAutoRequestTitle(customerStore.getCustomerOption, ticketData.value.id_employee_doctor)
  let confirmMsg =
    customerName + 'の受付ステータスを' + newStatus + 'に変更しますか？'
  await mtUtils
    .confirm(confirmMsg, '確認', 'はい')
    .then(async (confirmation) => {
      if (confirmation) {
        queueTicketStore.updateQueueTicketList(ticketData.id_queue_ticket, {
          ...ticketData,
          title_request: title_request
        })
      }
    })
}


const handleAutoRequestTitle = (customer: any, id_employee_doctor: any) => {
  const selectedEmployeeDoctor = useEmployeeStore().getAllEmployees.find((v: any) => v.value === id_employee_doctor)?.label
  const name_customer = concatenate(
    customer?.name_family,
    customer?.name_first
  )
  const fixedTextCustomer = name_customer ? ' 様 /' : ''
  const fixedTextDoctor = selectedEmployeeDoctor ? ' 先生' : ''
  const fixedTextStaff = selectedEmployeeDoctor ? '/ 担当: ' : ''
  return (
    getDateToday() +
    ' ' +
    customer?.code_customer +
    ' ' +
    name_customer +
    ' ' +
    (fixedTextCustomer ?? '') +
    ' ' +
    (selectedEmployeeDoctor ?? '') +
    (fixedTextDoctor ?? '') +
    ' ' +
    (fixedTextStaff
      ? fixedTextDoctor !== ''
        ? fixedTextStaff
        : fixedTextStaff.replace('/ ', '')
      : '') +
    (selectedEmployeeDoctor ?? '')
  )
}

function selectedTypeVisitPurposeTicket(selectedPurposes = []) {
  if(selectedPurposes.length === 0) return
  const firstSelectedPurpose = selectedPurposes[0]
  const cliCommon = cliCommonQTVisitPurposeListById.value.find(
    (obj) => obj.value == firstSelectedPurpose
  )
  if (cliCommon && cliCommon.code_func2) {
    ticketData.value.type_process_time = parseInt(cliCommon.code_func2)
  } else if (cliCommon && !cliCommon.code_func2) {
    ticketData.value.type_process_time = 0
  } else {
    ticketData.value.type_process_time = 20
  }
}

const getInsurancePet = (pet: PetType) => {
  if (pet?.pet_insurance && pet?.pet_insurance.length > 0) {
    const getActiveIndex = pet.pet_insurance.findIndex(
      (v: InsurancePlanType) => v.date_insurance_end >= getDateToday() && v.date_insurance_start <= getDateToday()
    )
    const insurance = pet.pet_insurance[getActiveIndex || 0]
    const insurance_plan = getInsurancePlans.value.find(
      (v: InsurancePlanType) =>
        v.id_insurance_plan === insurance?.id_insurance_plan
    )
    const color = insurance?.date_insurance_end <= getDateToday()
    const expired = insurance?.date_insurance_end <= getDateToday()
    return {
      name_insurance_plan: insurance_plan?.name_insurance_plan,
      color: color,
      expired: expired
    }
  }
  return {}
}

const getPetVaccination = (pet: PetType) => {
  if (pet.last_service_detail) {
    const latestSD: ServiceDetailType = pet.last_service_detail
    if (latestSD.code_category2 === ITEM_SERVICE_CODE_CATEGORY_VACCINATION)
      return {
        datetime_service_start: latestSD.datetime_service_start,
        name_item_service: latestSD.name_item_service
      }
  } else return {}
}

async function getBookedRequestList(value: any) {
  const requestList = await useRequestStore().fetchBookingRequest({
    id_customer: value, 
    // flg_booking: true, 
    date_request_start: getDateToday() 
  })
  if (requestList && requestList.length > 0) {
    requestOptionList.value = requestList.map((item: any) => ({
      label: `${item.flg_booking ? '予約 ' : ''}${item.number_request} - ${item.title_request}`,
      value: item.id_request
    }))
  } else {
    requestOptionList.value = []
  }
}
async function selectingCustomer(value: any, initCall = false) {
  isPet.value = false
  // If address length not matched then refresh the list
  if (value) {
    ticketData.value.id_customer = value
    await customerStore.selectCustomerOptions(value)
    await getBookedRequestList(value)
    selectedCustomer.value = getCustomerOption.value
    if (selectedCustomer.value) {
      jpFormattedPhones.length = 0
      ticketData.value.code_customer = selectedCustomer.value.code_customer
      if(selectedCustomer.value.customer_tel && selectedCustomer.value.customer_tel.length > 0) {
        const formattedPhoneNumbers = await Promise.all(
          selectedCustomer.value.customer_tel.map((tel) => formatJPPhone(`${tel.tel1}${tel.tel2}${tel.tel3}`))
        )
        for(const formattedPhone of formattedPhoneNumbers) {
          jpFormattedPhones.push(formattedPhone)
        }
      }
      if (selectedCustomer.value.pets.length) {
        petListDefault.length = 0
        petList.value.length = 0
        selectedCustomer.value.pets.map((petObj: any) => {
          if ((
            petObj.flg_deceased ||
            petObj.flg_delete_by_customer ||
            petObj.flg_deceased ||
            petObj.flg_pet_excluded ||
            petObj.flg_unlist
          )) return 
          petListDefault.push({
            ...petObj,
            label: concatenate(
              petObj.code_pet,
              selectedCustomer.value.name_family,
              petObj.name_pet
            ),
            value: petObj.id_pet
          })
        })
        petList.value = [...petListDefault]

        if (!isEdit.value || isFromBlankQt.value) {
          ticketData.value.id_pet = []

          ticketData.value.id_pet.push(selectedCustomer.value.pets.filter((p1) => !(
            p1.flg_deceased ||
            p1.flg_delete_by_customer ||
            p1.flg_deceased ||
            p1.flg_pet_excluded ||
            p1.flg_unlist
          ))[0]?.id_pet)

          if (props.data?.id_pet && initCall) ticketData.value.id_pet = [props.data?.id_pet]

          selectingPet(ticketData.value.id_pet)
        }
        
        isPet.value = true
        selectingPet()
        if (!isEdit.value) {
          selectingDoctor()
        }
        submitBtn.value.$el.focus()
      }
    }
  } else {
    ticketData.value.id_pet = []
    ticketData.value.id_customer = ''
    id_pet.value = ''
    ticketData.value.code_pet = []
    selected_pets.value = []
    petList.value.length = 0
    petListDefault.length = 0
  }
}
const selectingDoctor = () => {
  let selectDoctor = null
  if (
    ticketData.value &&
    ticketData.value.id_pet &&
    ticketData.value.id_pet.length
  ) {
    if (
      petList.value[0].id_employee_main_doctor &&
      inputDoctor.value?.sortEmpList?.find(
        (v) => v.value === petList.value[0].id_employee_main_doctor
      )
    )
      selectDoctor = petList.value[0].id_employee_main_doctor
    else if (
      selectedCustomer.value.id_employee_doctor &&
      inputDoctor.value?.sortEmpList?.find(
        (v) => v.value === selectedCustomer.value.id_employee_doctor
      )
    )
      selectDoctor = selectedCustomer.value.id_employee_doctor
  }
  if(!ticketData.value.id_employee_doctor) {
    ticketData.value.id_employee_doctor = selectDoctor
  }
}

const getPetRQ = (petId: number) => {
    const requestMap:any = Object.values(ticketData.value?.queue_detail?.request_dict || {}).reduce((acc: any, req: any) => {
      acc[req.id_pet] = req.number_request.split('-')[1];
      return acc;
    }, {});

    return requestMap[petId] || null;
}
const openRequestPage = () => {
  const route = router.resolve({
    name: 'RequestDetail',
    params: { id: ticketData.value?.request?.id_request }
  })
  window.open(route.href, '_blank')
}
const toggleStatus = (key: String) => {
  ticketData.value[key] = !ticketData.value[key]
}

const init = async () => {
  customerList.value.length = 0
  customerListDefault.length = 0
  customerList.value = [...customerStore.getCustomerListOptions]
  customerListDefault.push(...customerStore.getCustomerListOptions)
  if (props.data?.id_queue_ticket) {
    // Update case
    isEdit.value = true
    assignPageData(props.data)
    //ticketData.value = props.data
    if (props.data?.id_customer != '') {
      selectingCustomer(props.data?.id_customer, true)
    }

    await getBookedRequestList(props.data?.id_customer)

    if (props.data.request && props.data.request.length > 0) {
      ticketData.value.id_request = props.data.request[0].id_request
    }

    if(!!!ticketData.value?.id_customer) {
      isFromBlankQt.value = true
    }

    // set selected doctors
    // let employee_doctors = props.data.employee_doctor
    // employee_doctors = employee_doctors.map(ed => ed.id_employee)
    // data.value.id_employee_doctor = employee_doctors
    
    // set selected purposes
    type_visit_purpose_ticket_list.value = ticketData.value.type_visit_purpose_ticket

  } else {
    // Create case
    isEdit.value = false
    ticketData.value.id_clinic = JSON.parse(localStorage.getItem('id_clinic'))

    if (props?.data?.id_customer) {
      ticketData.value.id_customer = props.data.id_customer
      selectingCustomer(props.data.id_customer, true)
    }

    if (cliCommonQTVisitPurposeListById.value.length > 0 && type_visit_purpose_ticket_list.value.length == 0) {
      type_visit_purpose_ticket_list.value = [cliCommonQTVisitPurposeListById.value[0].value]
      selectedTypeVisitPurposeTicket(cliCommonQTVisitPurposeListById.value[0].value)
    }
  }

}

const isAutofoucs = computed(() => {
  const platform = navigator.platform.toLowerCase();
  if (platform.includes('iphone') || platform.includes('ipad') || platform.includes('android')) {
    return false
  }
  return true
})

const roundedProcessTime = computed({
  get: () => {
    if (!ticketData.value.type_process_time && isEdit.value) {
      ticketData.value.type_process_time = 0
      return ticketData.value.type_process_time
    }
    return ticketData.value.type_process_time
  },
  set: (val: number | string) => {
    const n = typeof val === 'string' ? parseInt(val) : val
    if (isNaN(n) && !isEdit.value) {
      ticketData.value.type_process_time = null
      return
    } else if (isNaN(n) && isEdit.value) {
      ticketData.value.type_process_time = 0
      return ticketData.value.type_process_time
    }
    const rounded = Math.ceil(n / 5) * 5
    ticketData.value.type_process_time = rounded
  }
})


const clinicStore = useClinicStore()
const commonStore = useCommonStore()
const { getAllClinics } = storeToRefs(clinicStore)
const { getCommonTypeAnimalOptionList, getCommonBreedOptionList } =
  storeToRefs(commonStore)
const breedDefaultList: any = reactive([])
const breedList: any = ref([])
const data = ref({
  id_clinic: JSON.parse(localStorage.getItem('id_clinic')),
  id_employee_request: JSON.parse(localStorage.getItem('id_employee')),
  name_first: '',
  name_family: null,
  name_kana_family: '',
  name_kana_first: '',
  phone1: '',
  code_customer: null,
  name_pet: '',
  id_cm_animal: null,
  type_pet_gender: 1,
  id_cm_breed: '',
  date_register: formatDate(getDateToday()),
  pet_list: [],
  telephone: {
    type_tel: null,
    tel_full: null
  },
  queue_detail: {}
})

const isQtFirstPurposeIsTimed = computed(() => {
  const tData = ticketData.value as Record<string, any>
  if (!tData?.queue_detail) return false
  let isTimed: boolean = false
  Object.keys(tData.queue_detail as Record<string, any>).filter(k => tData.id_pet?.includes(Number(k))).forEach((key: string) => {
    const purposeList = tData.queue_detail?.[key]?.type_purpose_list
    if (purposeList && _.isArray(purposeList) && purposeList.length > 0) {
      const purpose = getCliCommonQTVisitPurposeList.value.find(p => p.id_cli_common == purposeList[0])
      isTimed =  !!purpose?.flg_etc1
      return
    }
    //check only first pet
    return;
  })
  return isTimed
})


const updateTel = async (val: string | number | null = null) => {
  if (val) {
    data.value.telephone.tel_full = val.toString().replace(/[-() ]/g, '')
    const temp = val.toString().substr(0, 3)
    if (['080', '090', '070'].includes(temp)) data.value.telephone.type_tel = typeTel.find((i) => i.value === 2)?.value
    if (['050'].includes(temp)) data.value.telephone.type_tel = typeTel.find((i) => i.value === 6)?.value
  }
}
const onOpenPetSelectionModal = () => {
  queueTicketStore.setSelectedQtPets(ticketData.value.id_pet)
  const props = {
    petOptions: petList.value
  }
  const withoutDefaultScrollArea = true
  mtUtils.mediumPopup(MtPetSelectionModal, props, withoutDefaultScrollArea)
    .then(() => {
      ticketData.value.id_pet = queueTicketStore.getSelectedQtPets
      selectingPet(ticketData.value.id_pet)
    })
}
const setSelectedAnimal = (val: string) => {
  if (!val) {
    breedDefaultList.length = 0
    breedList.value.length = 0
    return false
  }

  breedDefaultList.length = 0
  breedList.value.length = 0
  const codeFunc = getCommonTypeAnimalOptionList.value.find((item) => {
    return item.id_common === val
  }).code_func1
  breedDefaultList.push(
    ...getCommonBreedOptionList.value.filter(
      (common: any) => common.code_func1 == codeFunc
    )
  )
  breedList.value = [...breedDefaultList]
}


const typeProcessTimeRef = ref()
const dateTimeEstimateRef = ref()

onMounted(() => {
  init()
  insurancePlanStore.fetchInsurancePlans()
  // set page title
  if (props?.data?.customer?.name_family && props?.data?.petList?.length) {
    const pageTitle = `${props?.data?.customer?.name_family}${props?.data?.petList[0]?.name_pet}`
    setPageTitle(`受: ${pageTitle}`)
  }
})
</script>

<template>
  <q-form class="modal__card column" @submit="submit">
    <q-card-section class="q-bb q-pl-lg q-pr-sm q-py-none">
      <q-toolbar class="q-px-none">
        <q-toolbar-title class="text-grey-900 title2 bold">
          受付・整理券
        </q-toolbar-title>
        <MtFormCheckBox
          v-model:checked="quickCustomer"
          label="新規オーナー受付"
          class="q-mr-md"
          @update:checked="
          async () => {
              data.pet_list = []
              data.pet_list.push({
                name_pet: null,
                id_cm_animal: null,
                type_pet_gender: 10,
                id_cm_breed: null,
                flg_dm: true
              })
              await customerUtils.getNextCustomerCode(data, { value: null })
            }
          "
        />
        <q-btn v-if="isEdit" flat round @click="openMenu" class="q-mx-sm">
          <q-icon size="xs" name="more_horiz" />
        </q-btn>
        <q-btn flat round dense icon="close" @click="closeModal" />
      </q-toolbar>
    </q-card-section>
    <q-scroll-area class="col">  
      <!--新患登録 受付-->
      <q-card-section v-if="quickCustomer" class="col row q-col-gutter-md modal-content">
        <div :tabindex="1" class="col-lg-6 col-md-6 col-sm-12">
          <div class="row q-col-gutter-md">
            <div class="col-12 title1">新規オーナー</div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtInputForm
                v-model="data.name_family"
                :tabindex="1"
                label="オーナー 姓 *"
                type="text"
                :rules="[aahValidations.validationRequired]"
                autofocus
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtInputForm
                v-model="data.name_first"
                :tabindex="2"
                label="オーナー 名"
                type="text"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtInputForm
                v-model="data.name_kana_family"
                :tabindex="3"              
                label="オーナー セイ"
                type="text"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtInputForm
                v-model="data.name_kana_first"
                :tabindex="4"
                label="オーナー メイ"
                type="text"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtFormInputNumber
                v-model:value="data.telephone.tel_full"
                :rules="[aahValidations.validationNumber]"
                label="電話番号"
                mode="phone"
                tabindex="5"
                @update:value="updateTel"
              >
              </MtFormInputNumber>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtInputForm
                v-model="data.code_customer"
                :disable="true"
                label="オーナーCD"
                type="text"
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
              <!-- <MtFormPullDown
                v-model:options="getCliCommonQTVisitPurposeList"
                v-model:selected="ticketData.type_visit_purpose_ticket"
                :rules="[aahValidations.validationSelection]"
                :tabindex="51"
                label="来院目的 *"
                required
                @update:selected="selectedTypeVisitPurposeTicket"
              /> -->
              <MtFormMultipleSelection
                v-model:options="cliCommonQTVisitPurposeListById"
                @update:model-value="selectedTypeVisitPurposeTicket"
                v-model="type_visit_purpose_ticket_list"
                label="来院目的 *"
                required
                :rules="[aahValidations.validationSelection]"
                :tabindex="51"
              />
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12">
          <div class="row justify-between">
            <div class="col-auto title1">新規ペット</div>
            <div class="row justify-end">
              <q-btn
                class="q-mr-sm"
                round
                flat
                @click.stop="()=>{
                  data.pet_list.push({
                    name_pet: null,
                    id_cm_animal: null,
                    type_pet_gender: 10,
                    id_cm_breed: null,
                    name_kana_pet: null,
                    flg_dm: true
                  })
                }"
              >
                <q-icon name="add_circle" />
              </q-btn>
              <q-btn
                round
                flat
                @click.stop="()=>{
                    if(data.pet_list && data.pet_list.length > 0){
                      data.pet_list.pop(data.pet_list.length - 1);
                    }
                  }"
              >
                <q-icon name="remove_circle_outline" />
              </q-btn>
            </div>
          </div>
          <q-scroll-area :style="data.pet_list && data.pet_list.length > 1 ? 'height: 360px;' : 'height : 180px;'">
            <div v-for="pet in data.pet_list" class="row q-col-gutter-md">            
              <div class="col-lg-6 col-md-6 col-sm-12">
                <MtInputForm
                  v-model="pet.name_pet"
                  :rules="[aahValidations.validationRequired]"
                  :tabindex="10"
                  label="ペット名 *"
                  type="text"
                  :multiple="false"
                />
              </div>
              <div class="col-lg-6 col-md-6 col-sm-12">
                <MtInputForm
                  v-model="pet.name_kana_pet"
                  :rules="[aahValidations.validationRequired]"
                  :tabindex="10"
                  label="ペット名カナ *"
                  type="text"
                />
              </div>
              <div class="col-lg-6 col-md-6 col-sm-12">
                <MtFormPullDown
                  v-model:selected="pet.id_cm_animal"
                  :options="getCommonTypeAnimalOptionList"
                  :rules="[aahValidations.validationSelection]"
                  :tabindex="11"
                  label="動物種 *"
                  type="selection"
                  @update:selected="setSelectedAnimal"
                />
              </div>
              <div class="col-lg-6 col-md-6 col-sm-12">
                <MtFilterSelect
                  v-if="pet.id_cm_animal"
                  v-model:options="breedList"
                  v-model:selecting="pet.id_cm_breed"
                  :options-default="breedDefaultList"
                  :tabindex="13"
                  label="ペット品種"
                />
              </div>
              <div class="col-lg-12 col-md-12 col-sm-12">
                <template v-for="(type, i) in typePetGender" :key="i">
                  <MtFormRadiobtn
                    v-model:selected="pet.type_pet_gender"
                    :label="type.label"
                    :tabindex="12"
                    :val="type.value"
                    size="28px"
                    type="radio"
                    class="pet-gender q-mr-md"
                    :class="{
                      'pet-gender-male': type.value === 1 || type.value === 4,
                      'pet-gender-female': type.value === 2 || type.value === 5,
                      'pet-gender-unknown': type.value === 10
                    }"
                  />
                </template>
              </div>
            </div>
          </q-scroll-area>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-12">
          <InputEmployeeOptGroup
            ref="inputDoctor"
            v-model:selected="data.id_employee_doctor"
            :tabindex="52"
            :multiple="true"
            clearable
            label="主担当者"
            show-select-default-employee
            @update:select-default-employee="()=>{data.id_employee_doctor = defaultEmployee}"
            :rules="[aahValidations.validationRequired]"
          />
        </div>
        <div class="col-12">
          <div class="row q-col-gutter-md">
            <div class="col-lg-6 col-md-6 col-sm-12">
              <MtFormInputText
                v-model="ticketData.memo_admin"
                :tabindex="53"
                autogrow
                label="病院側メモ"
                type="text"
              />
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="row q-col-gutter-md">
            <div class="col-auto q-mr-md">
              <MtFormCheckBox
                v-model:checked="ticketData.flg_appointment"
                label="予約"
              />
            </div>
            <div class="col-auto q-mr-md">
              <MtFormCheckBox
                v-model:checked="ticketData.flg_new_customer"
                label="新患"
              />
            </div>
            <div class="col-auto q-mr-md">
              <MtFormCheckBox
                v-model:checked="ticketData.flg_visit_for_pet"
                label="お見舞い"
              />
            </div>
            <div class="col-auto q-mr-md">
              <MtFormCheckBox
                v-model:checked="ticketData.flg_tel_requested"
                label="電話呼出希望"
              />
            </div>
            <div class="col-auto q-mr-md">
              <MtFormCheckBox
                v-model:checked="ticketData.flg_web_payment_requested"
                label="Web決済希望"
              />
            </div>
          </div>
        </div>
      </q-card-section>
      <!--通常受付：既存オーナー-->
      <q-card-section
        v-if="!quickCustomer"
        class="col row q-mx-md"
        style="row-gap: 8px;"
      >        
        <div class="col-6" v-if="isEdit">
          <span class="text-grey-600">受付番号</span>
          <span class="qt_1">{{ ticketData.number_queue_ticket }}</span>
          <span class="text-grey-600 q-ml-lg">院内順序</span>
          <span class="qt_2">{{ ticketData.process_order }}</span>
        </div>
        <div class="col-6" v-if="isEdit">
          <div class="row items-center justify-end gap-4 relative-position">
            <Transition name="slide-fade" mode="out-in">
              <div :key="activeGroup" class="row items-center justify-end gap-4">
                <q-btn
                  v-for="status in visibleStatuses"
                  :key="`${status.label}-item`"
                  flat
                  :label="status.label"
                  class="q-py-sm q-px-md"
                  :class="status.value === selectedQStatus ? 'btn__primary' : 'btn__secondary'"
                  @click="selectStatus(status.value)"
                />
              </div>
            </Transition>
            <q-btn
              round
              flat
              style="background: #e0e0e0; color: black"
              icon="chevron_right"
              @click="onSwitchQStatusGroup"
            />
          </div>
        </div>
        <!-- <div v-if="ticketData.request && ticketData.request.id_request" class="col-lg-6 col-md-6 col-sm-12 q-pt-md">
          <span class="text-grey-500 cursor-pointer" @click="openRequestPage">
            <span class="text-blue ">{{ ticketData.request.number_request }}</span>
            {{ ' / ' + ticketData.request.title_request }}              
          </span>
        </div> -->
        <div class="col-auto row items-center gap-4">
          <MtSearchCustomer 
            v-if="!isEdit || isFromBlankQt"
            :applyDefaultClass="false"
            :preSelectedId="ticketData.id_customer"
            :disable="isEdit && !isFromBlankQt"
            :autofocus="isAutofoucs"
            :is-full-width="false"
            :rules="[aahValidations.validationRequired]"
            :hide-bottom-space="true"
            required
            class="q-pa-none"
            label="診察券番号 *"
            custom-option
            @update:selecting="(val) => {
              ticketData.id_customer = val;
              selectingCustomer(val)
            }"
          />
          <div v-else-if="(isEdit && selectedCustomer) || isFromBlankQt">
            <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(selectedCustomer)" :data="selectedCustomer" show-customer-code is-clickable />
          </div>
          <div class="col-12" style="width: 1px;" v-else>
            <!-- this is just to bring down next column and not to break layout -->
          </div>
          <q-btn v-if="ticketData.id_customer" round flat style="background: #e0e0e0; color: black" icon="add" @click="onOpenPetSelectionModal" />
        </div>
        <div class="col row items-start relative-position gap-4 no-wrap">
          <div class="q-ml-auto relative-position column items-end gap-2 ">
            <div class="flex items-end gap-2 justify-end">
              <div class="col"></div>
              <div class="col-auto">
                <span class="text-grey-600 q-ml-lg">想定対応時間</span>
              </div>
              <div class="col-auto">
                <div class="row no-wrap gap-2 items-end">
                  <MtFormInputNumber
                    type="number"
                    v-model:value="roundedProcessTime"
                    borderless
                    dark
                    :min="0"
                    step="5"
                    placeholder="例: 20"
                    input-class="text-blue text-center font-bold col"
                    style="font-size: 36px; font-weight: 700; padding: 0 !important; max-width: 90px; text-align: right;"
                    @blur="showProcessTimeEdit = false"
                  />
                  <div>
                    <span class="col text-grey-600" style="align-self: flex-end;">
                      分
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="relative-position column items-center gap-2">
            <div class="flex items-end justify-end">
              <span class="text-grey-600">呼出予想時刻</span>
              <span
                class="text-blue text-time"
              >
                <q-popup-proxy>
                  <q-time
                    v-model="ticketData.datetime_estimate"
                    format24h
                    :minute-options="[0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]"
                  >
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-time>
                </q-popup-proxy>
                {{ ticketData?.datetime_estimate ? ticketData.datetime_estimate : '--:--' }}
              </span>
            </div>
            <div class="full-width text-right">
              <span v-if="isQtFirstPurposeIsTimed">myVetty表示中</span>
              <span v-else>院内のみ表示（オーナーへは非表示）{{  isQtFirstPurposeIsTimed ? 1 : 0 }}</span>
            </div>
  
          </div>
        </div>
        <!-- <div class="col-lg-3 col-md-4 col-sm-6">
          <MtFormPullDown
            v-model:selected="ticketData.type_status_queue_ticket"
            v-model:options="statusQueueTicket"
            label="ステータス"
            v-if="isEdit"
            @update:model-value="filterQueueTicket()"
            :tabindex="3"
          />
        </div> -->
        <!-- <div class="col-lg-3 col-md-4 col-sm-6">
          <p 
            class="text-grey-600 q-mt-md"
            v-if="requestOptionList.length === 0">
            連携できるRQはありません。
          </p>
          <MtFormPullDown
            v-else
            v-model:options="requestOptionList"
            v-model:selected="ticketData.id_request"
            :tabindex="3"
            label="連携する既存リクエスト"
          />
        </div> -->
        <div class="col-12 q-mb-md row gap-4">
          <q-btn 
            :outline="ticketData.flg_tel_requested ? false : true" 
            size="md" 
            :color="ticketData.flg_tel_requested ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_tel_requested')"
          >
            <q-icon name="call" class="q-mr-sm" />
            電話希望
          </q-btn>      
          <q-btn 
            :outline="ticketData.flg_parking_wait ? false : true" 
            size="md" 
            :color="ticketData.flg_parking_wait ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_parking_wait')"
          >
            <q-icon name="directions_car" class="q-mr-sm" />
            外待ち・駐車場
          </q-btn>     
          <q-btn 
            :outline="ticketData.flg_appointment ? false : true" 
            size="md" 
            :color="ticketData.flg_appointment ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_appointment')"
          >
            予約来院
          </q-btn>      
          <q-btn 
            :outline="ticketData.flg_new_customer ? false : true" 
            size="md" 
            :color="ticketData.flg_new_customer ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_new_customer')"
          >
            新患 
          </q-btn>    
          <q-btn 
            :outline="ticketData.flg_visit_for_pet ? false : true" 
            size="md" 
            :color="ticketData.flg_visit_for_pet ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_visit_for_pet')"
          >
            お見舞い
          </q-btn>
          <q-btn 
            :outline="ticketData.flg_web_payment_requested ? false : true" 
            size="md" 
            :color="ticketData.flg_web_payment_requested ? 'blue-7' : 'grey-7'" 
            @click="toggleStatus('flg_web_payment_requested')"
          >
            Web決済希望
          </q-btn>
          <q-btn
            size="md" 
            color="primary" 
            style="cursor: default !important;"
            icon="aod"
            label="myVetty"
          >
          </q-btn>
        </div>
        <div class="col-12">
          <!-- 各ペットごとに1行作成 -->
          <div 
            v-for="(petId, key) in ticketData.id_pet" 
            :key="petId" 
            class="pet-box row gap-4 q-mb-md items-start"
          >
            <!-- ペットと来院目的 -->
            <div class="col-12 col-md-3">
              <div
                v-for="(pet, index) in getCustomerOption?.pets?.filter(p => p.id_pet === petId)" 
                :key="index"
                class="column"
              >
                <q-btn flat class="text-weight-bold q-py-xs q-px-md" @click.stop="onClickChip(pet)">
                  <MtPetInfoLabel :pet="pet" is-clickable class="text-left" />
                </q-btn>
                <span v-if="ticketData?.request?.number_request" class="text-blue text-center cursor-pointer" @click="openRequestPage">RQ{{ getPetRQ(petId) }}</span>
                <div
                  v-if="pet?.pet_insurance"
                  :class="getInsurancePet(pet).color ? 'text-red' : 'text-green'"
                  class="title4 bold text-center"
                >
                  {{ getInsurancePet(pet).expired ? '【失効】' : '' }}
                  {{ getInsurancePet(pet)?.name_insurance_plan }}
                </div>
                <div v-if="pet?.last_service_detail">
                  {{ getPetVaccination(pet)?.datetime_service_start }}
                  {{ getPetVaccination(pet)?.name_item_service }}
                </div>
              </div>
            </div>
            <div class="col-12 col-md-4 flex items-center overflow-hidden">
              <MtFormMultipleSelection
                v-if="petId in ticketData.queue_detail"
                v-model="ticketData.queue_detail[petId].type_purpose_list"
                v-model:options="cliCommonQTVisitPurposeListById"
                :rules="[aahValidations.validationSelection]"
                label="来院目的 *"
                required
                class="full-width"
                @update:model-value="selectedTypeVisitPurposeTicket"
              />
            </div>
            <!-- 主担当者 -->
            <div class="col-12 col-md-4 flex items-center overflow-hidden">
              <MtFormMultipleSelection
                v-if="petId in ticketData.queue_detail"
                v-model="ticketData.queue_detail[petId].type_doctor_list"
                v-model:options="useEmployeeStore().getAllEmployees"
                label="主担当者"
                required
                class="full-width"
              />
            </div>
          </div>
        </div>
        <div class="col-12 q-mb-lg">
          <div class="row q-col-gutter-md">
            <div class="col-6">
              <MtFormInputText
                type="text"
                label="受付用 対応メモ"
                v-model="ticketData.memo_admin"
                autogrow
                :tabindex="4"
              />
            </div>
            <div v-if="isEdit" class="col-6">
              <MtFormInputText
                type="text"
                label="オーナーからコメント（myVettyから入力）"
                v-model="ticketData.memo_customer"
                autogrow
                readonly
              />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-scroll-area>
    <q-card-section class="q-bt bg-white">
      <div class="modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn
          ref="submitBtn"
          unelevated
          color="primary"
          :tabindex="100"
          type="submit"
        >
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
<style lang="scss" scoped>
.modal {
  &__card {
    height: 540px;
  }
}
.modal-btn {
  display: flex;
  gap: 16px;
  justify-content: center;
  @media screen and (max-width: 500px) {
    flex-direction: column;
  }
}
.header-sticky {
  position: sticky;
  width: 100%;
  height: 5vh;
  background: white !important;
  top: 0;
  z-index: 100;
}
.bottom-sticky {
  position: sticky;
  width: 100%;
  background: white !important;
  bottom: 5px;
  z-index: 100;
}
.qt_1 {
  font-size: 30px;
  font-weight: bold;
  margin: 0 20px; 
}
.qt_2 {
  font-size: 16px;
  font-weight: bold;
  color: rgb(41, 56, 99);
  background: #cdcdcd;
  padding: 5px 13px;
  border-radius: 5px;
  margin-left: 20px;
}

.pet-row {
  border-bottom: 1px solid #cdcdcd;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.pet-box {
  padding: 16px;
  background-color: #FFFDE5;
  border: 3px solid #DAC651;
  border-radius: 3px;
}
.text-time {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.12;
}
.btn {
  &__primary {
    background: var(--q-primary);
    color: white;
  }
  &__secondary {
    background: #e0e0e0;
    color: black;
  }
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(200px);
}
.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(200px);
}
.slide-fade-enter-active {
  transition: all 300ms ease-in-out;
}
.slide-fade-leave-active {
  transition: all 300ms ease-in-out;
  position: absolute;
}
</style>
