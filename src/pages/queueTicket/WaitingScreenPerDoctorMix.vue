<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import QtIntroAndWaitingScreen from '@/pages/queueTicket/QtIntroAndWaitingScreen.vue'
import { storeToRefs } from 'pinia'
import { orderBy, groupBy, flatten } from 'lodash'
import useQueueTicketStore from '@/stores/queue_ticket'
import useCliCommonStore from '@/stores/cli-common'

import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import {
  QueueTicketType
} from '@/types/types'
import { useQueueTicketUtils } from './queueTicketUtils'

const {
  isQtCalling,
  isQtToBeCalled,
  callingQt,
  REFRESH_INTERVAL,
  CLEAR_TICKET_CALLING_SECONDS,
  QUEUE_STATUS,
  clearTimeoutAndIntervals,
  qtToBeCalledPromise,
  timeoutId,
  intervalId,
  fetchRooms
} = useQueueTicketUtils()


// Store initialization
const queueTicketStore = useQueueTicketStore()
const cliCommonStore = useCliCommonStore()

// Store refs
const { getQueueTicketLists } = storeToRefs(queueTicketStore)

// Computed Properties
const confirmedQueueTickets = computed(() => {
  const calledTickets = getQueueTicketLists.value.filter(ticket => 
    ticket.type_status_queue_ticket === QUEUE_STATUS.CALLED
  )
  
  // Group tickets by doctor
  const groupedByDoctor = groupBy(calledTickets, 'id_employee_doctor')
  
  // For each doctor, keep only the most recent CALLED ticket
  const filteredTickets = Object.values(groupedByDoctor).map(doctorTickets => 
    getMostRecentTicket(doctorTickets)
  )
  
  // Order the filtered ticketsw
  return orderBy(filteredTickets, 'datetime_service_start')
})

const waitingQueueTickets = computed(() => {
  return orderBy(
    getQueueTicketLists.value.filter(ticket => 
      ticket.type_status_queue_ticket === QUEUE_STATUS.WAITING ||
      ticket.type_status_queue_ticket === QUEUE_STATUS.IN_PROGRESS
    ),
    'datetime_service_start'
  )
})

const absentTickets = computed(() => {
  return getQueueTicketLists.value.filter(ticket => 
    ticket.type_status_queue_ticket === QUEUE_STATUS.ABSENT
  )
})

// Methods

// Helper function to get the most recent ticket for a doctor
const getMostRecentTicket = (tickets: QueueTicketType[]) => {
  return orderBy(tickets, 'datetime_service_start', 'desc')[0]
}

const setupPolling = async () => {
  await fetchRooms()
  await refreshData()
  intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
}

const refreshData = async () => {
  await queueTicketStore.fetchQueueTicketList({ today: true })

  let qtCallingIndex = getQueueTicketLists.value.findIndex((queueTicket) => queueTicket.queue_detail?.flg_qt_calling)
  if(qtCallingIndex !== -1) {

    isQtToBeCalled.value = true
    await qtToBeCalledPromise()

    isQtCalling.value = true
    callingQt.value = getQueueTicketLists.value[qtCallingIndex]
    
    clearTimeoutAndIntervals()

    timeoutId.value = setTimeout(async () => {
      let payload = {
        id_queue_ticket: callingQt.value.id_queue_ticket,
        flg_qt_calling: false
      }
      await mtUtils.callApi(selectOptions.reqMethod.POST, 'queue_ticket_calling/', payload)
      await queueTicketStore.fetchQueueTicketList({ today: true })

      isQtCalling.value = false
      intervalId.value = setInterval(refreshData, REFRESH_INTERVAL)
    }, CLEAR_TICKET_CALLING_SECONDS)
  }
}

// Lifecycle hooks
onMounted(async () => {
  await cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [4] }, true)
  setupPolling()
})

onUnmounted(() => {
  clearTimeoutAndIntervals()
})
</script>

<template>
  <QtIntroAndWaitingScreen />
  <transition name="fade">
  <div class="waiting-screen-container q-pa-md" v-if="!isQtToBeCalled && !isQtCalling">
    <!-- Called Section -->
    <section class="called-section q-mt-md q-pa-md bg-skin-100">
      <header class="flex justify-between items-center">
        <h2 class="text-weight-bold heading">呼び出し中</h2>
        <p class="text-grey-700 instruction-text">
          診察室にお入りください。
        </p>
      </header>

      <div class="flex gap-2">
        <q-btn
          v-for="ticket in confirmedQueueTickets"
          :key="ticket.id_queue_ticket"
          :label="ticket.number_queue_ticket"
          class="queue-btn bg-white text-grey-700 text-weight-bold"
        />
      </div>
    </section>

    <!-- Waiting Section -->
    <section class="waiting-section q-mt-md q-pa-md">
      <header class="flex justify-between items-center">
        <h2 class="text-weight-bold heading">待合中</h2>
        <p class="text-grey-700 instruction-text">
          診療内容によって診察の順番は前後する場合がございます。予めご了承ください。
        </p>
      </header>

      <div class="q-mt-sm flex gap-2">
        <div v-if="!waitingQueueTickets.length" class="empty-ticket">
          <span>受付中はありません</span>
        </div>
        <q-btn
          v-for="ticket in waitingQueueTickets"
          :key="ticket.id_queue_ticket"
          :label="ticket.number_queue_ticket"
          class="queue-btn waiting-btn text-white text-weight-bold"
        />
      </div>
    </section>

    <!-- Absent Section -->
    <section class="absent-section q-mt-md q-pa-md bg-grey-200">
      <header class="flex justify-between items-center">
        <h2 class="text-weight-bold heading">ご不在</h2>
        <p class="text-grey-700 instruction-text">
          受付にお声掛けください。
        </p>
      </header>

      <div class="q-mt-md flex gap-2">
        <q-btn
          v-for="ticket in absentTickets"
          :key="ticket.id_queue_ticket"
          :label="ticket.number_queue_ticket"
          class="queue-btn bg-white text-grey-700 text-weight-bold"
        />
      </div>
    </section>
  </div>
  </transition>
</template>

<style lang="scss" scoped>
.waiting-screen-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Common styles for sections
.called-section,
.waiting-section,
.absent-section {
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 10px;

  .heading {
    font-size: clamp(3vh, 6vh, 8vh);
    color: #306671;
    line-height: 1;
    margin: 0;
  }

  .instruction-text {
    font-size: clamp(2vh, 3vh, 3vh);
    margin: 0;
  }
}

// Called section specific styles
.called-section {
  flex: 1
}

// Waiting section specific styles
.waiting-section {
  flex: 1;
  background: #d3e7ea;

  .waiting-btn {
    background: #306671;
  }
}

// Absent section specific styles
.absent-section {
  flex: 1;
}

// Common button styles
.queue-btn {
  min-width: 8vw;
  height: clamp(6vh, 10vh, 12vh);
  :deep(.q-btn__content) {
    margin-top: -5px;
    font-size: clamp(2.2vh, 4vw, 8vh);
  }

  &.waiting-btn {
    margin-top: 8px;
  }
}

// Empty state styles
.empty-ticket {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  color: #757575;
  font-size: 40px;
}
</style>