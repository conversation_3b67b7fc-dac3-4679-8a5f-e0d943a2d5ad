<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'

import { event_bus } from '@/utils/eventBus'
import {
  showSelectedPurposes,
  showSelectedDoctors,
  getSelectPurpose
} from '../checkInUtils'
import {
  QueueTicketType
} from '@/types/types'

import useEmployeeStore from '@/stores/employees'
import useCliCommonStore from '@/stores/cli-common'
import useClinicStore from '@/stores/clinics'
import useQueueTicketStore from '@/stores/queue_ticket'

const cliCommonStore = useCliCommonStore()
const clinicStore = useClinicStore()
const employeeStore = useEmployeeStore()
const queueTicketStore = useQueueTicketStore()
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getClinic } = storeToRefs(clinicStore)
const { getEmployees } = storeToRefs(employeeStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  totalPets: number[]
  selectedPurposes: Map<number, Set<number>>
  isEdit: boolean
  queueTicket: QueueTicketType
}
const props = withDefaults(defineProps<Props>(), {
  totalPets: [],
  selectedPurposes: new Map(),
  isEdit: false,
  queueTicket: {} as QueueTicketType
})

const { isEdit } = props

const tab = ref(1), splitterModel = ref()
const employeeByPetMap = ref<Map<number, Set<number>>>(new Map())

const allowVisitPurposeSelection = computed(() => {
  return !(getClinic.value.type_checkin_qt) || getClinic.value.type_checkin_qt == 1 || getClinic.value.type_checkin_qt == 2
})

const noEmployee = {
  label: '指名なし（最短）',
  id_employee: -1
}

const toggleEmployee = (petId: number, employeeId: number) => {
  const pet = employeeByPetMap.value.get(petId)
  if(pet.has(employeeId)) {
    pet.delete(employeeId)
    if(pet.size === 0) pet.add(-1)  // Select no doctor option
  }
  else {
    if(employeeId === -1) {
      pet.clear()
      pet.add(-1)
    }
    else {
      pet.add(employeeId)
      pet.delete(-1)
    }
  }
}

const submitQueueTicket = () => {
  let queueTicketData = { newPet: true }
  if(isEdit) {
    queueTicketData = { ...props.queueTicket, ...queueTicketData }
  }
  queueTicketData.pet_name_ui = []
  queueTicketData.queue_detail = {}
  queueTicketData.id_pet = []
  const { selectedPurposes, totalPets } = props
  queueTicketData.total_pets = totalPets
  for (let petId of totalPets) {
    queueTicketData.id_pet.push(Number(petId))
    queueTicketData.queue_detail[petId] = {
      type_purpose_list: Array.from(selectedPurposes.get(petId)),
      type_doctor_list: Array.from(employeeByPetMap.value.get(petId)).filter((idEmployee) => idEmployee !== -1)
    }
    queueTicketData.pet_name_ui.push({
      id_pet: petId,
      name_pet: `ペット${petId}`  
    })
  }

  queueTicketStore.setToBeCreatedTickets(queueTicketData)

  event_bus.emit('confrimTicket')
  closeModal()
}

onMounted(() => {
  if(isEdit && props.queueTicket) {
    const { queueTicket } = props
    for(let petId in queueTicket.queue_detail) {
      const employeeList = queueTicket.queue_detail[petId].type_doctor_list
      employeeByPetMap.value.set(Number(petId), new Set(employeeList.length > 0 ? [...employeeList] : [-1]))
    }
  } else {
    const { totalPets } = props
    totalPets.forEach((petId) => {
      employeeByPetMap.value.set(petId, new Set([-1]))
    })
  }
})
</script>
<template>
  <div class="header text-center text-white flex justify-center items-center">
    新規オーナー
    <small class="q-ml-sm relative-position" style="top: 2px;">さま</small>
  </div>
  <q-card-section class="queue-ticket-wrapper content">
    <div>
      <div class="queue-info-text">
        来院目的を選択して、『次へ』ボタンを押してください。
      </div>
      <div class="q-mt-md">
        <q-splitter
          v-model="splitterModel"
        >
        <template v-slot:before>
          <q-tabs
            v-model="tab"
            vertical
          >
            <q-tab
              v-for="petId in totalPets"
              :name="petId"
              :key="petId"
              class="tab-item"
              :class="tab === petId ? 'active' : ''"
            >
              <q-chip
                v-if="tab === petId"
                class="chip-dark-blue text-white">選択中
              </q-chip>
              <div class="text-weight-bold pet-name"><span>ペット{{petId}}</span></div>
              <div class="text-left ellipsis" style="max-width: 150px" v-if="allowVisitPurposeSelection">来院目的: {{ showSelectedPurposes(petId, selectedPurposes, getCliCommonQTVisitPurposeList) }}</div>
              <div class="text-left ellipsis" style="max-width: 150px">担当医: {{ showSelectedDoctors(petId, employeeByPetMap, getEmployees) }}</div>
            </q-tab>
          </q-tabs>
        </template>

        <template v-slot:after>
          <q-tab-panels
            v-model="tab"
            animated
            swipeable
            vertical
            transition-prev="jump-up"
            transition-next="jump-up"
            class="tab-content"
          >
            <q-tab-panel
              v-for="petId in totalPets"
              :name="petId"
            >
              <div class="flex gap-4">
                <q-btn
                  :outline="!(employeeByPetMap.has(petId) && employeeByPetMap.get(petId).has(occupation.id_employee))"
                  v-for="occupation in [noEmployee, ...getEmployees.filter((employee: EmployeeType) => getSelectPurpose(petId, selectedPurposes, getCliCommonQTVisitPurposeList)?.includes(employee.type_occupation ? employee.type_occupation?.toString() : ''))]"
                  class="employee-select-btn text-weight-bold"
                  :class="(employeeByPetMap.has(petId) && employeeByPetMap.get(petId).has(occupation.id_employee)) ? 'selected' : 'outline-btn'"
                  :key="occupation.id_employee"
                  @click="toggleEmployee(petId, occupation.id_employee)"
                >
                  <span> {{ occupation.id_employee == -1 ? occupation.label : `${occupation.name_family} ${occupation.name_first}` }} </span>
                </q-btn>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </template>
        </q-splitter>
      </div>
    </div>
  </q-card-section>
  <q-card-section class="bg-white q-bt action-btns">
    <div class="flex justify-between">
      <div>
        <q-btn 
          outline 
          class="full-width cancel outline-btn" 
          @click="closeModal"
        >
          <span>キャンセル</span>
        </q-btn>
      </div>
      <div>
        <q-btn 
          class="full-width next text-white"
          @click="submitQueueTicket"
        >
          <span>次へ</span>
        </q-btn>
      </div>
    </div>
  </q-card-section>
</template>
<style lang="scss" scoped>
.header {
  font-size: 22px; 
  height: 50px; 
  background: $dark-blue;
}
.outline-btn {
  &:before {
    border-color: $dark-blue;
  }
  :deep(.q-btn__content) {
    color: $dark-blue;
  }
}

.tab-item {
  border: 1px solid $grey-400;
  justify-content: left;
  &:first-child {
    border-top-left-radius: 4px;
  }
  &:last-child {
    border-bottom-left-radius: 4px; 
  }
  :deep(.q-tab__content) {
    align-items: flex-start;
  }
  :deep(.q-tab__indicator) {
    width: 0 !important;
  }
  :deep(.pet-name) {
    span {
      font-size: 24px;
      small {
        font-size: 16px;
      }  
    }
  }
   &.active {
    background: #e3eaef !important;
  }
}
.tab-content {
  border: 1px solid $grey-400;
  border-left: 0;
}
.employee-select-btn {
  border-radius: 40px;
  padding: 20px 40px;
  font-size: 24px;
  &.selected {
    background-color: $dark-blue;
    color: #fff;
  }
}
.action-btns {
  .q-btn {
    font-size: 20px;
    min-width: 175px;
  }
  .cancel {
    background: rgba(3, 60, 113, 11%) !important;
  }
  .next {
    background: $dark-blue;
  }
}
</style>