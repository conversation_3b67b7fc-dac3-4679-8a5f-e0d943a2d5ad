<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import SelectDoctorModal from '@/pages/queueTicket/checkIn/SelectDoctorModal.vue'
import {
  PetType,
  CliCommon,
  QueueTicketType
} from '@/types/types'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import {
  showSelectedPurposes,
  openLargeModal
} from './checkInUtils'

import { event_bus } from '@/utils/eventBus'

import useCustomerStore from '@/stores/customers'
import useCliCommonStore from '@/stores/cli-common'
import useClinicStore from '@/stores/clinics'
import useQueueTicketStore from '@/stores/queue_ticket'
const customerStore = useCustomerStore()
const cliCommonStore = useCliCommonStore()
const clinicStore = useClinicStore()
const queueTicketStore = useQueueTicketStore()

const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const { getClinic } = storeToRefs(clinicStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  customerInfo: CustomerType,
  selectedPetsIds: number[],
  isEdit: boolean,
  queueTicket: QueueTicketType
}
const props = withDefaults(defineProps<Props>(), {
  customerInfo: {} as CustomerType,
  selectedPetsIds: [],
  isEdit: false,
  queueTicket: {} as QueueTicketType
})

const { isEdit } = props

const petMap = ref<Map<number, PetType>>(new Map())
const purposeByPetMap = ref<Map<number, Set<number>>>(new Map())

const { selectedPetsIds } = props

const tab = ref(selectedPetsIds[0]), splitterModel = ref()

const popupFunction = openLargeModal() ? mtUtils.popup : mtUtils.mediumPopup

const disableSubmitButton = computed(() => {
  const petKeys = Array.from(purposeByPetMap.value.keys())
  return petKeys.length !== selectedPetsIds.length || petKeys.some((petId) => {
    const petPurposes = purposeByPetMap.value.get(petId)
    return !petPurposes || petPurposes.size === 0
  })
})

const allowVisitVetSelection = computed(() => {
  return !(getClinic.value.type_checkin_qt) || getClinic.value.type_checkin_qt == 1 || getClinic.value.type_checkin_qt == 3
})

const getPetName = (petId: number) => {
  const pet = petMap.value.get(petId)
  if(pet) {
    const petName = pet.name_pet
    const petHonorific = customerStore.getPetHonorific(pet)
    return `<span>${petName} <small>${petHonorific}</small></span>`
  }
}

const togglePurpose = (petId: number, purpose: number) => {
  const existingSet = purposeByPetMap.value.get(petId)

  if (!existingSet) {
    purposeByPetMap.value.set(petId, new Set([purpose]))
    return
  }

  const newSet = new Set(existingSet)

  if (newSet.has(purpose)) {
    newSet.delete(purpose)
  } else {
    newSet.add(purpose)
  }

  purposeByPetMap.value.set(petId, newSet)
}

const openSelectDoctorModal = () => {
  if(allowVisitVetSelection.value) {
    popupFunction(SelectDoctorModal, {
      customerInfo: props.customerInfo,
      selectedPetsIds: props.selectedPetsIds,
      selectedPurposes: purposeByPetMap.value,
      isEdit,
      queueTicket: props.queueTicket,
    })
  }
  else {
    let queueTicketData = {  }
    if(isEdit) {
      queueTicketData = { ...props.queueTicket }
    }
    queueTicketData.petList = []
    queueTicketData.queue_detail = {}
    queueTicketData.id_pet = []
    queueTicketData.pet_name_ui = []
    for (let petId of selectedPetsIds) {
      queueTicketData.queue_detail[petId] = {
        type_purpose_list: Array.from(purposeByPetMap.value.get(petId)),
        type_doctor_list: []
      }
      queueTicketData.petList.push(petMap.value.get(petId))
      queueTicketData.id_pet.push(Number(petId))
      queueTicketData.pet_name_ui.push({
        id_pet: petId,
        name_pet: petMap.value.get(petId)?.name_pet || ''
      })
    }

    queueTicketStore.setToBeCreatedTickets(queueTicketData)

    event_bus.emit('confrimTicket')
    closeModal()
  }
}

const handleConfirmTicket = () => closeModal()

onMounted(() => {
  const { customerInfo } = props
  customerInfo.pets.forEach((pet: PetType) => {
    petMap.value.set(pet.id_pet, pet)
  })

  if(isEdit && props.queueTicket) {
    const { queueTicket } = props
    for(let petId in queueTicket.queue_detail) {
      const purposeList = queueTicket.queue_detail[petId].type_purpose_list
      purposeByPetMap.value.set(Number(petId), new Set([...purposeList]))
    }
  }
  event_bus.on('confrimTicket', handleConfirmTicket)
})

onUnmounted(() => {
  event_bus.off('confrimTicket', handleConfirmTicket)
})

</script>
<template>
  <div class="header text-center text-white flex justify-center items-center">
    {{customerInfo.name_family}} {{customerInfo.name_first}}
    <small class="q-ml-sm relative-position" style="top: 2px;">{{ customerStore.getCustomerHonorific(customerInfo) }}</small>
  </div>
  <q-card-section class="queue-ticket-wrapper content">
    <div>
      <div class="queue-info-text">
        来院目的を選択して、『次へ』ボタンを押してください。
      </div>
      <div class="q-mt-md">
        <q-splitter
          v-model="splitterModel"
        >
        <template v-slot:before>
          <q-tabs
            v-model="tab"
            vertical
          >
            <q-tab
              v-for="petId in selectedPetsIds"
              :name="petId"
              :key="petId"
              class="tab-item"
              :class="tab === petId ? 'active' : ''"
            >
              <q-chip
                v-if="tab === petId"
                class="chip-dark-blue text-white">選択中
              </q-chip>
              <div class="text-weight-bold pet-name" v-html="getPetName(petId)" />
              <div class="text-left ellipsis" style="max-width: 150px">来院目的: {{ showSelectedPurposes(petId, purposeByPetMap, getCliCommonQTVisitPurposeList) }}</div>
            </q-tab>
          </q-tabs>
        </template>

        <template v-slot:after>
          <q-tab-panels
            v-model="tab"
            animated
            swipeable
            vertical
            transition-prev="jump-up"
            transition-next="jump-up"
            class="tab-content"
          >
            <q-tab-panel
              v-for="petId in selectedPetsIds"
              :name="petId"
            >
              <div class="flex gap-4">
                <q-btn
                  :outline="!(purposeByPetMap.has(petId) && purposeByPetMap.get(petId).has(purpose.id_cli_common))"
                  v-for="purpose in getCliCommonQTVisitPurposeList"
                  class="purpose-select-btn text-weight-bold"
                  :class="(purposeByPetMap.has(petId) && purposeByPetMap.get(petId).has(purpose.id_cli_common)) ? 'selected' : 'outline-btn'"
                  :key="purpose.id_cli_common"
                  @click="togglePurpose(petId, purpose.id_cli_common)"
                >
                  <span> {{ purpose.label }} </span>
                </q-btn>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </template>

        </q-splitter>
      </div>
    </div>
  </q-card-section>
  <q-card-section class="bg-white q-bt action-btns">
    <div class="flex justify-between">
      <div>
        <q-btn 
          outline 
          class="full-width cancel outline-btn" 
          @click="closeModal"
        >
          <span>キャンセル</span>
        </q-btn>
      </div>
      <div class="flex column items-center justify-center">
        <q-btn 
          class="full-width next text-white"
          @click="openSelectDoctorModal"
          :disable="disableSubmitButton"
        >
          <span>次へ</span>
        </q-btn>
        <span v-if="disableSubmitButton" class="text-center">来院目的が未選択です</span>
      </div>
    </div>
  </q-card-section>
</template>
<style lang="scss" scoped>
.header {
  font-size: 22px; 
  height: 50px; 
  background: $dark-blue;
}
.outline-btn {
  &:before {
    border-color: $dark-blue;
  }
  :deep(.q-btn__content) {
    color: $dark-blue;
  }
}

.tab-item {
  border: 1px solid $grey-400;
  &:first-child {
    border-top-left-radius: 4px;
  }
  &:last-child {
    border-bottom-left-radius: 4px; 
  }
  :deep(.q-tab__content) {
    align-items: flex-start;
  }
  :deep(.q-tab__indicator) {
    width: 0 !important;
  }
  :deep(.pet-name) {
    span {
      font-size: 24px;
      small {
        font-size: 16px;
      }  
    }
  }
  &.active {
    background: #e3eaef !important;
  }
}
.tab-content {
  border: 1px solid $grey-400;
  border-left: 0;
}
.purpose-select-btn {
  border-radius: 40px;
  padding: 20px 40px;
  font-size: 24px;
  &.selected {
    background-color: $dark-blue;
    color: #fff;
  }
}
.action-btns {
  .q-btn {
    font-size: 20px;
    min-width: 175px;
  }
  .cancel {
    background: rgba(3, 60, 113, 11%) !important;
  }
  .next {
    background: $dark-blue;
  }
}
</style>