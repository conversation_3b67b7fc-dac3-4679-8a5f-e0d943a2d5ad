<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import pawImage from '@/assets/img/queueticketdetail/paw.svg'

import { event_bus } from '@/utils/eventBus'

import useQueueTicketStore from '@/stores/queue_ticket'
const queueTicketStore = useQueueTicketStore()

let closeModalTimeout:ReturnType<typeof setTimeout> | null = null

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

interface Props {
  confirmedQueueTickets: number[]  
}

const props = withDefaults(defineProps<Props>(), {
  confirmedQueueTickets: []  
})

const closeConfirmationModal = () => {
  queueTicketStore.clearToBeCreatedTickets()
  event_bus.emit('finalConfirmationDone')
  closeModal()
}

onMounted(() => {
  closeModalTimeout = setTimeout(() => {
    closeConfirmationModal()
  }, 1000 * 10)
})

onUnmounted(() => {
  if (closeModalTimeout) {
    clearTimeout(closeModalTimeout)
  }
})
</script>
<template>
  <div class="checkin-feat content flex col">
    <div class="checkin-feat-wrapper">
      <div class="header text-center text-white flex justify-center items-center gap-4 q-py-md">
        <img :src="pawImage" />
        <span class="checkin-confirmation-header text-large weighted">受付完了</span>
        <img :src="pawImage" />
      </div>
      <q-separator />
      <q-card-section class="qt-wrapper text-center">
        <div class="info-content">
          <div class="q-mt-lg flex items-center justify-center">
            <span class="text-regular weighted">受付番号</span>
            <span class="text-xx-large weighted q-ml-md">{{confirmedQueueTickets.sort((a,b) => b - a).join(',')}}</span>
          </div>
          <div class="q-mt-lg">
            <span class="text-regular weighted">診療内容によって、診察の順番は前後する場合がございます。</span>
            <span class="text-regular weighted">予めご了承ください。</span>
          </div>
          <div>
            <span class="text-regular weighted">10秒後にTOP画面へ切り替わります。</span>
          </div>
        </div>
      </q-card-section>
      <q-card-section>
      <div class="checkin-feat flex justify-center">
        <q-btn 
          outline 
          class="outline-btn top-btn weighted" 
          @click="closeConfirmationModal"
          >
          <span>TOPへ戻る</span>
        </q-btn>
      </div>
      </q-card-section>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$checkin-btn-dark-blue: #033C71;
$checkin-large-text-size: 40px;
.checkin-confirmation-header {
  font-size: $checkin-large-text-size;
  color: $checkin-btn-dark-blue;
}
</style>


