<script lang="ts" setup>
import { computed, defineAsyncComponent, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { exportFile } from 'quasar'
import * as Encoding from 'encoding-japanese'

// Components
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'

// Utilities
import mtUtils from '@/utils/mtUtils'
import { copyText, formatDate, formatJPPhoneNumber, getDateToday, getDaysBefore } from '@/utils/aahUtils'
import selectOptions from '@/utils/selectOptions'

// Stores
import useAuthStore from '@/stores/auth'
import useServiceDetailStore from '@/stores/service-details'
import useCustomerStore from '@/stores/customers'
import useCliCommonStore from '@/stores/cli-common'
import useCommonStore from '@/stores/common'
import useEmployeeStore from '@/stores/employees'

// Types and Enums
import { typePetGender, typeRabiesProcess, typeRabiesRound, typeTel } from '@/utils/enum'
import { RabiesType } from '@/types/types'
import { storeToRefs } from 'pinia'
import { regularAttributes, regularAttributesWithCustom } from '@/utils/pdfAttributes/regular'
import {
  DmPrintMode,
  GenerateCustomersCallback,
  GenerateCustomersCallbackParams
} from '@/pages/master/customerPet/SelectDmPrintTemplates.vue'
import UpdInjectDetailModal from '@/pages/request/inject/UpdInjectDetailModal.vue'
import useClinicStore from '@/stores/clinics'

// Import MtHtmlToExportButton component
import MtHtmlToExportButton from '@/components/MtHtmlToExportButton.vue'

// Lazy-Loaded Components (for pages)
const UpdateCustomerModal = defineAsyncComponent(() => import('@/pages/master/customerPet/UpdateCustomerModal.vue'));
const AdditionalRabiesFilterModal = defineAsyncComponent(() => import('@/pages/request/inject/AdditionalRabiesFilterModal.vue'));
const ViewPetDetailModal = defineAsyncComponent(() => import('@/pages/master/customerPet/ViewPetDetailModal.vue'));
const SelectDmPrintTemplates = defineAsyncComponent(
  () => import('@/pages/master/customerPet/SelectDmPrintTemplates.vue')
)

const { getCommonTypeAnimalOptionList } = storeToRefs(useCommonStore())

// Store nitialization
const authStore = useAuthStore();
const serviceDetailStore = useServiceDetailStore()
const cliCommonStore = useCliCommonStore()
const commonStore = useCommonStore()

const props = withDefaults(defineProps<{ isSearch: boolean }>(), {
  isSearch: false
})

const emits = defineEmits(['close'])

function closeModal() {
  emits('close')
}

// const count = ref(0)
const router = useRouter()
const tableRef = ref(null)

const columns = ref([
  {
    name: 'date_start',
    label: '接種日',
    field: 'date_start',
    style: 'width:2%'
  }, {
    name: 'customer',
    label: 'オーナー ',
    field: 'customer',
    style: 'width:6%'
  }, {
    name: 'address',
    label: '住所',
    field: 'address',
    style: 'width: 6%'
  }, {
    name: 'phone',
    label: '電話番号',
    field: 'phone',
    style: 'width: 6%'
  },
  {
    name: 'pet',
    label: 'ペット',
    field: 'pet',
    align: 'left',
    style: 'width: 6%'
  },
  {
    name: 'pet_gender',
    label: '性別',
    field: 'pet_gender',
    align: 'left',
    style: 'width:6%'
  },
  {
    name: 'id_cm_animal',
    label: '動物種',
    field: 'id_cm_animal',
    align: 'left',
    style: 'width:6%'
  },
  {
    name: 'pet_hair_color',
    label: '品種 / 毛色',
    field: 'pet_hair_color',
    align: 'left',
    style: 'width: 6%;'
  },
  {
    name: 'pet_birthday',
    label: '生年月日',
    field: 'pet_birthday',
    align: 'left',
    style: 'width: 6%'
  },
  {
    name: 'code_city_hall',
    label: '保健所',
    field: 'code_city_hall',
    align: 'left',
    style: 'width: 6%'
  },
  {
    name: 'license_id',
    label: '鑑札番号',
    field: 'license_id',
    align: 'left',
    style: 'width: 6%'
  },
  {
    name: 'datetime_licensed',
    label: '鑑札登録日',
    field: 'datetime_licensed',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'num_tag',
    label: '済票',
    field: 'num_tag',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'date_tag_issued',
    label: '済票発行日',
    field: 'date_tag_issued',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'lot_number1',
    label: 'ロット番号',
    field: 'lot_number1',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'id_employee_registered',
    label: '登録担当',
    field: 'id_employee_registered',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'type_rabies_process',
    label: '処理区分',
    field: 'type_rabies_process',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'type_rabies_round',
    label: '代行区分',
    field: 'type_rabies_round',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'number_inject',
    label: '注射番号',
    field: 'number_inject',
    align: 'left',
    style: 'width: 5%'
  },
  {
    name: 'リクエスト番号',
    label: 'リクエスト番号',
    field: 'request',
    align: 'left',
    style: 'width: 5%'
  }
])

const search = ref({
  date_start: getDaysBefore(7),
  date_end: getDateToday(),
  date_tag_issued_start: null,
  date_tag_issued_end: null,
  id_sp_clinic: null,
  code_city_hall: null,
  address_prefecture: null,
  address_cities: null,
})
const rowList = ref<Array<RabiesType & { checked: boolean }>>([])

const alreadyVisited = ref([])
const nextPage = ref(null)

const dmSelectionState = ref<'selecting' | 'none'>('none')

const dmSelectionCount = computed(() => {
  return rowList.value.filter((v) => v.checked).length
})

const downloadDmLabel = computed(() => {
  return dmSelectionState.value === 'selecting' ?
    (dmSelectionCount.value === 0 ? '印刷をキャンセル' : '印刷を続ける') : 'ハガキ印刷'
})

const labelColor = (color: number) => {
  if (color) {
    return cliCommonStore.getCliCommonCustomerColorList.find((v) => v.code_func1 === color.toString())?.text1
  }

  return ''
}

const openSearchModal = async () => {
  const confirm = await mtUtils.confirm('検索結果の反映には数分かかる場合があります。\n検索しますか？', 'Alert', 'OK')
  if (!confirm) return
  search.value.address_prefecture = null;
  search.value.address_cities = null;
  await mtUtils.smallPopup(AdditionalRabiesFilterModal, {
    search: search.value, callBackSearch: (value) => {
      search.value = { ...search.value, ...value }
    }
  })

  
  if (search.value.id_cm_animal_list && typeof search.value.id_cm_animal_list == 'object') {
    search.value.id_cm_animal_list = search.value.id_cm_animal_list.join(',')
  }
  
  nextPage.value = null
  rowList.value = []
  await fetchSchedule(false)
}

async function fetchSchedule(fetchMore: boolean = true) {
  if (fetchMore) {
    if (!nextPage.value) {
      await mtUtils.autoCloseAlert('条件内で全データを呼び出しました')
      return
    }
  }

  search.value.id_sp_clinic = localStorage.getItem('selectedClinic')
  
  const response = await mtUtils.callApi(selectOptions.reqMethod.GET, 'SearchRabiesList', {
    hide_preparation: true,
    ...search.value,
    page: nextPage.value
  }, true)


  if (response) {

    if (!alreadyVisited.value.includes(nextPage.value)) {
      rowList.value.push(...response.data.map((v: any) => ({ ...v, checked: false })))
      handleToggleDmSelection(true)
      alreadyVisited.value.push(response.current)
    }
    if (response.next) {
      nextPage.value = response.next.split('page=')[1]

    } else {
      nextPage.value = null
    }
  }
}

const downloadCompleteDmPdf = async (rows: any) => {
  const customerToFetch = rows.filter((v: any) => v.customer?.id_customer && v.checked).map((v: any) => v.customer?.id_customer)
  const customerWithAddressAndTelRespose = customerToFetch.length > 0 ? await useCustomerStore().fetchCustomersWithAdressesAndTel({
    ids: customerToFetch.join(',')
  }) : null
  mtUtils.smallPopup(SelectDmPrintTemplates, {
    mode: 'mixedSource.injectList' as DmPrintMode,
    callBack: () => {
      const generateCustomers: GenerateCustomersCallback = (params: GenerateCustomersCallbackParams) => {
        const { data } = params
        let laterAddOnMappedAttributes: Record<string, any>[] = []
        const customersData = data.filter((ccData: Record<string, any>) => {
          // Check if at least one property has a value
          return Object.values(ccData).some(value => 
            value !== undefined && 
            value !== null && 
            value !== ''
          );
        }).map((ccData: Record<string, any>, index: number) => {
          const clinicData = JSON.parse(localStorage.getItem('clinic') || '{}')
          const customerWithAddressAndTel = customerWithAddressAndTelRespose?.data?.customers ?? null
          if (customerWithAddressAndTel && customerWithAddressAndTel.length > 0 && ccData?.customer) {
            const customer = customerWithAddressAndTel.find((v: any) => v.id_customer == ccData?.customer?.id_customer)
            if(customer) {
              ccData.customer = customer
            }
          }
          return {
            id_clinic: clinicData,
            id_pet: ccData?.pet ?? {},
            id_customer: ccData?.customer ?? {},
            booking: {}
          }
        })
        return {
          rowData: customersData,
          addOnAttributes: laterAddOnMappedAttributes,
          addOnAttributeMapping: regularAttributes.concat(regularAttributesWithCustom)
        }
      }
      return {
        rowList: rows.filter((v: any) => v.checked),
        generateCustomers
      }
    },
    type_prints: [1]
  })
}

const handleDownloadDmPdf = async () => {
  const hideSelection = () => {
    dmSelectionState.value = 'none'
    columns.value.splice(0, 1)
  }
  if (dmSelectionState.value === 'selecting') {
    if(dmSelectionCount.value === 0) {
      hideSelection()
      return
    }
    await mtUtils
      .confirm(`${dmSelectionCount.value}件のDMをダウンロードしますか？`,
        '印刷テンプレートを続行',
        'はい',
        null,
        null,
        null,
        {},
        true
    )
      .then((confirmation) => {
        if (confirmation) {
          downloadCompleteDmPdf(rowList.value)
        }
      }).then(() => {
        hideSelection()
      })
  } else {
    dmSelectionState.value = 'selecting'
    columns.value.unshift({
      name: 'checkbox',
      label: '',
      field: 'checked', 
      style: 'width:5%;',
      overLoad: true
    })
  }
}

const apiCalled = ref(false)

let lastVerticalPosition = 0
let lastHorizontalPosition = 0

async function fetchMore(e) {

  if (e.horizontalPosition != lastHorizontalPosition && e.verticalPercentage == 1) {
    return
  }

  if (e.lastVerticalPosition != e.verticalPosition) {
    lastVerticalPosition = e.verticalPosition
    lastHorizontalPosition = e.horizontalPosition
  }
  
  if (e.verticalPercentage == 1 && !apiCalled.value) {
    apiCalled.value = true
    await fetchSchedule()
    apiCalled.value = false
    lastHorizontalPosition = e.horizontalPosition
  }

}

function getRabiesAddress(row: any) {
  const address = row.customer?.address

  return address?.find((a: any) => a.id_address == row.id_address) || address[0]

}

async function openCustomerModal(row: any) {
  await useCustomerStore().selectCustomer(row.id_customer)
  await mtUtils.popup(UpdateCustomerModal, {
    data: useCustomerStore().getCustomer
  })
  fetchSchedule()
}

const onRequestClick = (row: RabiesType) => {
  const route = router.resolve({ name: 'RequestDetail', params: { id: row.request.id_request } })?.href
  return window.open(route, '_blank')
}

const onInjectClick = async (row: any) => {
  await mtUtils.promiseAllWithLoader([useCustomerStore().selectCustomer(row.customer.id_customer, true)])
  const pet = useCustomerStore().getCustomer.pets.find((pet: any) => pet.id_pet == row.pet.id_pet)
  const inject = {
    ...row.inject,
    id_pet: pet.id_pet,
    pet: pet,
    rabies: { ...row }
  }

  await mtUtils.mediumPopup(UpdInjectDetailModal, {
    injectObj: inject,
    injectDetail: row.inject_detail,
    requestObj: row.request,
    isShow: true
  })

}

const onPetClick = async (row: RabiesType) => {
  await mtUtils.popup(ViewPetDetailModal, {
    id_customer: row.customer.id_customer,
    id_pet: row.pet.id_pet,
  })
}

const downloadCSV = () => {
  // Explicitly define the order of all columns in the header
  const headers = [
    '接種日', '診察券番号', 'オーナー', '郵便番号', '都道府県', '住所', '電話番号', 'ペットCD', 'ペット名',
    '性別', '品種 / 毛色', '生年月日', '保健所', '鑑札番号', '鑑札登録日', '済票',
    '済票発行日', 'ロット番号', '登録担当', '処理区分', '代行区分', 'リクエスト番号'
  ];

  // Prepare the CSV rows
  const rows = rowList.value.map(row => {
    return [
      formatDate(row.inject?.date_start) ?? '', // 接種日
      row.customer?.code_customer ?? '', // 診察券番号
      row.customer?.name_family + ' ' + row.customer?.name_first ?? '', // オーナー
      row.customer?.address[0]?.zip_code ?? '', // 郵便番号
      row.customer?.address[0]?.address_prefecture ?? '', // 都道府県
      `${row.customer?.address[0]?.address_city ?? ''} ${ row.customer?.address[0]?.address_other ?? '' }`, // 住所
      // `${row.customer.address[0]?.address_prefecture ?? ''} ${row.customer.address[0]?.address_city ?? ''} ${row.customer.address[0]?.address_other ?? ''}`, // 住所
      row.customer?.customer_tel[0]?.tel_full ? `"${row.customer?.customer_tel[0]?.tel_full}"` : '', // Preserve leading zeros for 電話番号
      row.pet?.code_pet ?? '', // ペットCD
      row.pet?.name_pet ?? '', // ペット名
      typePetGender.find((p) => row?.pet.type_pet_gender == p.value)?.label ?? '', // 性別
      `${useCommonStore().getCommonBreedOptionList.find((p) => row?.pet.id_cm_breed == p.id_common)?.name_common ?? ''} / ${useCliCommonStore().getCliCommonHairColorList.find((p) => row?.pet.id_cm_hair == p.id_cli_common)?.name_cli_common ?? ''}`, // 品種 / 毛色
      formatDate(row.pet?.date_birth) ?? '', // 生年月日
      row.code_city_hall ?? '', // 保健所
      row.pet?.license_id ?? '', // 鑑札番号
      formatDate(row.pet?.datetime_licensed) ?? '', // 鑑札登録日
      row.num_tag ?? '', // 済票
      formatDate(row.date_tag_issued) ?? '', // 済票発行日
      row.inject_detail?.lot_number1 ?? '', // ロット番号
      useEmployeeStore().getEmployeeListWOF.find((e) => row?.id_employee_registered == e.value)?.label ?? '', // 登録担当
      typeRabiesProcess.find((e) => row?.type_rabies_process == e.value)?.label ?? '', // 処理区分
      typeRabiesRound.find((e) => row?.type_rabies_round == e.value)?.label ?? '', // 代行区分
      row.request?.number_request ?? '' // リクエスト番号
    ];
  });

  // Combine headers and rows into CSV content
  const csvContent = [
    headers.join(','), // Header row
    ...rows.map(row => row.join(',')) // Data rows
  ].join('\n');

   // Convert CSV content to Shift-JIS
   const shiftJISArray = Encoding.stringToCode(csvContent);
  const shiftJIS = Encoding.convert(shiftJISArray, {
    to: 'SJIS',
    from: 'UNICODE',
  });
  const uint8Array = new Uint8Array(shiftJIS);

  // Generate the file name
  const formattedDate = getDateToday('YYYYMMDD');
  const loggedInUserName = authStore.getAuthUser.name_display;
  const fileName = `${formattedDate}_狂犬病予防接種リスト_${loggedInUserName}.csv`;

  // Export the CSV file using Quasar's exportFile utility
  const status = exportFile(fileName, uint8Array, 'text/csv');

  if (status !== true) {
    console.error('Error exporting file');
  }
};

function handleToggleDmSelection(value: any) {
  rowList.value = rowList.value.map((r: any) => ({ ...r, checked: value }))
}

// Function to generate HTML content for the PDF
const generateHtmlContent = () => {
  // Get clinic info for header
  const clinicId = localStorage.getItem('id_clinic')
  const clinicData = useClinicStore().getClinics.find((clinic: any) => clinic.id_clinic == clinicId)
  const ceoName = clinicData?.name_director || ''
  
  // Custom date formatter for YY/DD/MM format
  const formatShortDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    
    const year = date.getFullYear().toString().slice(-2); // Get last 2 digits of year
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}/${month}/${day}`;
  };
  
  // Simplified gender formatter
  const formatGender = (genderValue) => {
    if (genderValue === 1 || genderValue === 4) {
      return 'オス';
    } else if (genderValue === 2 || genderValue === 5) {
      return 'メス';
    } else {
      return '';
    }
  };
  
  // Function to get the appropriate address
  const getPreferredAddress = (addresses) => {
    if (!addresses || !addresses.length) return { address_city: '', address_other: '', tel_full: '' };
    
    // First try to find address with flg_rabies = true
    const rabiesAddress = addresses.find(addr => addr.flg_rabies === true);
    if (rabiesAddress) return rabiesAddress;
    
    // Then try to find address with flg_main = true
    const mainAddress = addresses.find(addr => addr.flg_main === true);
    if (mainAddress) return mainAddress;
    
    // Otherwise return the first address
    return addresses[0];
  };
  
  // Function to get the preferred phone number
  const getPreferredPhone = (phones) => {
    if (!phones || !phones.length) return '';
    
    // Try to find phone with flg_main_tel = true
    const mainPhone = phones.find(phone => phone.flg_main_tel === true);
    if (mainPhone) return mainPhone.tel_full;
    
    // Otherwise return the first phone
    return phones[0].tel_full;
  };

  // Function to get the pet hair color
  const getPetHairColor = (id_cm_hair) => {
    return useCliCommonStore().getCliCommonHairColorList.find((p:any) => id_cm_hair == p.id_cli_common)?.label || ''
  }

  // Function to get the pet breed name
  const getBreedName = (id_cm_breed) => {
    return useCommonStore().getCommonBreedOptionList.find((p:any) => id_cm_breed == p.id_common)?.name_common || ''
  }

  const getPetBreedHairLabel = (id_cm_breed, id_cm_hair) => {
    const breedName = getBreedName(id_cm_breed)
    const hairColor = getPetHairColor(id_cm_hair)
    return `${breedName}${ breedName && hairColor ? ' / ' : ''}${hairColor}`
  }
  
  // Constants for styling
  const commonTopThStyle = 'height: 40px !important; font-weight: normal !important; text-align: center !important; vertical-align: middle !important;'
  const commonThStyle = 'height: 40px !important; font-size: 12px !important; font-weight: normal !important; text-align: center !important; vertical-align: middle !important;'
  const commonRowDataTdStyle = 'height: 30px !important; padding:4px !important; font-size: 12px !important; font-weight: normal !important;'
  const commonRowDataTdStyle2 = 'height: 30px !important; padding:4px !important; font-size: 13px !important; font-weight: normal !important; text-align: center !important; vertical-align: middle !important;'
  const commonGeneralTdStyle = 'border-top: none !important; border-left: none !important; border-bottom: none !important;'
  const commonOnlyRightTdBorder = 'border-right: 1px solid black !important;'
  
  // Constants for page dimensions (A4 landscape)
  const PAGE_WIDTH = 296.926 + 0.5 //mm wkhtmlto adjustment
  const PAGE_HEIGHT = 210.058; // mm
  const PAGE_PADDING = { top: 10, right: 10, bottom: 15, left: 10 }; // mm
  
  // Conversion factor: 1mm = 3.779528 pixels (96 DPI)
  const MM_TO_PX = 3.779528;
  
  // Create table header
  const tableHeader = `
    <thead>
      <tr style="border-bottom: 1px solid black !important; border-top: 2px solid black !important; border-left: 2px solid black !important; border-right: 2px solid black !important;">
        <td colspan="6" style="${commonTopThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} font-size: 18px !important;">鑑札・狂犬病予防注射済票交付整理簿</td>
        <td colspan="1" style="${commonTopThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} font-size: 14px !important;">獣医師</td>
        <td colspan="2" style="${commonTopThStyle} ${commonGeneralTdStyle} font-size: 14px !important;">${ceoName}</td>
      </tr>
      <tr style="background-color: #f2f2f2 !important; border-bottom: 1px solid black !important; border-top: none !important; border-left: 2px solid black !important; border-right: 2px solid black !important;">
        <th style="width: 23%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">住所・電話番号</th>
        <th style="width: 10%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">氏名 (フリガナ)</th>
        <th style="width: 8%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">接種年月日</th>
        <th style="width: 10%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">名前</th>
        <th style="width: 12%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">種類・毛色</th>
        <th style="width: 5%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">性別</th>
        <th style="width: 8%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">生年月日</th>
        <th style="width: 12%; ${commonThStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder}">注射済票番号</th>
        <th style="width: 12%; ${commonThStyle} ${commonGeneralTdStyle}">登録鑑札番号</th>
      </tr>
    </thead>
  `;

  // Generate row HTML for each data row
  const generateRowHTML = (row, isFirstInPage, isLastInPage, isLastRow) => {
    const preferredAddress = getPreferredAddress(row.customer?.address);
    const preferredPhone = getPreferredPhone(row.customer?.customer_tel);
    
    // Determine border styles based on position
    const borderTop = isFirstInPage ? 'border-top: none !important;' : 'border-top: none !important;';
    const borderBottom = isLastInPage || isLastRow ? 'border-bottom: 2px solid black !important;' : 'border-bottom: 1px solid black !important;';
    return `
      <tr style="${borderBottom} ${borderTop} border-left: 2px solid black !important; border-right: 2px solid black !important;">
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: left !important; vertical-align: top !important;">${preferredAddress.address_city || ''} ${preferredAddress.address_other || ''}<br>
            ${formatJPPhoneNumber(preferredPhone)}</td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: left !important; vertical-align: middle !important;">${ row.customer?.name_kana_family || row.customer?.name_kana_first ? `<span style="white-space: nowrap; font-size: 9px !important;">(${row.customer?.name_kana_family + ' ' + row.customer?.name_kana_first})</span><br>` : '' }${row.customer?.name_family || ''} ${row.customer?.name_first || ''}</td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: center !important; vertical-align: middle !important;">${formatShortDate(row.inject?.date_start) || ''}</td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: left !important; vertical-align: middle !important;">${row.pet?.name_pet || ''}</td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: left !important; vertical-align: middle !important;"><small>${getPetBreedHairLabel(row?.pet?.id_cm_breed, row?.pet?.id_cm_hair)}</small></td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: center !important; vertical-align: middle !important;">${formatGender(row?.pet?.type_pet_gender)}</td>
        <td style="${commonRowDataTdStyle} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: center !important; vertical-align: middle !important;">${formatShortDate(row.pet?.date_birth) || ''}</td>
        <td style="${commonRowDataTdStyle2} ${commonGeneralTdStyle} ${commonOnlyRightTdBorder} text-align: center !important; vertical-align: middle !important;">${row.num_tag || ''}</td>
        <td style="${commonRowDataTdStyle2} ${commonGeneralTdStyle} text-align: center !important; vertical-align: middle !important;">${row.pet?.license_id || ''}</td>
      </tr>
    `;
  };
  
  // We'll use a different approach - create a temporary DOM to measure row heights
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.visibility = 'hidden';
  tempDiv.style.width = `${(PAGE_WIDTH - PAGE_PADDING.left - PAGE_PADDING.right) * MM_TO_PX}px`;
  document.body.appendChild(tempDiv);
  
  // Function to measure a row's height in mm
  const measureRowHeight = (rowHTML) => {
    tempDiv.innerHTML = `<table style="width: 100%; border-collapse: collapse;"><tbody>${rowHTML}</tbody></table>`;
    const heightPx = tempDiv.querySelector('tr').offsetHeight;
    return heightPx / MM_TO_PX; // Convert pixels to mm
  };
  
  // Create a table to measure header height in mm
  tempDiv.innerHTML = `<table style="width: 100%; border-collapse: collapse;">${tableHeader}</table>`;
  const headerHeightPx = tempDiv.querySelector('table').offsetHeight;
  const headerHeight = headerHeightPx / MM_TO_PX; // Convert pixels to mm
  
  // Clear the temp div
  tempDiv.innerHTML = '';
  
  // Initialize pages array and current page data
  const pages = [];
  const totalRowCount = rowList.value.length
  const typeRabiesRoundCount1 = rowList.value.filter((v:any) => v.type_rabies_round == 1).length
  const typeRabiesRoundCount2 = rowList.value.filter((v:any) => v.type_rabies_round == 2).length
  let currentPageRows: any[] = [];
  let currentPageHeight = 0;
  const maxPageContentHeight = PAGE_HEIGHT - PAGE_PADDING.top - PAGE_PADDING.bottom;
  let isFirstPage = true;
  // Process each row
  rowList.value.forEach((row, index) => {
    const isLastRow = index === rowList.value.length - 1;
    
    // Generate row HTML - initially mark as not first/last in page
    // We'll update this when we know the final position
    const tempRowHTML = generateRowHTML(row, false, false, isLastRow);
    const rowHeight = measureRowHeight(tempRowHTML);
    
    // Account for header height on every page, not just the first page
    const availableHeight = maxPageContentHeight - headerHeight - 5
    
    // If adding this row would exceed the page height, start a new page
    if (currentPageHeight + rowHeight > availableHeight) {
      // Mark the last row of the current page
      if (currentPageRows.length > 0) {
        const lastRowIndex = currentPageRows.length - 1;
        // Replace the last row with one marked as last in page
        const lastRow = rowList.value[index - 1];
        currentPageRows[lastRowIndex] = generateRowHTML(lastRow, currentPageRows.length === 1, true, false);
      }
      
      // Save current page
      pages.push({
        rows: currentPageRows,
        isFirstPage: isFirstPage,
        totalRowCount: totalRowCount,
        typeRabiesRoundCount1: typeRabiesRoundCount1,
        typeRabiesRoundCount2: typeRabiesRoundCount2
      });
      
      // Start a new page with this row marked as first in page
      currentPageRows = [generateRowHTML(row, true, false, isLastRow)];
      currentPageHeight = rowHeight;
      isFirstPage = false;
    } else {
      // Add row to current page - mark as first if it's the first row
      currentPageRows.push(generateRowHTML(row, currentPageRows.length === 0, false, isLastRow));
      currentPageHeight += rowHeight;
    }
  });
  
  // Add the last page if it has any rows
  if (currentPageRows.length > 0) {
    // Mark the last row of the last page
    const lastRowIndex = currentPageRows.length - 1;
    const lastRowInLastPage = rowList.value[rowList.value.length - 1];
    currentPageRows[lastRowIndex] = generateRowHTML(lastRowInLastPage, currentPageRows.length === 1, true, true);
    
    pages.push({
      rows: currentPageRows,
      isFirstPage: isFirstPage,
      totalRowCount: totalRowCount,
      typeRabiesRoundCount1: typeRabiesRoundCount1,
      typeRabiesRoundCount2: typeRabiesRoundCount2
    });
  }
  
  // Clean up the temporary div
  document.body.removeChild(tempDiv);
  // Generate HTML for all pages
  const pagesHTML = pages.map((page, pageIndex) => {
    return `
      <div class="pdf-page" style="width: ${PAGE_WIDTH}mm; height: ${PAGE_HEIGHT}mm; position: relative;">
        ${true ? `<div class="form-title" style="font-size: 11px !important; position: absolute; top: ${PAGE_PADDING.top - 6}mm; left: ${PAGE_PADDING.left}mm;">様式第3号</div>` : ''}
        ${true ? `<div class="form-title" style="font-size: 11px !important; position: absolute; width: 400px; text-align: right; top: ${PAGE_PADDING.top - 6}mm; right: ${PAGE_PADDING.left}mm;">
          総数:&nbsp;&nbsp;${page.totalRowCount}&nbsp;件&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;継続区分&nbsp;&nbsp;(&nbsp;新規:&nbsp;&nbsp;${page.typeRabiesRoundCount2}&nbsp;件,&nbsp;&nbsp;継続:&nbsp;&nbsp;${page.typeRabiesRoundCount1}&nbsp;件&nbsp;)</div>` : ''}
        <div class="page-content" style="position: absolute; top: ${PAGE_PADDING.top}mm; right: ${PAGE_PADDING.right}mm; bottom: ${PAGE_PADDING.bottom+2}mm; left: ${PAGE_PADDING.left}mm;">
          <table style="width: 100%; border-collapse: collapse; border: 2px solid black;">
            ${tableHeader}
            <tbody>
              ${page.rows.join('')}
            </tbody>
          </table>
        </div>
        <div style="position: absolute; width: 400px; text-align: right; bottom: ${PAGE_PADDING.bottom-6}mm; right: ${PAGE_PADDING.right}mm; font-size: 11px;">
          ${pageIndex + 1} / ${pages.length}
        </div>
      </div>
    `;
  }).join('');
  
  // Create complete HTML with styling
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>鑑札・狂犬病予防注射済票交付整理簿</title>
      <style>
        body {
          font-family: 'Noto Sans JP', 'Gothic A1', sans-serif;
          margin: 0;
          padding: 0;
        }
        .form-title {
          font-size: 9px;
          text-align: left;
          margin-bottom: 0;
          border: 0 !important;
          height: 20px;
        }
        thead {
          display: table-header-group;
        }
        tfoot {
          display: table-row-group;
        }
        @media print {
          thead {
            display: table-header-group;
          }
          @page {
            margin: 0;
            size: landscape;
          }
          body {
            margin: 0;
          }
          * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        }
      </style>
    </head>
    <body>
      ${pagesHTML}
    </body>
    </html>
  `;
};

// Define export options for MtHtmlToExportButton
const exportOptions = ref({
  orientation: '2',
  margin: [0, 0, 0, 0],
  image: { type: 'jpeg', quality: 0.8 },
  html2canvas: { 
    scale: 1.5,
    useCORS: true,
    logging: false,
    imageTimeout: 0,
    allowTaint: true,
    letterRendering: true,
    removeContainer: true
  },
  jsPDF: { 
    unit: 'mm', 
    format: 'a4', 
    orientation: 'landscape',
    compress: true
  }
})

// Define filename for export
const fileName = computed(() => `狂犬病予防接種リスト_${getDateToday('YYYY_MM_DD')}.pdf`)

// Callback function for rendering content
const handleRenderCallback = () => {
  return generateHtmlContent()
}

// String version of HTML content (optional, can use callback instead)
const toRenderHtmlString = ref('')

// Please adjust this to row structure, since every page is have different data structure
const getCustomerInfoLabelProps = (row) => {
  return {
    code: row.customer?.code_customer,
    fullKanaName: `${row.customer?.name_kana_family} ${row.customer?.name_kana_first}`,
    fullName: `${row.customer?.name_family} ${row.customer?.name_first}`,
  }
}

onMounted(async () => {
  await commonStore.fetchPreparationCommonList({ code_common: [2, 3] })
  await cliCommonStore.fetchPreparationCliCommonList(
    {
      code_cli_common: [5, 8]
    }
  )

  if (props.isSearch) {
    rowList.value = serviceDetailStore.getSelectedServiceDetailRecordList.map((v: any) => ({ ...v, checked: false }))
    handleToggleDmSelection(true)
  }
  await fetchSchedule(false)
})
</script>

<template>
  <div>
    <MtModalHeader @closeModal="closeModal">
      <div class="row gap-2 items-center flex-1">
        <q-toolbar-title class="text-grey-900 title2 bold prescription-title">
          狂犬病予防接種済一覧
        </q-toolbar-title>
        <MtFormInputDate
          v-model:date="search.date_start"
          autofocus
          label="接種日：Start"
          outlined
          type="date"
          class="col-2"
        />
        <MtFormInputDate
          v-model:date="search.date_end"
          label="接種日：end"
          outlined
          type="date"
          class="col-2"
        />
        <q-btn outline @click="openSearchModal">
          検索条件
        </q-btn>
        <q-btn
          outline
          unelevated
          @click="()=> {
            search = {
              date_start: null,
              date_end: null,
              date_tag_issued_start: null,
              date_tag_issued_end: null,
            }
            nextPage = null
            rowList = []
            fetchSchedule(false)
          }"
        >
          <span> クリア </span>
        </q-btn>
        <q-btn
          outline
          class="q-pl-sm q-pr-md"
          @click="downloadCSV"
        >
          <q-icon name="download" class="text-grey-700 q-mr-xs" />
          <span> CSV </span>
        </q-btn>
        
        <!-- New MtHtmlToExportButton implementation -->
        <MtHtmlToExportButton
          :contentCallback="handleRenderCallback"
          :contentString="toRenderHtmlString"
          :options="exportOptions"
          actionType="download"
          :fileName="fileName"
        >
          <template #default="{ onExport, isLoading }">
            <q-btn
              outline
              @click="onExport"
              @touchstart="onExport"
              :loading="isLoading"
            >
              <q-icon name="download" class="text-grey-700 q-mr-xs" />
              PDF
            </q-btn>
          </template>
        </MtHtmlToExportButton>
        
        <q-btn
          color="primary"
          :label="downloadDmLabel"
          @click="handleDownloadDmPdf"
        />
        <q-btn
          tabindex="3"
          color="grey-800"
          text-color="white"
          class="q-mr-sm"
          unelevated
          @click="()=>{
              nextPage = null
              rowList = []
              fetchSchedule(false)
            }"
        >
          <q-icon name="search" size="20px" />
          <span class="hide-tablet">
            検索
          </span>
        </q-btn>
      </div>
    </MtModalHeader>
    <div class="row items-center justify-between q-pa-md tableBox">
      <div class="body1 regular text-grey-700 row">
        検索結果 :<span class="q-ml-sm">{{ rowList.length }}件</span>
      </div>
    </div>
    <q-scroll-area
      class="separate-scrollbar"
      @scroll="fetchMore"
    >
      <MtTable2
        ref="tableRef"
        :columns="columns"
        :rows="rowList"
        bordered
        :style="{ width: 'calc(150vw)' , height: 'calc(100vh - 110px)'}"
        flat
        @checked="handleToggleDmSelection"
      >
        <template v-slot:row="{ row }">
          <td
            v-for="(col, index) in columns"
            :key="index"
            class="cursor-pointer"
          >
            <div
              v-if="col.field == 'checked' && dmSelectionState === 'selecting'"
              key="checked"
              auto-width
            >
              <MtFormCheckBox 
                v-model:checked="row.checked"
              />
            </div>
            <div
              v-if="col.field == 'date_start'"
              key="date_start"
            >
              <div v-if="row.inject" auto-width class="body2 row no-wrap regular">
                {{ formatDate(row.inject.date_start) ?? '' }}
              </div>
            </div>

            <div
              v-if="col.field == 'num_tag'"
              key="num_tag"
            >
              <div v-if="row" auto-width class="body2 row no-wrap regular">
                {{ row?.num_tag ?? '' }}
              </div>
            </div>
            <div
              v-if="col.field == 'date_tag_issued'"
              key="date_tag_issued"
            >
              <div v-if="row" auto-width class="body2 row no-wrap regular">
                {{ formatDate(row.date_tag_issued) ?? '' }}
              </div>
            </div>

            <div
              v-else-if="col.field == 'customer'"
              key="customer_name"
              auto-width
            >
              <div
                v-if="row.customer"
                class="body2 column regular"
                @click="openCustomerModal(row?.customer)"
              >
                <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(row)" show-customer-code is-clickable />
              </div>
            </div>

            <div v-else-if="col.field == 'code_city_hall'" key="code_city_hall" auto-width>
              <div v-if="row" class="body2 column regular">
                {{ row.code_city_hall }}
              </div>
            </div>

            <div v-else-if="col.field == 'pet'" key="pet" auto-width>
              <div v-if="row.pet" class="body2 column regular" @click="onPetClick(row)">
                <MtPetInfoLabel :pet="row.pet" is-clickable />
              </div>
            </div>


            <div v-else-if="col.field == 'pet_birthday'" key="pet_birthday" auto-width>
              <div v-if="row.pet" auto-width class="body2 row no-wrap regular">
                {{ row?.pet.date_birth ? formatDate(row?.pet.date_birth) : '' }}
              </div>
            </div>

            <div v-else-if="col.field == 'pet_gender'" key="pet_gender">
              <div v-if="row.pet" class="body2 row">
                {{ typePetGender.find((p) => row?.pet.type_pet_gender == p.value)?.label ?? '' }}
              </div>
            </div>

            <div v-else-if="col.field == 'id_cm_animal'" key="id_cm_animal">
              <div v-if="row.pet" class="body2 row">
                {{ getCommonTypeAnimalOptionList.find((p) => row?.pet.id_cm_animal == p.value)?.label ?? '' }}
              </div>
            </div>
        

            <div v-else-if="col.field == 'pet_hair_color'" key="pet_hair_color">
              <div v-if="row.pet" class="body2 row">
                {{ useCliCommonStore().getCliCommonHairColorList.find((p) => row?.pet.id_cm_hair == p.id_cli_common)?.name_cli_common ?? ''
                }}
              </div>
              <div v-if="row.pet" class="body2 row">
                {{ useCommonStore().getCommonBreedOptionList.find((p) => row?.pet.id_cm_breed == p.id_common)?.name_common ?? ''
                }}
              </div>
            </div>


            <div v-else-if="col.field == 'license_id'" key="license_id" auto-width>
              <div v-if="row.pet" auto-width class="body2 row no-wrap regular">
                {{ row?.pet.license_id ?? '' }}
              </div>
            </div>

            <div v-else-if="col.field == 'datetime_licensed'" key="datetime_licensed" auto-width>
              <div v-if="row.pet" auto-width class="body2 row no-wrap regular">
                {{ row?.pet.datetime_licensed ? formatDate(row?.pet.datetime_licensed) : '' }}
              </div>
            </div>

            <div v-else-if="col.field == 'address'" key="address" auto-width>
              <div v-if="row.customer && row.customer.address && row.customer.address.length && row.customer.address.length > 0"
                   class="body2 column regular">
              <span class="regular text-grey-700">
                {{ getRabiesAddress(row)?.zip_code ?? '' }}
              </span>    
              <span class="regular text-grey-700">
                 {{ getRabiesAddress(row)?.address_prefecture ?? '' }}
              </span>
              <span class="regular text-grey-700">
                 {{ getRabiesAddress(row)?.address_city ?? '' }}
              </span><span class="regular text-grey-700">
                 {{ getRabiesAddress(row)?.address_other ?? '' }}
              </span>
              </div>
            </div>

            <div v-else-if="col.field == 'phone'" key="phone" auto-width @click="copyText(row.customer.customer_tel[0].tel_full)">
              <div v-if="row.customer && row.customer.customer_tel	 && row.customer.customer_tel.length && row.customer.customer_tel.length > 0"
                   class="body2 column regular">
              <span class="regular text-grey-700">
                {{ typeTel.find((t) => row.customer.customer_tel[0]?.type_tel == t.value)?.label ?? '' }}
              </span><span class="regular text-grey-700">
                {{ row.customer.customer_tel[0]?.title_tel ?? '' }}
              </span><span class="regular text-grey-700 text-blue">
                {{ row.customer.customer_tel[0]?.tel_full ?? '' }}
              </span>
              </div>
            </div>
            <div v-else-if="col.field == 'id_employee_registered'" key="id_employee_registered" auto-width>
              <div v-if="row.id_employee_registered" class="body2 column regular">
                {{ useEmployeeStore().getEmployeeListWOF.find((e) => row?.id_employee_registered == e.value)?.label ?? ''
                }}
              </div>
            </div>          
            <div v-else-if="col.field == 'type_rabies_process'" key="type_rabies_process" auto-width>
              <div class="body2 column regular" v-if="row.type_rabies_process">
                {{ typeRabiesProcess.find((e)=> row?.type_rabies_process == e.value)?.label ?? '' }}
              </div>
            </div>
            <div v-else-if="col.field == 'type_rabies_round'" key="type_rabies_round" auto-width>
              <div class="body2 column regular" v-if="row.type_rabies_round">
                {{ typeRabiesRound.find((e)=> row?.type_rabies_round == e.value)?.label ?? '' }}
              </div>
            </div>
            <div v-else-if="col.field == 'lot_number1'" key="lot_number1" auto-width>
              <div v-if="row && row.inject_detail" class="body2 column regular">
                {{ row.inject_detail.lot_number1 ?? '' }}
              </div>
            </div>

            <div v-else-if="col.field == 'number_inject'" key="number_inject" auto-width>
              <div v-if="row && row.request" class="body2 column regular text-blue" @click="onInjectClick(row)">
                {{ row.inject.number_inject ?? '' }}
              </div>
            </div>
            <div v-else-if="col.field == 'request'" key="request" auto-width>
              <div v-if="row && row.request" class="body2 column regular text-blue" @click="onRequestClick(row)">
                {{ row.request.number_request ?? '' }}
              </div>
            </div>

          </td>
        </template>
      </MtTable2>
    </q-scroll-area>
  </div>
</template>

<style lang="scss" scoped>
.grid {
  width: 350px;
  display: grid;
  grid-template-columns: 47% 6% 47%;
}

.separate-scrollbar {
  height: calc(100vh - 110px);
  width: 100%;
  max-width: 100%;

  :deep(.q-scrollarea__content) {
    max-height: unset !important;
  }
}

.hide-tablet {
  @media screen and (max-width: 1280px) {
    display: none;
  }
}
</style>
