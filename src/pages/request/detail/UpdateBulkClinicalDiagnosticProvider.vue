<script lang="ts" setup>
import { ref, computed } from 'vue'
import { typeDiagnosticInfoGroupTitle, typeDiagnosticInfo, typeProvider } from '@/utils/enum'
import { event_bus } from '@/utils/eventBus'

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const selectedDiagnostics = ref<Set>(new Set([]))
const selectedProvider = ref(null)

const groupedTypeDiagnotics = computed(() => {
  const groupedData = {}
  for(let group of typeDiagnosticInfoGroupTitle) {
    groupedData[group.value] = { title: group.label, data: [] }
    groupedData[group.value].data =  typeDiagnosticInfo.filter((v) => v.groupTitle == group.value)
  }
  return groupedData
})

const toggleDiagnostic = (typeDiagnostic: number) => {
  if(selectedDiagnostics.value.has(typeDiagnostic)) {
    selectedDiagnostics.value.delete(typeDiagnostic)
    return
  }
  selectedDiagnostics.value.add(typeDiagnostic)
}

const sumbmitSelectedTypes = () => {
  let selectedDiagnosticsProviders = {
    diagnostics: Array.from(selectedDiagnostics.value).join(','),
    provider: selectedProvider.value
  }
  event_bus.emit('onSelectBulkFileTypes', selectedDiagnosticsProviders)
  closeModal()
}
</script>
<template>
  <div class="flex justify-between">
    <div class="text-weight-bold q-pa-sm q-ml-sm" style="font-size:20px">臨床ファイル 一括設定</div>
    <q-btn class="q-mr-sm" flat round @click="closeModal">
      <q-icon size="xs" name="close" />
    </q-btn>
  </div>
  <q-card-section class="content">
    <div class="text-weight-bold q-mb-md" style="font-size: 18px">データ区分<span class="caption1 regular text-grey-700 q-ml-xs">（複数選択可）</span></div>
    <template v-for="(groupTypeDiagnostic, groupIndex) in Object.keys(groupedTypeDiagnotics)" :key="`${groupTypeDiagnostic}-${idx}`">
      <div class="row q-col-gutter-md">
        <div class="col-sm-2 text-body1 text-right">{{ groupedTypeDiagnotics[groupTypeDiagnostic].title }}</div>
        <div class="col-sm-10 flex items-center gap-4">
          <template v-for="typeDiagnostic in groupedTypeDiagnotics[groupTypeDiagnostic].data" :key="typeDiagnostic.value">
            <q-btn 
              :class="selectedDiagnostics.has(typeDiagnostic.value) ? 'bg-primary text-white' : 'bg-grey-200'"
              @click="toggleDiagnostic(typeDiagnostic.value)" 
              class="type-diagnostic-label">
              {{typeDiagnostic.label}}
            </q-btn>
          </template>
        </div>
      </div>
      <div class="q-my-md" style="margin-left: 5%; border: 1px solid #cdcdcd" v-if="groupIndex !== (typeDiagnosticInfoGroupTitle.length - 1)"></div>
    </template>
    
    <div class="text-weight-bold q-mt-lg" style="font-size: 18px">情報元<span class="caption1 regular text-grey-700 q-ml-xs">（オプション/単一選択）</span></div>
    <div class="flex gap-4 q-mt-sm">
      <template v-for="provider in typeProvider">
        <q-btn
           @click="selectedProvider = (selectedProvider == provider.value ? null : provider.value)"
           class="type-provider-label"
          :class="selectedProvider == provider.value ? 'bg-primary text-white' : ''">
            {{provider.label}}
        </q-btn>
      </template>
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn 
        outline 
        class="bg-grey-100 text-grey-800" 
        @click="closeModal"
        >
          <span>キャンセル</span>
      </q-btn>
      <q-btn
        class="q-ml-md"
        color="primary"
        unelevated
        @click="sumbmitSelectedTypes"
        >
          <span>保存</span>
      </q-btn>
    </div>
  </q-card-section>
</template>
<style lang="scss" scoped>
.type-diagnostic-label, .type-provider-label {
  :deep(.q-btn__content) {
    font-size: 24px;
  }
}
</style>