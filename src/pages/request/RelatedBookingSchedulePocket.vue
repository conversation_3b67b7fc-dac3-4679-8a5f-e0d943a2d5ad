<script lang="ts" setup>

import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import { ref, watch } from 'vue'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'
import { formatDateTime } from '@/utils/aahUtils'

const relatedBookingList = ref([])
const props = defineProps({ item: Object, from: String })

watch(() => props.item, async (nowValue, oldValue) => {
  if (nowValue && nowValue.id_pet) {
    console.log(props.item)
    await init()
  }
})

const init = async () => {
  const response = await mtUtils.callApi(selectOptions.reqMethod.GET, 'bookings', {
    id_pet: props.item.id_pet,
    id_customer: props.item.id_customer,
    fromSd: props.from == 'Sd',
    fromId: props.from == 'Id',
    fromPd: props.from == 'Pd',
    id_item_service: props.item.id_item_service
  })
  if (response) {
    relatedBookingList.value = response
  }
}

</script>

<template>
  <div v-if="relatedBookingList.length > 0" class="scheduled-order-section">
    <div class="items-grid">
      <div
        v-for="(item, index) in relatedBookingList"
        :key="index"
        class="order-item"
      >
        <div class="item-content">
          <MtFormCheckBox
            v-model:checked="item.flg_end"
            @update:checked="async (value)=>{
            await mtUtils.callApi(selectOptions.reqMethod.PUT, `/booking/${item.id_booking}`, {
              'flg_end': value
            })
          }"
          />
          <div class="item-info">
            <div class="item-name">
              {{
                props.item?.name_prescription_display || props.item?.name_inject_display || props.item?.name_item_service
              }}
            </div>
            <div class="item-date">{{ formatDateTime(item?.datetime_booking_confirmed) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

.scheduled-order-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.header-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 20px;
}

.cancel-btn {
  background-color: #ffffff;
  border: 1px solid #ccc;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.confirm-btn {
  background-color: #333333;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn:hover {
  background-color: #f0f0f0;
}

.confirm-btn:hover {
  background-color: #555555;
}

.scheduled-order-section {
  background-color: white;
  border: 2px solid #333;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: bold;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.order-item {
  background-color: #e8f0fe;
  border: 2px solid #4285f4;
  border-radius: 8px;
  padding: 15px;
}

.item-content {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.item-checkbox {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  cursor: pointer;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.item-date {
  color: #666;
  font-size: 14px;
}

.add-item-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.add-item-btn:hover {
  background-color: #3367d6;
}

</style>