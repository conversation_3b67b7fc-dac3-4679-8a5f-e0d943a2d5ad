<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw, onMounted, reactive, ref, shallowRef, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRoute, useRouter } from 'vue-router'
import { debounce, keyBy, keys } from 'lodash'

// Utilities
import mtUtils from '@/utils/mtUtils'
import { aahUtilsGetEmployeeName, getCustomerName, getDateToday, getDaysBefore } from '@/utils/aahUtils'
import { setPageTitle } from '@/utils/pageTitleHelper'

// Stores
import useRequestStore from '@/stores/requests'
import useCustomerStore from '@/stores/customers'
import useActionStore from '@/stores/action'
import useEmployeeStore from '@/stores/employees'
import useCliCommonStore from '@/stores/cli-common'
import useServiceDetailStore from '@/stores/service-details'

// Components
import MtHeader from '@/components/layouts/MtHeader.vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtToolTipsSmall from '@/components/toolTips/MtToolTipsSmall.vue'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'

// Types
import { RequestType } from '@/types/types'

// Lazily loaded components with loading state
const MtSpinnerLoading = defineAsyncComponent(() => import('@/components/MtSpinnerLoading.vue'))

const UpdateRequestModal = defineAsyncComponent({
  loader: () => import('./UpdateRequestModal.vue'),
  loadingComponent: MtSpinnerLoading,
  delay: 200,
  timeout: 10000
})

const AdditionalFilterRequestModal = defineAsyncComponent({
  loader: () => import('./AdditionalFilterRequestModal.vue'),
  loadingComponent: MtSpinnerLoading,
  delay: 200,
  timeout: 10000
})

// Store instances
const requestStore = useRequestStore()
const customerStore = useCustomerStore()
const actionStore = useActionStore()
const employeeStore = useEmployeeStore()
const cliCommonStore = useCliCommonStore()
const serviceDetailStore = useServiceDetailStore()

// Store references
const {
  getAdditionalSearchRequest,
  getAdditionalSearchCountRequest,
  getRequests
} = storeToRefs(requestStore)
const { getAllCustomerPreps } = storeToRefs(customerStore)
const badgeCount = computed(() => requestStore.getAdditionalSearchCountRequest)

// Router
const router = useRouter()
const route = useRoute()

// Computed properties
const action = computed(() => actionStore.action)
const params = computed(() => actionStore.params)

// Reactive variables
const customerList = ref<any>([])
const customerListDefault = reactive<any>([])

const search = ref<RequestType>({
  date_request_start: getDaysBefore(1),
  date_request_goal: getDateToday(),
  number_request: null,
  name_customer: '',
  id_customer: null,
  id_pet: null,
  code_customer: null,
  title_request: null,
  flg_complete: null,
  flg_complete_payment: null,
  id_clinic: null
})

// Use shallowRef for static data to avoid reactivity overhead
const columns = shallowRef([
  {
    name: 'number_request',
    label: 'リクエスト番号',
    field: 'number_request',
    align: 'left'
  },
  {
    name: 'date_request_start ',
    label: '開始日',
    field: 'date_request_start',
    align: 'left'
  },
  {
    name: 'date_request_goal',
    label: '終了日',
    field: 'date_request_goal',
    align: 'left'
  },
  {
    name: 'name_customer',
    label: 'オーナー',
    field: 'name_customer',
    align: 'left'
  },
  {
    name: 'title_request',
    label: 'リクエスト名',
    field: 'title_request',
    align: 'left',
    style: 'width: 300px'
  },
  {
    name: 'id_employee_doctor',
    label: '担当獣医師',
    field: 'id_employee_doctor',
    align: 'left'
  },
  {
    name: 'flg_complete',
    label: '完了',
    field: 'flg_complete',
    align: 'center'
  },
  {
    name: 'open_request_detail',
    label: ' ',
    field: 'open_request_detail',
    align: 'left'
  }
])

const openSearchModal = async () => {
  await mtUtils.smallPopup(AdditionalFilterRequestModal, {
    search: search.value
  })

  const additionalSearchRequest = getAdditionalSearchRequest.value

  if (additionalSearchRequest) {
    // Clear previous search parameters
    const newSearch = {
      date_request_start: search.value.date_request_start,
      date_request_goal: search.value.date_request_goal,
      id_clinic: search.value.id_clinic
    }

    // Merge with new parameters
    search.value = {
      ...newSearch,
      ...additionalSearchRequest
    }

    // Parse JSON strings if needed
    if (search.value.id_cm_animal_list) {
      search.value.id_cm_animal_list = JSON.stringify(search.value.id_cm_animal_list)
    }
    if (search.value.id_cm_breed_list) {
      search.value.id_cm_breed_list = JSON.stringify(search.value.id_cm_breed_list)
    }
    
    search.value.json = true
    init()
  }
}

const openRequestDetail = (row: any) => {
  if (row && row.id_request) {
    const route = router.resolve({
      name: 'RequestDetail',
      params: { id: row.id_request }
    })
    window.open(route.href, '_blank')
  } else {
    console.error('Invalid row data')
  }
}

const openAddModal = async (params = null) => {
  await mtUtils.mediumPopup(UpdateRequestModal, params)
  init()
}

const onRowClick = async (row: any) => {
  serviceDetailStore.setServiceDetailList(null)
  customerStore.resetSelectedCustomer()
  useRequestStore().setIsFastFetch(row.id_pet, row.id_customer, true)
  await router.push({
    name: 'RequestDetail',
    params: { id: row.id_request },
  })
}

// Debounce search to prevent excessive API calls
const searchData = debounce(() => {
  const id_clinic = localStorage.getItem('id_clinic')
    ? JSON.parse(localStorage.getItem('id_clinic')!)
    : null

  if (id_clinic) {
    search.value.id_clinic = id_clinic
  }
  requestStore.fetchRequests(search.value)
}, 300)

const clearSearch = () => {
  search.value = {
    date_request_start: null,
    date_request_goal: null,
    id_customer: null,
    id_pet: null,
    number_request: null,
    title_request: null,
    code_customer: null,
    flg_complete: null,
    flg_complete_payment: null,
    id_clinic: null,
    id_employee_doctor: null,
    id_employee_staff: null,
    id_cm_animal_list: null,
    id_cm_breed_list: null,
    name_customer: ''
  }

  requestStore.setRequestSearch(null, 0)
}

const getDoctorName = (value: any) => {
  const employee = employeeStore.getAllEmployees.find((emp: any) => 
    emp.id_employee === value
  )
  if (employee) {
    return employee.name_employee
  }
  return null
}

// Please adjust this to row structure, since every page is have different data structure
const getCustomerInfoLabelProps = (row) => {
  return {
    code: row?.code_customer,
    fullKanaName: row?.customerFullName,
    fullName: row?.customerName,
    colorType: row?.type_customer_color
  }
}

const init = async () => {
  // Efficiently reset arrays without reactivity overhead
  customerList.value = []
  customerListDefault.length = 0
  
  // Clone arrays instead of mutating in place
  customerList.value = [...customerStore.getCustomerListOptions]
  customerListDefault.push(...customerStore.getCustomerListOptions)

  await requestStore.fetchRequests(search.value)
}

const moveNext = (e: any) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    ;(inputs[index + 1] as HTMLElement).focus()
  } else {
    ;(inputs[1] as HTMLElement).blur()
    searchData()
  }
}

// Optimize watch with immediate option
// watch(
//   () => search.value.date_request_start,
//   (newStartDate) => {
//     search.value.date_request_goal = newStartDate;
//   },
//   { immediate: true }
// );

const getEmployeeLabel = (empId: any) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, empId)
}

const setSearchRequestByParams = async () => {
  if (route.query.id_customer)
    search.value.id_customer = route.query.id_customer as string
  if (route.query.id_pet) search.value.id_pet = route.query.id_pet as string
  if (route.query.id_customer || route.query.id_pet) {
    search.value.date_request_start = undefined
    search.value.date_request_goal = undefined
  }

  let count = 0
  keys(search.value).forEach((key) => {
    const val = search.value[key as keyof typeof search.value] as any
    if (
      ![
        'date_request_start',
        'date_request_goal',
        'flg_complete_payment',
        'flg_complete',
        'id_cm_animal_list',
        'id_cm_breed_list'
      ].includes(key)
    ) {
      if (val && val !== null && val !== false && val !== '') count++
    }
  })

  const id_clinic = localStorage.getItem('id_clinic')
    ? JSON.parse(localStorage.getItem('id_clinic')!)
    : null

  if (id_clinic) {
    search.value.id_clinic = id_clinic
  }
  requestStore.setRequestSearch(search.value, count)
}

const openHelpMenu = async () => {
  await mtUtils.smallPopup(MtToolTipsSmall, {
    title: 'Test',
    content: 'Test'
  })
}

// Memoize expensive computations
const customerDataById = computed(() => {
  if (!getAllCustomerPreps.value?.length) return {}
  return keyBy(getAllCustomerPreps.value, 'id_customer')
})

// Memoize and optimize the requestsList computation
const requestsList = computed(() => {
  if (!getRequests.value?.length) return []
  if (!Object.keys(customerDataById.value).length) return getRequests.value

  return getRequests.value.map((request: any) => {
    const customerData = customerDataById.value[request?.id_customer]
    if (!customerData) {
      return markRaw(request)
    }

    const {
      type_customer_color,
      name_kana_family,
      name_kana_first,
      name_customer_display,
      name_family,
      name_corporate
    } = customerData

    return markRaw({
      ...request,
      type_customer_color,
      customerFullName: `${name_kana_family || ''} ${name_kana_first || ''}`,
      customerName: getCustomerName({
        name_customer_display,
        name_family,
        name_corporate
      })
    })
  })
})

const changeDate = (type: string) => {
  if (type === 'from') {
    search.value.date_request_goal = search.value.date_request_start
  }
}

watch(
  () => requestStore.openUpdateModal,
  (nowValue) => {
    if (nowValue) {
      openAddModal()
      requestStore.openUpdateModal = false
    }
  }
)

onMounted(async () => {
  await setSearchRequestByParams()
  await init()
  await cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [14] })
  if (
    action.value === 'createRequest' ||
    localStorage.getItem('pageAction') === 'createRequest'
  ) {
    await openAddModal(params.value)
    actionStore.resetAction()
    localStorage.removeItem('pageAction')
    requestStore.openUpdateModal = false
  } else {
    requestStore.setRequestSearch(null, 0)
  }

  // set page title
  setPageTitle('リクエスト一覧')
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          リクエスト一覧
        </q-toolbar-title>
        <div class="row mobile-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate
                v-model:date="search.date_request_start"
                :tabindex="1"
                autofocus
                label="開始日：Start"
                outlined
                type="date"
                @keydown.enter="moveNext"
                @update:date="changeDate('from')"
              />
              <MtFormInputDate
                v-model:date="search.date_request_goal"
                :tabindex="2"
                class="q-mx-sm"
                label="開始日：End"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <q-btn outline @click="openSearchModal">
                詳細検索
                <span
                  v-if="badgeCount > 0"
                  class="q-badge q-badge--floating q-badge--top q-badge--right text-white bg-red"
                >
                  {{ badgeCount }}
                </span>
              </q-btn>
              <q-btn
                class="q-mx-sm"
                color="grey-100"
                outline
                text-color="primary"
                unelevated
                @click="clearSearch()"
              >
                クリア
              </q-btn>
              <q-btn
                color="grey-800"
                tabindex="3"
                text-color="white"
                unelevated
                @click="searchData()"
              >
                <q-icon size="20px" name="search" />検索
              </q-btn>
              <q-btn
                class="q-ml-sm"
                color="grey-800"
                text-color="white"
                unelevated
                @click="openAddModal()"
              >
                <q-icon size="20px" name="add" />リクエスト
              </q-btn>
            </div>
          </div>
        </div>
        <div class="row desktop-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate
                v-model:date="search.date_request_start"
                autofocus
                class="q-mr-sm ipad-field-size-md"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <MtFormInputDate
                v-model:date="search.date_request_goal"
                class="ipad-field-size-md"
                outlined
                type="date"
                @keydown.enter="moveNext"
              />
              <q-btn class="q-ml-sm" outline @click="openSearchModal">
                <q-icon name="tune" />
                <q-badge
                  v-if="getAdditionalSearchCountRequest > 0"
                  color="red"
                  floating
                >
                  {{ getAdditionalSearchCountRequest }}
                </q-badge>
              </q-btn>
              <q-btn
                color="grey-800"
                text-color="white"
                unelevated
                class="q-mx-sm"
                @click="searchData()"
              >
                <q-icon size="20px" name="search" />
              </q-btn>
              <q-btn
                color="grey-800"
                text-color="white"
                unelevated
                @click="openAddModal()"
              >
                <q-icon size="20px" name="add" />
              </q-btn>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>
    <div class="row items-center justify-between q-px-lg tableBox">
      <div class="body1 regular text-grey-700">
        検索結果：<span class="q-ml-sm"> {{ getRequests.length }} 件</span>
      </div>
    </div>
    <MtTable2
      :columns="columns"
      :rows="requestsList"
      :rowsBg="true"
      :style="{
        height: 'calc(100dvh - 90px)',
        touchAction: 'pan-y' 
      }"
      flat
      virtual-scroll
      :virtual-scroll-item-size="48"
      :virtual-scroll-sticky-size-start="48"
      class="table-container"
    >
      <template v-slot:row="{ row }">
        <td
          v-for="(col, index) in columns"
          :key="index"
          :class="{
            flg_cancel_row: row.flg_cancel,
            flg_complete_row: row.flg_complete
          }"
          class="text-grey-900"
          :style="col.style"
          v-memo="[row.id_request, row.flg_complete, row.flg_cancel]"
          @click="onRowClick(row)"
        >
          <template v-if="col.field === 'number_request'">
            {{ row['number_request'] }}
          </template>
          <template v-else-if="col.field === 'name_customer'">
            <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(row)" show-customer-code />
          </template>
          <template v-else-if="col.field === 'date_request_start'">
            {{ row['date_request_start'] }}
          </template>
          <template v-else-if="col.field === 'date_request_goal'">
            {{ row['date_request_goal'] }}
          </template>
          <template
            v-else-if="col.field == 'title_request'"
            class="ellipsis"
            style="width: 300px"
          >
            <div class="ellipsis" style="width: 300px">
              {{ row['title_request'] }}
            </div>
          </template>
          <template v-else-if="col.field === 'id_employee_doctor'">
            {{ getEmployeeLabel(row['id_employee_doctor']) }}
          </template>
          <template v-else-if="col.field === 'flg_complete'">
            <div class="text-green">
              <q-icon v-if="row[col.field]" size="24px" name="check_circle" />
            </div>
          </template>
          <template
            v-else-if="col.field === 'open_request_detail'"
          >
            <div
              class="col-sm-1 col-xs-1"
              @click.stop="openRequestDetail(row)"
            >
              <q-icon size="24px" name="open_in_new" />
            </div>
          </template>
          <template v-else>
            <div class="body1 regular text-grey-900">
              {{ row[col.field] }}
            </div>
          </template>
        </td>
      </template>
    </MtTable2>
  </q-page>
</template>

<style lang="scss" scoped>
.tableBox {
  margin-top: 20px;
}

.table-container :deep(.q-markup-table.q-table__container) {
  max-width: 100%;
  overflow-x: auto;
  overscroll-behavior-x: none;
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
  contain: strict;
}
</style>
