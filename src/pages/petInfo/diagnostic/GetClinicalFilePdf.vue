<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import PdfExport from '@/pages/pdfExport/PdfExport.vue'
import { date } from 'quasar'

import useCustomerStore from '@/stores/customers'
import useClinicStore from '@/stores/clinics'
import { storeToRefs } from 'pinia'

import {
  ClinicType,
  PetType
} from '@/types/types'
import {
  getImage,
  getFullPetName,
  getCurrentPetAge,
  concatenate
} from '@/utils/aahUtils'
import { 
  typePetGender 
} from '@/utils/enum'

const emits = defineEmits(['close'])
const close = () => emits('close')

const customerStore = useCustomerStore()
const { getCustomer } = storeToRefs(customerStore)
const clinicStore = useClinicStore()

interface Props {
  callback?: (pdfBlob: Blob, pdfFileName: string) => void,
  idPet: number,
  imageUrl: string
}

const props = withDefaults(defineProps<Props>(), {
  idPet: '',
  imageUrl: ''
})

const exportPdf = ref()
const clinicData = ref<ClinicType | null>(null)
const petData = ref<PetType | null>(null)
const imageData = ref('')

const fetchClinic = async () => {
  const idClinic = JSON.parse(localStorage.getItem('id_clinic'))
  let clinic:any = await clinicStore.fetchClinicById(idClinic)
  return clinic
}

const getPetGenderType = () => typePetGender.find((v: any) => v.value == petData.value?.type_pet_gender)?.label

const generateReport = () => {
  let imagePDFOptions: any = { quality: 0.85 }, jsPDFOptions: any = { format: 'a5' }, pagesNumber: Number = 0
  if(props.callback){
    exportPdf.value.getPdfBlob(getPdfName(), pagesNumber, jsPDFOptions, imagePDFOptions).then((blob: Blob) => {
      props.callback(blob, `${getPdfName()}.pdf`)
    })
  } else {
    exportPdf.value.generateReport(getPdfName(), pagesNumber, jsPDFOptions, imagePDFOptions)
  }
}

const getPdfName = () => {
  return `${date.formatDate(Date.now(), 'YYYYMMDD')}_cf_${getFullPetName(petData.value, getCustomer.value)}`
}

const init = async () => {
  await nextTick()
  const { imageUrl } = props
  clinicData.value = await fetchClinic()
  await nextTick()
  
  const promises = [
    getImage(imageUrl),
    clinicData.value?.logo_file_path1 ? getImage(clinicData.value.logo_file_path1) : Promise.resolve(null),
    clinicData.value?.logo_file_path2 ? getImage(clinicData.value.logo_file_path2) : Promise.resolve(null)
  ]

  const [cfImagePath, logoPath1, logoPath2] = await Promise.allSettled(promises)
  clinicData.value.logo_file_path1 = logoPath1.status === 'fulfilled' ? logoPath1.value : null
  clinicData.value.logo_file_path2 = logoPath2.status === 'fulfilled' ? logoPath2.value : null
  imageData.value = cfImagePath.status === 'fulfilled' ? cfImagePath.value : null

  await nextTick()
  generateReport()
  close()
}

onMounted(() => {
  const { idPet } = props
  const pet = getCustomer.value.pets.find((pet: PetType) => pet.id_pet === idPet)
  if(pet) {
    petData.value = pet 
  }
  init()
})
</script>
<template>
  <div class="q-pa-md page-inner-body">
    <PdfExport :generateOnePDF="false" ref="exportPdf" />
    <q-card id="element-to-print" style="max-width: 1000px; margin: auto" class="bg-white q-pa-none" square>
      <div class="card-pdf-main q-px-md q-pt-md" style="height: 792.96px; overflow: hidden">
        <div class="flex column justify-between" style="height: 100%;">
        <div class="flex justify-between font-12px">
          <img
            v-if="clinicData?.logo_file_path2 || clinicData?.logo_file_path1"
            :src="clinicData?.logo_file_path2 || clinicData?.logo_file_path1"
            :style="{
              maxWidth: '70px',
              height: '30px',
              objectFit: 'contain',
            }"
            class="q-mr-sm"
          />
          <div>
            <span>{{ getFullPetName(petData, getCustomer) }}</span>
            <span class="q-ml-md">{{ getCurrentPetAge(petData) }}</span>
            <span class="q-ml-md">{{ getPetGenderType() }}</span>
          </div>
        </div>
        <div style="flex-grow: 1; overflow: hidden;">
          <img :src="imageData" />
        </div>

        <div>
          <div class="font-10px">
            {{ concatenate(clinicData?.address_prefecture || '', clinicData?.address_city || '', clinicData?.address_other || '') }}
          </div>
          <div class="font-10px">
            <span>TEL: {{ clinicData?.phone1 }}</span>
          </div>
        </div>
        </div>
      </div>
    </q-card>
  </div>
</template>
<style src="../../pdfExport/style.scss" lang="scss" scoped></style>