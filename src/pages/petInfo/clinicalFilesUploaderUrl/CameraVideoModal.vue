<script setup lang="ts">
import { onMounted, ref, nextTick } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import { formatDateWithTime, getDateToday } from '@/utils/aahUtils'
import useClinicalFilesStore from '@/stores/clinical-files'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import { Loading } from 'quasar'

const emits = defineEmits(['close'])
const props = defineProps({
  pet_illness_history:Object,
  id_clinic: String,
  petSelected: Object,
  modalClosedCallback: Function
})

const clinicalFilesStore = useClinicalFilesStore()

const isCameraOpen = ref(false)
const isPhotoTaken = ref(false)
const isShotPhoto = ref(false)
const isLoading = ref(false)
const link = ref('#')
const camera = ref(null)
const canvas = ref(null)
const isRecording = ref(false);
// let mediaRecorder = null;
// let recordedChunks = [];
const mediaRecorder = ref(null);
const recordedChunks = ref([]);
const showRecording = ref(false);
const recordedVideoUrl = ref("");
const recordedVideoBlob = ref(null);
const facingMode = ref("environment");
const capturedPhotos = ref([])
const imagesContainerRef = ref()

const closeModal = () => {
  console.log("------------ closeModal ------------");
  stopCameraStream()
  stopRecording()
  emits('close');
}

function toggleCamera() {
  if(isCameraOpen.value) {
    isCameraOpen.value = false;
    isPhotoTaken.value = false;
    isShotPhoto.value = false;
    stopCameraStream();
  } else {
    isCameraOpen.value = true;
    createCameraElement();
  }
}
   
function switchCamera() {
  facingMode.value = facingMode.value === 'environment' ? 'user' : 'environment';
  createCameraElement();
}

function createCameraElement() {
  isLoading.value = true;
  showRecording.value = false;
  
  const constraints = {
    audio: false,
    video: { 
      facingMode: facingMode.value,
      width: { ideal: 2560 },
      height: { ideal: 1440 } 
    }
  }

  navigator.mediaDevices.getUserMedia(constraints).then(stream => {
    isLoading.value = false;
    camera.value.srcObject = stream;
  }).catch(error => {
    isLoading.value = false;
    alert("ERR : ブラウザ・カメラ設定によるエラーで処理を継続できません。");
  });
}
    
function stopCameraStream() {
  try{
    let tracks = camera.value.srcObject.getTracks();
    tracks.forEach(track => {
      track.stop();
    });
  }catch(error){
  }
}
    
function takePhoto() {
  if(!isPhotoTaken) {
    isShotPhoto.value = true;

    const FLASH_TIMEOUT = 50;

    setTimeout(() => {
      isShotPhoto.value = false;
    }, FLASH_TIMEOUT);
  }
  
  const video_ = camera.value;
  const canvas_ = canvas.value;
  canvas_.width = video_.videoWidth;
  canvas_.height = video_.videoHeight;
  console.log("Video resolution:", video_.videoWidth, video_.videoHeight);
  
  setTimeout(() => {
    const context = canvas.value.getContext('2d');
    context.drawImage(video_, 0, 0, video_.videoWidth, video_.videoHeight);

    const photoData = canvas_.toDataURL('image/png')
    capturedPhotos.value.push(photoData)
  }, 100)

  isPhotoTaken.value = true
  nextTick(() => {
    const el = imagesContainerRef.value
    if (el) {
      nextTick(() => {
        const scrollTarget = el.getScrollTarget()
        el.setScrollPosition("vertical", scrollTarget.scrollHeight, 300)
      })
    }
  })
}
    
function downloadImage() {
  const download = document.getElementById("downloadPhoto");
  const canvas_ = document.getElementById("photoTaken").toDataURL("image/jpeg").replace("image/jpeg", "image/octet-stream");
  download.setAttribute("href", canvas_);
  download?.click()
}

async function submit(){
  const currentDateTime = formatDateWithTime(getDateToday(),'YYYY/MM/DD HH:mm:ss')
  const filename = `${props.petSelected.id_customer}_${currentDateTime}`
  const data = {
    id_customer: props.petSelected.id_customer,
    id_pet: props.petSelected.id_pet,
    name_pet: props.petSelected.name_pet,
    id_clinic: props.id_clinic,
    id_pet_illness_history: props.pet_illness_history.id_pet_illness_history,
    name_file: capturedPhotos.value.length ? filename + '.jpg' : filename + '.mp4',
    file_path: null,
    file_index: 0,
    type_file: 1,
    memo_file_storage: "カメラから撮影",
    name_supplier_other: '',
    datetime_receive: currentDateTime,
    type_receive_format: 1,
    type_diagnostic_info: 2,
  }
  if(capturedPhotos.value.length > 0) {
    const imagePromises = []
    capturedPhotos.value.forEach((base64Image: string) => {
      let imagePayload = {
        ...data,
        type_file: 1,
        file_path: convertBase64ToBlob(base64Image)
      }
      imagePromises.push(clinicalFilesStore.submitClinicalFile(imagePayload))
    })
    Loading.show({
      backgroundColor: 'transparent',
      spinnerColor: 'black',
      message: 'アップロードしています...',
      messageColor: 'black'
    })
    try {
      await Promise.all(imagePromises)
    } catch(err) {
      console.log('Error uploading files')
    } finally {
      Loading.hide()
    }
    clinicalFilesStore.fetchClinicalFiles({
      id_pet: data.id_pet
    })
    props.modalClosedCallback(clinicalFilesStore.recentClinicalFile.data)
    closeModal()
    mtUtils.autoCloseAlert(aahMessages.success)
  }else{
    data.file_path = recordedVideoBlob.value
    data.type_file = 2
    clinicalFilesStore.submitClinicalFile(data).then(() => {
      clinicalFilesStore.fetchClinicalFiles({
        id_pet: data.id_pet
      })
      props.modalClosedCallback(clinicalFilesStore.recentClinicalFile.data)
      emits('close')
      mtUtils.autoCloseAlert(aahMessages.success)
    })
  }
}


function startRecording() {
  const stream = camera.value.srcObject;
  const options = { mimeType: 'video/webm;codecs=vp9,opus' };
  mediaRecorder.value = new MediaRecorder(stream,options);
  
  mediaRecorder.value.ondataavailable = function(event) {
    if (event.data.size > 0) {
      recordedChunks.value.push(event.data);
    }
  };
  
  mediaRecorder.value.onstop = function() {
    const blob = new Blob(recordedChunks.value, { type: 'video/webm;codecs=vp9,opus' });
    const url = URL.createObjectURL(blob);
    recordedVideoBlob.value = blob;
    recordedVideoUrl.value = url;
    
    const videoElement = document.createElement('video');
    videoElement.id = 'recordedVideo'
    videoElement.style = "width: 100%; height: 100%;"
    videoElement.controls = true; // Show video controls
    videoElement.src = recordedVideoUrl.value;

    const videoContainer = document.getElementById('videoContainer')
    const btnEle = videoContainer.getElementsByTagName('button')[0]

    videoContainer.insertBefore(videoElement, btnEle);


    // Do something with the recorded video URL, like display it or upload it
    recordedChunks.value = [];
  };
  
  mediaRecorder.value.start();
  isRecording.value = true;
}

function stopRecording() {
  if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
    mediaRecorder.value.stop();
    isRecording.value = false;
    showRecording.value = true;
  }
  stopCameraStream()

}

function discordRecordedVideo(){
  mediaRecorder.value = null;
  recordedChunks.value = [];
  showRecording.value = false;
  recordedVideoUrl.value = '';
  recordedVideoBlob.value = null;
  isCameraOpen.value = false;
  isPhotoTaken.value = false;
  isShotPhoto.value = false;
  stopCameraStream();
  if(document.getElementById('recordedVideo')){
    document.getElementById('recordedVideo')?.remove()
  }
}

const deleteFile = (index: number) => {
  capturedPhotos.value.splice(index, 1)
  if(capturedPhotos.value.length === 0) {
    isPhotoTaken.value = false
  }
}

const convertBase64ToBlob = (base64String: string, contentType = 'image/png') => {
  const decodedString = atob(base64String.split(',')[1])
  const bytesArray = []

  for(let i = 0; i < decodedString.length; i += 512) {
    const chunks = decodedString.slice(i, i + 512)
    const byteNumbers = new Array(chunks.length)

    for (let j = 0; j < chunks.length; j++) {
      byteNumbers[j] = chunks.charCodeAt(j)
    }

    const byteArray = new Uint8Array(byteNumbers)
    bytesArray.push(byteArray)
  }

  return new Blob(bytesArray, { type: contentType })
}

onMounted(() => {
 
})
</script>

<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="text-grey-900 title2 bold">
      画像・動画の撮影
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section :class="{ 'content': isCameraOpen}" >
    <div class="flex justify-center">
      <div id="app" class="web-camera-container" v-if="!showRecording">
        <template v-if="isCameraOpen">
          <div class="flex items-center gap-2">
            <div v-show="!isLoading" class="camera-box" :class="{ 'flash' : isShotPhoto }">
              <div class="camera-shutter" :class="{'flash' : isShotPhoto}"></div>
              <video v-show="!isPhotoTaken" ref="camera" :style="{width: '100%', height: '100%', objectFit: 'cover'}" autoplay></video>
              <canvas v-show="isPhotoTaken" id="photoTaken" ref="canvas" :style="{width: '100%', height: '100%', objectFit: 'cover'}"></canvas>
            </div>
            <q-scroll-area 
              v-if="capturedPhotos.length"
              style="height: calc(100vh - 300px); width: 100%; max-width: 200px;"
              class="images-container"
              ref="imagesContainerRef">
              <template v-for="(photo, index) in capturedPhotos" :key="index">
                <div class="relative-position">
                  <img :src="photo" />
                  <q-badge color="red" floating transparent class="cursor-pointer"
                    @click="deleteFile(index)">
                    <q-icon name="close" />
                  </q-badge>
                </div>
              </template>
            </q-scroll-area>
          </div>
        </template>    
        <div class="flex justify-center items-center">
          <div v-if="!isCameraOpen" class="camera-button q-ml-md q-mt-sm">
            <button
              type="button" 
              class="cursor-pointer is-rounded q-mt-lg" 
              :class="{ 'primary' : !isCameraOpen}" 
              @click="toggleCamera"
              :disabled="isRecording"
            >
              <span >カメラ起動</span>
            </button>
          </div>          
          <span v-show="isCameraOpen && isLoading" class="col camera-loading">
            <ul class="loader-circle">
              <li></li>
              <li></li>
              <li></li>
            </ul>
          </span>
        </div>
      </div>
      <div
        v-if="showRecording"
        style="height: calc(100vh - 300px);"
        :style="{width: '100%', objectFit: 'cover' }"
        id="videoContainer"
      >
      </div>
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white row justify-center gap-5">
    <q-btn
      v-if="showRecording"
      label="削除する"
      @click="discordRecordedVideo"
    />
    <div v-if="isCameraOpen && !isLoading && !showRecording" class="camera-shoot row">
      <q-btn 
        class="is-rounded is-danger" 
        @click="toggleCamera"
        :disabled="isRecording"
      >
        <q-icon name="highlight_off" size="30px" />
      </q-btn>
      <q-btn class="q-ml-md" @click="takePhoto" :disabled="isRecording">
        <q-icon name="photo_camera" size="30px"></q-icon>
      </q-btn>
      <q-btn
        v-if="capturedPhotos.length"
        class="q-ml-md h-full"
        color="primary"
        @click="isPhotoTaken = false"
      >
        撮影
      </q-btn>
    </div>
    <div v-if="isCameraOpen">
    <template v-if="!capturedPhotos.length">
      <q-btn
        class="q-ml-md full-height"
        label="録画 開始"
        @click="startRecording"
        :disabled="isRecording || isPhotoTaken"
      />
      <q-btn
        class="q-ml-md full-height"
        label="録画 終了"
        @click="stopRecording"
        :disabled="!isRecording"
      />
    </template>
    <q-btn
      class="q-ml-md full-height"
      color="primary"
      text-color="white"        
      @click.stop="switchCamera()"
      :disabled="isRecording || isPhotoTaken"
    >
      <q-icon name="flip_camera_android" size="32px" />
      </q-btn>
    </div>
    <div class="text-center modal-btn">
      <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
        <span>キャンセル</span>
      </q-btn>
      <q-btn 
        unelevated 
        color="primary" 
        class="q-ml-md" 
        @click="submit"
        :disable="!((isPhotoTaken || showRecording) && isCameraOpen)"
      >
        <span>保存</span>
      </q-btn>
    </div>
  </q-card-section>
</template>

<style lang="scss">
.camera-modal-container .popup-scroll-area {
  height: max-content !important;
}
</style>
<style scoped lang="scss">

body {
  display: flex;
  justify-content: center;
}

.is-rounded{
  border-radius: 290486px;
  padding: 1em;
}

.is-danger{
  background-color: #ff3860;
  border-color: transparent;
  color: #fff;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.web-camera-container {
  // margin-top: 2rem;
  // margin-bottom: 2rem;
  // padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  height: fit-content;
  
  .camera-button {
    margin-bottom: 2rem;
  }
  
  .camera-box {   
    width: 100%;
    height: calc(100vh - 300px);
    position: relative;
    flex: 1;
    .camera-shutter {
      opacity: 0;
      // width: 700px;
      // height: 530px;
      width: 100%;
      height: 100%;
      inset: 0;
      background-color: #fff;
      position: absolute;
      
      &.flash {
        opacity: 1;
      }
    }
  }
  
  .camera-shoot {
    margin: 1rem 0;
    
    button {
      height: 60px;
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 100%;
      
      img {
        height: 35px;
        object-fit: cover;
      }
    }
  }
  
  .camera-loading {
    overflow: hidden;
    height: 100%;
    position: absolute;
    width: 100%;
    min-height: 150px;
    margin: 3rem 0 0 -1.2rem;
    
    ul {
      height: 100%;
      position: absolute;
      width: 100%;
      z-index: 999999;
      margin: 0;
    }
    
    .loader-circle {
      display: block;
      height: 14px;
      margin: 0 auto;
      top: 50%;
      left: 100%;
      transform: translateY(-50%);
      transform: translateX(-50%);
      position: absolute;
      width: 100%;
      padding: 0;
      
      li {
        display: block;
        float: left;
        width: 10px;
        height: 10px;
        line-height: 10px;
        padding: 0;
        position: relative;
        margin: 0 0 0 4px;
        background: #999;
        animation: preload 1s infinite;
        top: -50%;
        border-radius: 100%;
        
        &:nth-child(2) {
          animation-delay: .2s;
        }
        
        &:nth-child(3) {
          animation-delay: .4s;
        }
      }
    }
  }

  @keyframes preload {
    0% {
      opacity: 1
    }
    50% {
      opacity: .4
    }
    100% {
      opacity: 1
    }
  }
}


#videoContainer{
  text-align: center;
  justify-content: center;
}

#recordedVideo{
  width: 100%;
  height: 100%;
}

canvas{
  box-shadow: none;
  -webkit-box-shadow: none
}
.canvas-container{
  box-shadow: none;
  -webkit-box-shadow: none
}

.images-container {
  img {
    width: 100%;
    max-width: 200px;
    max-height: 200px;
    object-fit: cover;
  }
}

</style>
