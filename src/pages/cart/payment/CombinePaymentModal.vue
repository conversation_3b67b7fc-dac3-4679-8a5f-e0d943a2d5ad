<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { Platform } from 'quasar'
import dayjs, { Dayjs } from 'dayjs'

import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormInputDateTime from '@/components/form/MtFormInputDateTime.vue'
import MtFormInputNumber2 from '@/components/form/MtFormInputNumber2.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'


import { isDateOutOfToday, getDateToday, getDateTimeNow, getDateByFormat } from '@/utils/aahUtils'
import mtUtils from '@/utils/mtUtils'
import { typePaymentStatus, timeHourMinute } from '@/utils/enum'
import aahValidations from '@/utils/aahValidations'
import { roundToNearestQuarterHour } from '@/utils/roundToNearestQuarterHour'

import useCliCommonStore from '@/stores/cli-common'
import useCartStore from '@/stores/carts'
import usePaymentStore from '@/stores/payment'


const cliCommonStore = useCliCommonStore()
const cartStore = useCartStore()
const paymentStore = usePaymentStore()

const { getCliCommonTypePaymentMethodList } = storeToRefs(cliCommonStore)
const { getCart, getRelatedCarts } = storeToRefs(cartStore)

const data = ref({
  type_payment_status: 1,
  type_payment_category: 1, //should be dynamic 
  carts: []
})

const now = dayjs()
const roundedTime = roundToNearestQuarterHour(now as Dayjs)

const datetime_payment_date = ref(getDateToday())
const datetime_payment_time = ref(roundedTime.format('HH:mm'))

const bulkPaymentForm = ref()
const isLoading = ref(false)
const isIpad = Platform.is.ipad

const typePaymentStatusList = ref(typePaymentStatus)

const cartBalanceList = computed(() =>
  data.value.carts.map((cart) => {
    const currentPaid = cart.payment_amount.reduce(
      (sum, p) => sum + (parseInt(p.amount) || 0),
      0
    )
    const totalPaid = (cart.total_amount_paid || 0) + currentPaid
    return (cart.bill || 0) - totalPaid
  })
)

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0
  }).format(value)
}

const onAddPaymentAmount = (cartIndex: number) => {
  const remainingBalance = cartBalanceList.value[cartIndex]

  const newPayment = {
    type_payment_method: '',
    amount: String(remainingBalance ?? 0)
  }
  data.value.carts[cartIndex].payment_amount.push(newPayment)
}

const onSyncTypePayment = () => {
  const firstType = data.value.carts[0]?.payment_amount[0]?.type_payment_method

  if (!firstType) return

  data.value.carts.forEach((cart) => {
    cart.payment_amount.forEach((payment) => {
      payment.type_payment_method = firstType
    })
  })
}

const onRemovePaymentAmount = (cartIndex: number, payment) => {
  // Change this to api call for remove payment amount or make it local still
  const paymentIndex = data.value.carts[cartIndex].payment_amount.findIndex(
    (paymentItem) => paymentItem === payment
  )
  if (paymentIndex !== -1) {
    data.value.carts[cartIndex].payment_amount.splice(paymentIndex, 1)
  }
}

const computedSumPaymentAmount = computed(() => {
  return data.value.carts.reduce((sum, cart) => {
    return (
      sum +
      cart.payment_amount.reduce((cartSum, payment) => {
        return cartSum + (parseInt(payment.amount) || 0)
      }, 0)
    )
  }, 0)
})

const computedCartBalance = computed(() => {
  return data.value.carts.reduce((sum, cart) => {
    const currentPaid = cart.payment_amount.reduce(
      (cartSum, payment) => cartSum + (parseInt(payment.amount) || 0),
      0
    )
    const totalPaid = (cart.total_amount_paid || 0) + currentPaid
    return sum + ((cart.bill || 0) - totalPaid)
  }, 0)
})

const hasSameTypePaymentMethod = computed(() => {
  const allMethods = data.value.carts
    .flatMap((cart) => cart.payment_amount.map((p) => p.type_payment_method))
    // .filter((v) => v !== '') // ignore empty ones 

  if (allMethods.length === 0) return false

  return allMethods.every((method) => method === allMethods[0])
})


const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

function shortenCartNumber(cartNumber: string): string {
  const parts = cartNumber.split('-')
  return parts.length > 1 ? `CT${parts[1]}` : cartNumber
}

const typePaymentMethodOptions = computed(() =>
  getCliCommonTypePaymentMethodList.value
    .filter((item) => !isDateOutOfToday(item?.date_start, item?.date_end))
    .map((i) => ({ value: i.code_func1, label: i.name_cli_common }))
)

const onBulkPaymentSubmit = async () => {
  try {
    const isValid = await bulkPaymentForm.value?.validate()

    if (!isValid) {
      // mtUtils.autoCloseAlert('Validation failed')
      console.warn('Validation failed')
      return
    }
    isLoading.value = true
    // data.value.datetime_payment = 
    data.value.datetime_payment = datetime_payment_date.value
    data.value.datetime_payment += ' ' + datetime_payment_time.value + ':00'
    data.value.datetime_bill = data.value.datetime_payment
    await paymentStore.createBulkPayment(data.value)
    // Optionally show success message or close modal
    await mtUtils.promiseAllWithLoader([
      cartStore.fetchCart(getCart.value.id_cart),
      paymentStore.fetchPaymentByCustomer({
        id_cart: getCart.value.id_cart,
        flg_upfront_ui: true,
        id_customer: getCart.value.id_customer,
        flg_refund_occurred: true,
        flg_refund_conducted: true
      }),
    ])
    await paymentStore.fetchPaymentsByCart(getCart.value.id_cart)
    isLoading.value = false
    closeModal()
  } catch (e) {
    console.error('Bulk payment failed', e)
    // Handle error (show toast, message, etc.)
  }
}

onMounted(async () => {
  try {
    await cartStore.fetchRelatedCarts(getCart.value.id_cart)
    data.value.carts = JSON.parse(JSON.stringify(getRelatedCarts.value)).map((cart) => {
      const defaultTypePaymentMethod = typePaymentMethodOptions.value[0]?.value ?? ''
      return {
        ...cart,
        payment_amount: [
          {
            type_payment_method: defaultTypePaymentMethod,
            amount: cart.amount_balance > 0 ? String(cart.amount_balance ?? 0) : '0' // pre-fill with balance
          }
        ]
      }
    })
  } catch (e) {
    console.error('Failed to fetch related carts', e)
  }
})

</script>

<template>
  <q-card class="column full-height">
    <MtModalHeader @closeModal="closeModal" :closeBtn="true">
      <q-toolbar-title class="text-grey-900 title2 bold">
        まとめ入金
        <span class="q-ml-md font-regular"> 診察券番号: {{ getCart.number_cart }} {{ getCart.name_customer }} </span>
      </q-toolbar-title>
    </MtModalHeader>
    <q-scroll-area class="col-grow">
      <q-card-section class="q-pa-lg">
        <div id="combine-payment-box__header" class="row gap-2 q-mb-lg">
          <span class="col"> 会計番号 </span>
          <span class="col"> 会計日 </span>
          <span class="col"> 保険負担 </span>
          <span class="col"> 顧客請求(税込) </span>
          <span class="col"> 支払済額 </span>
          <span class="col-3 text-center"> 入金額 </span>
          <span class="col-3 text-center"> 入金方法 </span>
        </div>
        <q-form ref="bulkPaymentForm">
          <div
            v-for="(cart, cartIndex) in data.carts"
            :key="`${cartIndex}-${cart.number_cart}`"
            class="row gap-2">
            <div class="col text-h4 column justify-start">
              <section class="row items-center q-mb-sm" style="min-height: 56px">
                <span class="text-weight-bold q-mr-sm">会計{{ cartIndex + 1 }}</span>
                {{ shortenCartNumber(cart.number_cart) }}
              </section>
              <span
                v-if="cartBalanceList[cartIndex] !== 0"
                class="q-mt-auto text-red-10 text-subtitle1 row items-center gap-2">
                <q-icon color="red-10" name="error" size="sm" />
                {{ cartBalanceList[cartIndex] > 0 ? '残高' : '過入金' }}
                {{ formatCurrency(Math.abs(cartBalanceList[cartIndex])) }}
              </span>
            </div>
            <div class="col text-h4 column justify-start">
              <span class="row items-center" style="min-height: 56px">
                {{ getDateByFormat(cart.date_cart) }}
              </span>
            </div>
            <div class="col text-h4 column justify-start">
              <span class="row items-center" style="min-height: 56px"
                :class="cart.total_amount_insured > 0 ? 'highlight-insured' : ''">
                {{ formatCurrency(cart.total_amount_insured) }}
              </span>
            </div>
            <div class="col text-h4 text-weight-bold column justify-start">
              <span class="row items-center" style="min-height: 56px"> {{ formatCurrency(cart.bill) }} </span>
            </div>
            <div class="col text-h4 text-weight-bold column justify-start">
              <span
                class="row items-center"
                style="min-height: 56px"
                :class="{
                  'unpaid-amount': cart.total_amount_paid > 0 && cart.total_amount_paid < cart.bill,
                  'overpaid-amount': cart.total_amount_paid > cart.bill
                }">
                {{ formatCurrency(cart.total_amount_paid) }}
              </span>
            </div>

            <div class="col-3 column">
              <section
                v-for="(payment, index) in cart.payment_amount"
                :key="`${index}-amount`"
                class="title2 q-mb-sm row items-center">
                <span class="col-auto text-h3 q-ma-none"> ¥ </span>
                <MtFormInputNumber2
                  v-model:value="payment.amount"
                  :rules="[aahValidations.validationNonZeroNumber]"
                  input-class="text-h4 text-center text-weight-bold"
                  q-class="col row items-center"
                  outlined
                  autofocus
                  item-aligned
                  hide-bottom-space />
              </section>
              <span class="text-blue text-right text-subtitle1 cursor-pointer" @click="onAddPaymentAmount(cartIndex)">+
                Add PM</span>
            </div>
            <div class="col-3 column">
              <section
                v-for="(payment, index) in cart.payment_amount"
                :key="`${index}-payment_type`"
                class="row items-center"
                style="max-height: 56px">
                <MtFormPullDown
                  v-model:selected="payment.type_payment_method"
                  :options="typePaymentMethodOptions"
                  label="入金区分"
                  class="col-10"
                  :rules="[aahValidations.validationRequired]"
                  input-class="text-center text-h4" />
                <span v-if="cartIndex === 0 && index === 0" class="q-ml-auto">
                  <q-btn
                    round
                    flat
                    style="background: lightblue; color: #0057ff"
                    icon="sync"
                    size="sm"
                    @click="onSyncTypePayment()">
                    <q-tooltip> Sync PM1 to all </q-tooltip>
                  </q-btn>
                </span>
                <span v-if="index + 1 > 1" class="q-ml-auto">
                  <q-btn
                    round
                    flat
                    style="background: #ffcaca; color: #6b0000"
                    icon="close"
                    size="sm"
                    @click="onRemovePaymentAmount(cartIndex, payment)" />
                </span>
              </section>
              <div v-if="cartIndex + 1 == data?.carts.length && !hasSameTypePaymentMethod"
                class="q-mt-xl text-red-10 text-subtitle1">
                <q-icon color="red-10" name="error" size="sm" />
                支払い方法が会計単位で異なっていますが良いですか？
              </div>
            </div>
            <hr v-if="cartIndex + 1 < data?.carts.length" class="full-width q-my-md" />
          </div>
          <div class="sum-payment__box row gap-2 items-center q-pa-md q-mt-md">
            <div class="col-2">
              <!-- <MtFormInputDate v-model:date="form.dt_payment" type="date" label="入金日時" /> -->
              <!-- <MtFormInputDateTime
                v-model:date="data.datetime_payment"
                required
                :rules="[aahValidations.validationRequired]"
                label="入金日時" /> -->
              <MtFormInputDate
                v-model:date="datetime_payment_date"
                type="date"
                label="請求日時 *"
                required
                :rules="[aahValidations.validationRequired]" />
            </div>
            <div class="col-1 q-mr-xl">
              <MtFormPullDown
                type="selection"
                v-model:selected="datetime_payment_time"
                :options="timeHourMinute"
                label="時：分 *"
                required
                :rules="[aahValidations.validationRequired]" />
            </div>
            <div class="col-2">
              <MtFormPullDown
                v-model:selected="data.type_payment_status"
                :options="typePaymentStatusList"
                label="入金区分"
                :rules="[aahValidations.validationRequired]"
                input-class="text-center text-h4" />
            </div>
            <div class="col-1"></div>
            <div class="col font-40px text-bold">{{ formatCurrency(computedSumPaymentAmount) }}</div>
            <div class="col">
              {{ computedCartBalance >= 0 ? '残高' : '過入金' }}
              <span class="title2 bold q-ml-sm">
                {{ formatCurrency(Math.abs(computedCartBalance)) }}
              </span>
            </div>
          </div>
        </q-form>
      </q-card-section>
    </q-scroll-area>
    <q-card-actions class="q-bt q-pa-md modal-btn" align="center">
      <q-btn class="q-px-xl" outline label="閉じる" @click="closeModal" />
      <q-btn class="q-px-xl" color="primary" label="支払い登録のみ" :loading="isLoading" :disable="isLoading"
        @click="onBulkPaymentSubmit" />
      <!-- <q-btn class="q-px-xl" color="primary" label="登録して会計終了" @click="onBulkPaymentAndComplete" /> -->
    </q-card-actions>
  </q-card>
</template>

<style scoped>
.font-regular {
  font-weight: 500;
}

.font-40px {
  font-size: 40px;
}

.sum-payment__box {
  background: #fcffd2;
  border-radius: 10px;
}

.highlight-insured {
  background: #b7f4ff;
  color: hsl(206deg 100% 18.18%);
  border-radius: 5px;
  text-align: right;
  padding: 3px 8px;
}

.unpaid-amount {
  background: #ffc8c8;
  color: #3d0000;
  border-radius: 5px;
  text-align: right;
  padding: 3px 8px;
}

.overpaid-amount {
  background: #d0ffc8;
  color: #0c5100;
  border-radius: 5px;
  text-align: right;
  padding: 3px 8px;
}

</style>
