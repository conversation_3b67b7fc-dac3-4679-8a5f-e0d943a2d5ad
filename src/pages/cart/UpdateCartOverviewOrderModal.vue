<script setup lang="ts">
import { computed,  nextTick,  onMounted, onUnmounted, ref} from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import { formatDecimalNumber, formatNumber } from '@/utils/helper'
import MtCarousel from '@/components/MtCarousel.vue'
import _ from 'lodash';

/**
 * Interface representing a single date entry in the cart
 */
export interface DateEntry {
  /** The date string */
  date: string;
  /** Array of cart row items for this date */
  rowItems: CartRowItem[];
}

/**
 * Interface representing a pet's cart entries
 */
export interface PetEntry {
  /** Name of the pet */
  petName: string;
  /** Array of date entries containing cart items */
  dates: DateEntry[];
}

/**
 * Interface representing a single cart row item
 */
export interface CartRowItem {
  /** Unique identifier for the cart item */
  id: any;
  /** Type of cart item */
  type: string;
  /** Display order index */
  indexAsDisplayed: number;
  /** Name/description of the item */
  name: string;
  /** Price per unit */
  unitPrice: number;
  /** Quantity of items */
  quantity: number;
  /** Total price (unitPrice * quantity) */
  totalPrice: number;
  /** Whether pet insurance applies */
  flgPetInsurance: boolean;
  /** Tax type */
  typeTax: any;
  /** Optional group details if item is part of a group */
  groupDetails: {
    /** Group title */
    title: string;
    /** Unit sales amount */
    unitSales: number;
    /** Array of cart detail IDs in this group */
    cartDetailIds: any[];
  } | null;
  /** Optional prescription details if item is a prescription */
  prescriptionDetails: {
    /** Name of medicine */
    medicineName: string;
    /** Format/type of medicine */
    medicineFormat: string;
    /** Dosage amount */
    dosage: number;
    /** Number of days for prescription */
    totalDays: number;
    /** Total amount of medicine */
    totalAmount: number;
  } | null;
}

/**
 * Event emitter for modal actions
 */
const emits = defineEmits<{
  /** Emitted when modal is closed */
  (e: 'close'): void,
}>()

const props = defineProps<{
  cartData: {
    [petId: string]: PetEntry;
  };
  closeCallback: (saved: boolean, sortedCartData: {
    [petId: string]: PetEntry;
  }) => void;
  lastKnownOrder: {
    [petId: string]: PetEntry;
  };
}>()

const isSaved = ref<boolean>(false)

const toSortCartData = ref<{
  [petId: string]: PetEntry;
}>(_.cloneDeep(props.cartData))

// Add these refs for drag and drop functionality
const draggedItem = ref<{
  petId: string;
  dateIndex: number;
  itemIndex: number;
} | null>(null)

const dragOverItem = ref<{
  petId: string;
  dateIndex: number;
  itemIndex: number;
} | null>(null)

const dragDirection = ref<'before' | 'after' | null>(null)

const currentTabScrollIndex = ref<string>('0-pet')

const canReset = computed(() => {
  return !_.isEqual(toSortCartData.value, props.lastKnownOrder)
})

const tabScrollItems = computed(() => {
  // Create a flat array of pet names and dates (without year)
  const items: { label: string; value: string }[] = [];
  let counter: number = 0
  Object.values(toSortCartData.value).forEach(pet => {
    // Add pet name
    const petCounterValue = `${counter++}-pet`
    items.push({
      label: pet.petName,
      value: petCounterValue
    });
    
    // Add dates without year (assuming format is YYYY/MM/DD or similar)
    pet.dates.forEach((date) => {
      // Extract month and day from date string (remove year)
      const dateParts = date.date.split('/');
      if (dateParts.length >= 3) {
        items.push({
          label: `${dateParts[1]}/${dateParts[2]}`,
          value: `${petCounterValue}-${date.date}`
        }); // MM/DD format
      } else {
        items.push({
          label: date.date,
          value: `${petCounterValue}-${date.date}`
        });
      }
    });
  });
  
  return items;
})

// Function to handle drag start
const handleDragStart = (petId: string, dateIndex: number, itemIndex: number, event: DragEvent) => {
  // Store the dragged item information
  draggedItem.value = {
    petId,
    dateIndex,
    itemIndex
  }
  
  // Add a class to the dragged element for styling
  if (event.target) {
    const element = (event.target as HTMLElement).closest('.cart-row-item')
    if (element) {
      element.classList.add('dragging')
    }
  }
  
  // Set data for drag operation
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', `${petId}-${dateIndex}-${itemIndex}`)
  }
}

// Function to handle drag over
const handleDragOver = (petId: string, dateIndex: number, itemIndex: number, event: DragEvent) => {
  // Prevent default to allow drop
  event.preventDefault()
  
  if (!event.target) return
  
  // Get the element
  const element = (event.target as HTMLElement).closest('.cart-row-item')
  if (!element) return
  
  // Check if drag is allowed (same pet and date group)
  const isDragAllowed = draggedItem.value && 
                        draggedItem.value.petId === petId && 
                        draggedItem.value.dateIndex === dateIndex
  
  if (!isDragAllowed) {
    // Set cursor to not-allowed
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'none'
    }
    (element as HTMLElement).style.cursor = 'not-allowed'
    return
  } else {
    // Reset cursor and set move effect
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move'
    }
    (element as HTMLElement).style.cursor = 'grab'
  }
  
  // Store the current drag over item
  dragOverItem.value = {
    petId,
    dateIndex,
    itemIndex
  }
  
  // Get the bounding rectangle of the target element
  const rect = element.getBoundingClientRect()
  // Calculate the position of the mouse relative to the element
  const mouseY = event.clientY - rect.top
  // Determine if we should drop before or after based on mouse position
  dragDirection.value = mouseY < rect.height / 2 ? 'before' : 'after'
  
  // Add visual indicator classes
  element.classList.remove('drop-before', 'drop-after')
  element.classList.add(`drop-${dragDirection.value}`)
}

// Function to handle drag leave
const handleDragLeave = (event: DragEvent) => {
  // Remove visual indicator classes
  if (event.target) {
    const element = (event.target as HTMLElement).closest('.cart-row-item')
    if (element) {
      ;(element as HTMLElement).classList.remove('drop-before', 'drop-after')
      ;(element as HTMLElement).style.cursor = '' // Reset cursor
    }
  }
}

// Function to handle drop
const handleDrop = (petId: string, dateIndex: number, itemIndex: number, event: DragEvent) => {
  // Prevent default browser behavior
  event.preventDefault()
  
  // Remove visual indicator classes
  document.querySelectorAll('.drop-before, .drop-after, .dragging').forEach(el => {
    el.classList.remove('drop-before', 'drop-after', 'dragging')
  })
  
  // Only allow drop within the same pet and date group
  if (!draggedItem.value || 
      draggedItem.value.petId !== petId || 
      draggedItem.value.dateIndex !== dateIndex) {
    return
  }
  
  // Get the date entry
  const dateEntry = toSortCartData.value[petId].dates[dateIndex]
  
  // Don't do anything if trying to drop on itself
  if (draggedItem.value.itemIndex === itemIndex) {
    return
  }
  
  // Remove the dragged item from its current position
  const [draggedRowItem] = dateEntry.rowItems.splice(draggedItem.value.itemIndex, 1)
  
  // Calculate the new index based on the drop direction
  let newIndex = itemIndex
  if (dragDirection.value === 'after') {
    newIndex = itemIndex + (itemIndex > draggedItem.value.itemIndex ? 0 : 1)
  } else {
    newIndex = itemIndex - (itemIndex > draggedItem.value.itemIndex ? 1 : 0)
  }
  
  // Insert the dragged item at the new position
  dateEntry.rowItems.splice(newIndex, 0, draggedRowItem)
  
  // Reset drag state
  draggedItem.value = null
  dragOverItem.value = null
  dragDirection.value = null
}

// Function to handle drag end
const handleDragEnd = (event: DragEvent) => {
  // Remove the dragging class from all elements
  document.querySelectorAll('.dragging, .drop-before, .drop-after').forEach(el => {
    el.classList.remove('dragging', 'drop-before', 'drop-after')
    if (el instanceof HTMLElement) {
      el.style.cursor = '' // Reset cursor
    }
  })
  
  // Reset drag state
  draggedItem.value = null
  dragOverItem.value = null
  dragDirection.value = null
}

const handleSave = () => {
  nextTick(() => {
    isSaved.value = true
  }).then(() => {
    handleClose();
  })
}

const handleTabScroll = (value: string) => {
  nextTick(() => {
    const element = document.getElementById(value);
    if (element) {
      // Use scrollIntoView with custom options
      nextTick(() => {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }).then(() => {
        
      })
    }
  });
}

const handleReset = () => {
  toSortCartData.value = _.cloneDeep(props.lastKnownOrder)
}

const handleClose = () => {
  emits('close');
}

const handleMoveItem = (petId: string, dateIndex: number, itemIndex: number, direction: 'UP' | 'DOWN') => {
  const dateEntry = toSortCartData.value[petId].dates[dateIndex];
  
  if (direction === 'UP' && itemIndex > 0) {
    // Move item up
    const [movedItem] = dateEntry.rowItems.splice(itemIndex, 1);
    dateEntry.rowItems.splice(itemIndex - 1, 0, movedItem);
  } else if (direction === 'DOWN' && itemIndex < dateEntry.rowItems.length - 1) {
    // Move item down
    const [movedItem] = dateEntry.rowItems.splice(itemIndex, 1);
    dateEntry.rowItems.splice(itemIndex + 1, 0, movedItem);
  }
}

const expandedItems = ref<{[key: string]: boolean}>({})

const toggleItemExpand = (petId: string, dateIndex: number, itemIndex: number) => {
  const key = `${petId}-${dateIndex}-${itemIndex}`
  expandedItems.value[key] = !expandedItems.value[key]
}

onMounted( () => {
  toSortCartData.value = props.cartData
})

onUnmounted(() => {
  props.closeCallback(isSaved.value, toSortCartData.value)
})

</script>

<template>
  <q-form @submit="handleSave">
    <MtModalHeader @closeModal="handleClose">
      <q-toolbar-title class="text-grey-900 title2 bold flex justify-between">
        <div class="q-pt-sm">
          会計明細の順序変更
          <span class="caption1 regular text-grey-700 q-ml-md">
            ※ 日付クリックでスクロール / ドラッグ&ドロップで操作
          </span>
        </div>
        <q-btn outline color="primary" @click="handleReset()" :disable="!canReset">
          <span>リセット</span>
        </q-btn>
      </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="q-py-none q-px-md overflow-hidden">
      <div class="row sticky-header overflow-hidden">
        <MtCarousel :options="tabScrollItems" @update:model-value="(v: any) => handleTabScroll(v)" v-model="currentTabScrollIndex"  />
      </div>
    </q-card-section>
    <q-card-section class="q-pa-none scroll" style="max-height: 70vh; overflow-x: hidden !important;">
      <!-- Cart Items Here -->
      <div class="row full-width q-pa-md">
        <div v-for="(pet, petId, petIndex) in toSortCartData" :key="petId" class="pet-section q-mb-lg full-width">
          <div class="pet-header q-py-xs q-px-md text-bold bg-accent-200 text-grey-900 q-mb-sm" :id="`${petIndex}-pet`">
            {{ pet.petName }}
          </div>
          
          <div v-for="(dateEntry, dateIndex) in pet.dates" :key="dateIndex" class="date-section q-mb-md">
            <div class="date-header q-py-xs q-px-md bg-grey-200 text-grey-800 q-mb-sm" :id="`${petIndex}-pet-${dateEntry.date}`">
              {{ dateEntry.date }}
            </div>
            
            <div 
              v-for="(item, itemIndex) in dateEntry.rowItems" 
              :key="item.id"
              class="cart-row-item q-pa-sm q-mb-xs"
              :class="[
                { 'item-type-service': item.type === 'service' },
                { 'item-type-prescription': item.type === 'prescription' },
                { 'item-type-inject': item.type === 'inject' },
                { 'item-type-group': item.type === 'group' },
                { 'item-type-other': !['service', 'prescription', 'inject', 'group'].includes(item.type) },
                { 'insurance-applied': item.flgPetInsurance },
                { 'expanded': expandedItems[`${petId}-${dateIndex}-${itemIndex}`] }
              ]"
              draggable="true"
              @dragstart="(e) => handleDragStart(petId.toString(), dateIndex, itemIndex, e)"
              @dragover="(e) => handleDragOver(petId.toString(), dateIndex, itemIndex, e)"
              @dragleave="handleDragLeave"
              @drop="(e) => handleDrop(petId.toString(), dateIndex, itemIndex, e)"
              @dragend="handleDragEnd"
              @click="toggleItemExpand(petId.toString(), dateIndex, itemIndex)"
            >
              <div class="row items-center">
                <div class="col">
                  <div class="row justify-even items-center">
                      <div class="col">
                        <div class="text-weight-medium title2 bold">{{ item.type === 'group' ? item.groupDetails?.title : item.name }}</div>
                      </div>
                      <div class="col-auto">
                        <div class="row items-center q-mr-xl">
                          <q-badge v-if="item.flgPetInsurance" color="primary" class="q-ml-md" size="xs">
                            保険適用
                          </q-badge>
                          <div class="row text-caption text-grey-700">
                            <div class="col-auto q-ml-md body2 regular">
                              ¥{{ formatNumber(item.type === 'group' ? item.groupDetails?.unitSales : item.totalPrice) }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Expanded details -->
                  <div v-if="expandedItems[`${petId}-${dateIndex}-${itemIndex}`]" class="expanded-details q-mt-xs">
                    <div class="row text-caption text-grey-700 q-mb-xs">
                      <div class="col-auto q-mr-md body2 regular">
                        <span>¥{{ formatNumber(item.type === 'group' ? item.groupDetails?.unitSales : item.unitPrice) }}</span>
                        <span class="caption2"> x </span>
                        <span>{{ formatDecimalNumber(item.type === 'group' ? 1 : item.quantity) }}</span>
                      </div>
                    </div>
                    
                    <!-- Group details if available -->
                    <div v-if="item.groupDetails" class="text-caption q-mt-xs">
                      <div class="text-italic">グループ: {{ item.groupDetails.title }}</div>
                    </div>
                    
                    <!-- Prescription details if available -->
                    <div v-if="item.prescriptionDetails" class="text-caption q-mt-xs">
                      <div>{{ item.prescriptionDetails.medicineName }} ({{ item.prescriptionDetails.medicineFormat }})</div>
                      <div>
                        用量: {{ item.prescriptionDetails.dosage }},
                        日数: {{ item.prescriptionDetails.totalDays }},
                        総量: {{ item.prescriptionDetails.totalAmount }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="col-auto">
                  <div class="flex justify-end items-center">
                    <q-btn
                      class="cursor-pointer"
                      flat
                      icon="expand_less"
                      round
                      size="10px"
                      :disable="itemIndex === 0"
                      @click.stop="
                        handleMoveItem(petId.toString(), dateIndex, itemIndex, 'UP')
                      "
                    />
                    <q-btn
                      class="cursor-pointer"
                      flat
                      icon="keyboard_arrow_down"
                      round
                      size="10px"
                      :disable="itemIndex === dateEntry.rowItems.length - 1"
                      @click.stop="
                        handleMoveItem(petId.toString(), dateIndex, itemIndex, 'DOWN')
                      "
                    />
                    <q-icon name="drag_indicator" size="20px" class="cursor-move q-ml-sm" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-pa-md bg-white q-bt">
      <div class="text-center">
        <q-btn outline class="bg-grey-100 text-grey-800 q-mr-md" @click="handleClose()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" type="submit">
          <span>変更を確定</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.cart-row-item {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.dragging {
    opacity: 0.5;
    border: 2px dashed #ccc;
  }
  
  &.drop-before {
    border-top: 3px solid #1976d2;
  }
  
  &.drop-after {
    border-bottom: 3px solid #1976d2;
  }
  
  &.insurance-applied {
    border-left: 4px solid #1976d2;
  }
  
  &.item-type-service {
    border-left: 6px solid #eebedb;
  }
  
  &.item-type-prescription {
    border-left: 6px solid rgb(190, 204, 238);
  }
  
  &.item-type-inject {
    border-left: 6px solid rgb(205, 183, 242);
  }
  
  &.item-type-group {
    border-left: 6px solid #9e9e9e;
  }
  
  &.item-type-other {
    border-left: 6px solid #757575;
  }
  
  &.expanded {
    background-color: #f5f5f5;
  }
  
}

.cursor-move {
  cursor: move;
}

.scroll {
  overflow-y: auto;
}
</style>
