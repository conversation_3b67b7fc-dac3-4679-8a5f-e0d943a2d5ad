<script setup lang="ts">
import BarChart from '@/components/BarChart.vue'
import DoughnutChart from '@/components/DoughnutChart.vue'
import { computed, ref } from 'vue'

const props = defineProps({
  searchData: Object,
  customerSummary: Object
})

const selectedMonth = ref('')

const monthlyChartData = computed(() => {
  const monthly = props.customerSummary?.monthly || {}
  const labels = Object.keys(monthly)
  if (!selectedMonth.value && labels.length > 0) selectedMonth.value = labels[0]

  const newCustomers = labels.map(month => monthly[month]?.new_customers || 0)
  const newPets = labels.map(month => monthly[month]?.new_pets || 0)
  const newPetsExisting = labels.map(month => monthly[month]?.new_pets_existing_customers || 0)

  return {
    labels,
    datasets: [
      { label: '新規顧客', data: newCustomers, backgroundColor: 'rgba(255, 99, 132, 0.6)' },
      { label: '新規ペット', data: newPets, backgroundColor: 'rgba(54, 162, 235, 0.6)' },
      { label: '既存顧客の新規ペット', data: newPetsExisting, backgroundColor: 'rgba(255, 206, 86, 0.6)' }
    ]
  }
})

function generateUniqueColors(count: number, baseColors: string[] = []): string[] {
  const used = new Set(baseColors)
  const colors = [...baseColors]

  while (colors.length < count) {
    const color = `hsl(${Math.floor(Math.random() * 360)}, 70%, 80%)`
    if (!used.has(color)) {
      colors.push(color)
      used.add(color)
    }
  }

  return colors
}


const breedChartData = computed(() => {
  const petTypes = props.customerSummary?.pet_types || {}
  const labels = Object.keys(petTypes)
  const values = Object.values(petTypes)

  const baseColors = ['#ff99cc', '#ffcc00', '#66ccff', '#B39DDB', '#20B2AA', '#A0E7E5', '#FF7F7F']
  const finalColors = generateUniqueColors(labels.length, baseColors)

  return {
    labels,
    datasets: [
      {
        label: 'ペット登録数',
        data: values,
        backgroundColor: finalColors,
        hoverOffset: 4
      }
    ]
  }
})


const ageGroupChartData = computed(() => {
  const monthlyAgeGroups = props.customerSummary?.monthly_age_groups || {}
  const selected = selectedMonth.value
  const ageGroups = monthlyAgeGroups[selected] || {}
  const breeds = Object.keys(ageGroups)
  const ageBuckets = ['0-1yr', '1-5yr', '5+yr', '不明']

  const datasets = ageBuckets.map(bucket => ({
    label: bucket,
    data: breeds.map(breed => ageGroups[breed]?.[bucket] || 0),
    backgroundColor: getAgeGroupColor(bucket),
    stack: 'ageStack'
  }))

  return {
    labels: breeds,
    datasets
  }
})

function getAgeGroupColor(bucket: string): string {
  switch (bucket) {
    case '0-1yr': return 'rgba(255, 99, 132, 0.6)'
    case '1-5yr': return 'rgba(54, 162, 235, 0.6)'
    case '5+yr': return 'rgba(255, 206, 86, 0.6)'
    default: return 'rgba(200, 200, 200, 0.5)'
  }
}
</script>

<template>
  <div>
    <h3 class="chart-title">顧客・ペット登録概要</h3>

    <!-- Monthly Summary Bar Chart -->
    <div class="q-mb-lg report-background q-pa-md">
      <h5 class="chart-title">月別登録数</h5>
      <BarChart :chartData="monthlyChartData" />
    </div>

    <!-- Pet Types Doughnut Chart -->
    <div class="q-mb-lg report-background q-pa-md">
      <h5 class="chart-title">ペット種類別登録</h5>
      <DoughnutChart :chartData="breedChartData" :ageGroups="props.customerSummary?.age_groups || {}" />
    </div>

    <!-- Month Selector for Age Group Detail -->
    <div class="q-mb-md">
      <label class="q-mr-sm">月選択:</label>
      <select v-model="selectedMonth">
        <option v-for="month in Object.keys(props.customerSummary?.monthly || {})" :key="month" :value="month">
          {{ month }}
        </option>
      </select>
    </div>

    <!-- Age Breakdown per Breed for Selected Month -->
    <div class="q-mb-lg report-background q-pa-md">
      <h5 class="chart-title">年齢層別登録（種類別） - {{ selectedMonth }}</h5>
      <BarChart :chartData="ageGroupChartData" />
    </div>
  </div>
</template>

<style scoped>
.chart-title {
  text-align: center;
  color: #333;
  font-size: 16px;
  margin-bottom: 10px;
}

.report-background {
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #ddd;
}
</style>
