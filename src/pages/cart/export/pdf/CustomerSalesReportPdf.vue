<script setup lang="ts">
import { ref, nextTick } from 'vue'
import PdfExport from '@/pages/pdfExport/PdfExport.vue'
import useClinicStore from '@/stores/clinics'
import { formattedPrice } from '@/utils/helper'
import { dateFormat, getDateToday, filterRows } from '@/utils/aahUtils'

const emits = defineEmits(['close'])
const close = () => emits('close')

const props = defineProps({
  resultList: Array,
  dateParams: Object,
  columns: Array
})

const exportPdf = ref()
const clinicStore = useClinicStore()
const rows = ref([])

async function init() {
  await nextTick()
  const rowsData = [...props.resultList]
  rows.value = exportPdf.value.populateRows(rowsData, 28, 28)
  await nextTick()
  generateReport()
  close()
}

function generateReport() {
  const jsPDFOptions = { orientation: 'landscape' }
  const imagePDFOptions = { quality: 0.1 }
  const pagesNumber = 0
  exportPdf.value.generateReport(getFileName(), pagesNumber, jsPDFOptions, imagePDFOptions)
}

function getFileName() {
  const formattedDate = dateFormat(getDateToday(), 'YYMMDD')
  const formattedRange = `${dateFormat(props.dateParams.date_start, 'YYMMDD')}_${dateFormat(props.dateParams.date_end, 'YYMMDD')}`
  return `顧客別売上_${formattedRange}_${clinicStore.getClinic.name_clinic_display}(DL${formattedDate})`
}

init()
</script>

<template>
  <div>
    <PdfExport ref="exportPdf" />
    <div class="q-pa-md page-inner-body text-grey-900">
      <q-card id="element-to-print" class="bg-white q-pa-none" square style="max-width: 1200px; margin: auto">
        <div v-for="(page, idx) in rows" :key="idx" class="card-pdf-main q-px-md">
          <div class="flex justify-between q-mt-md">
            <div>
              <span class="title">顧客別売上集計</span>
              <span class="q-mx-sm">[ 期間 ]</span>
              <span>{{ props.dateParams.date_start + ' ~ ' + props.dateParams.date_end }}</span>
            </div>
            <div>
              <span class="title">{{ clinicStore.getClinic.code_clinic }} / {{ clinicStore.getClinic.name_clinic_display
                }}</span>
              <div class="font-10px text-right">
                <span class="text-grey-700 q-pr-sm">出力日:</span>
                {{ dateFormat(new Date(), 'YYYY/MM/DD HH:mm') }}
              </div>
            </div>
          </div>

          <div class="row full-width">
            <div class="col-12 relative-position" :style="{ 'min-height': '700px' }">
              <q-table
                :columns="props.columns"
                :rows="filterRows(page)"
                :rows-per-page-options="[page.length]"
                class="relative-position"
                flat
                hide-bottom
                row-key="id_pet"
                style="table-layout: fixed; width: 100%">
                <template v-slot:header="props">
                  <q-tr v-if="page.length" :props="props" style="background: transparent">
                    <q-th v-for="col in props.cols" :key="col.name" :props="props">
                      <div class="font-10px text-grey-800" style="padding: 2px 0">{{ col.label }}</div>
                    </q-th>
                  </q-tr>
                </template>

                <template v-slot:body="props">
                  <q-tr v-if="props.row.row_type === 'customer'" class="table-body-row">
                    <q-td> <strong class="font-10px">{{ props.row.customerName }}</strong> </q-td>
                    <q-td></q-td>
                    <q-td>
                      <div class="font-10px text-right">
                        <strong>{{ formattedPrice(props.row.summary?.total_invoice_amount || 0) }} </strong>
                      </div>
                    </q-td>
                    <q-td>
                      <div class="font-10px text-right" v-if="props.row.summary?.total_unpaid_amount > 0">
                        <strong>{{ formattedPrice(props.row.summary?.total_unpaid_amount) }}</strong>
                      </div>
                    </q-td>
                  </q-tr>

                  <q-tr v-else-if="props.row.row_type === 'pet'" class="table-body-row">
                    <q-td></q-td>
                    <q-td>
                      <div class="font-10px"> {{ props.row.pet.name_pet }}</div>
                    </q-td>
                    <q-td>
                      <div class="font-10px text-right"> {{ formattedPrice(props.row.pet.invoice_amount) }} </div>
                    </q-td>
                    <q-td>
                      <div class="font-10px text-right" v-if="props.row.pet.unpaid_amount > 0">
                        {{ formattedPrice(props.row.pet.unpaid_amount) }}
                      </div>
                    </q-td>
                  </q-tr>
                </template>
              </q-table>

              <div class="absolute-bottom full-width font-10px flex justify-between"
                style="border-top: 2px solid #000;">
                <div>Vetty電子カルテ 発行番号# {{ 'CMON' + clinicStore.getClinic.code_clinic }}{{ dateFormat(new Date(),
                  'YYYYMMDDHHmm') }}</div>
                <div>Page: {{ idx + 1 }} / {{ rows.length }}</div>
              </div>
            </div>
          </div>
          <template v-if="idx + 1 != rows.length">
            <div class="html2pdf__page-break"></div>
          </template>
        </div>
      </q-card>
    </div>
  </div>
</template>

<style src="@/pages/pdfExport/style.scss" lang="scss" scoped></style>
<style lang="scss" scoped>
.table-head {
  border-bottom: 2px solid #000;
}

.table-body-row>td.q-td {
  border-bottom: 1px dotted rgba(0, 0, 0, 0.12);
  height: 24px !important;
}
</style>
