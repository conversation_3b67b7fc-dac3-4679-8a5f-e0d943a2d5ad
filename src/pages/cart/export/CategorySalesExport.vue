<script lang="ts" setup>
import { exportFile } from 'quasar';
import MtModalHeader from '@/components/MtModalHeader.vue'
import { ref, onMounted } from 'vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import GetPdfPerCategorySalesReport from '@/pages/cart/GetPdfPerCategorySalesReport.vue'
import useCartStore from '@/stores/carts'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import { typeItem } from '@/utils/enum'
import { formattedPrice } from '@/utils/helper'
import * as Encoding from 'encoding-japanese';
import {
  dateFormat,
  getDateToday,
  getDateTimeNow
} from '@/utils/aahUtils'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import dayjs from 'dayjs'

const cartStore = useCartStore()
const clinicStore = useClinicStore()

const emits = defineEmits(['close'])
const props = defineProps({
  params: {}
})
const flgCompleted = ref(1);
const disableExport = ref(true)
const flgSortRows = ref(false)
const searchData = ref({
  date_start: dayjs().format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD'),
})

const columns = [
  { name: 'classification', label: '分類', field: 'classification', align: 'left', tooltip: `商品区分、サービス区分、大分類名を表示します。` }, // contain code and name category1
  { name: 'code_category2', label: '分類CD', field: 'code_category2', align: 'left', tooltip: `該当するカテゴリーに紐づくコードを表示します。` },
  { name: 'name_category2', label: '項目名', field: 'name_category2', align: 'left', tooltip: `該当する小分類に設定された項目名を表示します。` },
  { name: 'cd_count', label: '件数', field: 'cd_count', align: 'right', tooltip: `対象期間に、小分類項目に該当するカート明細の件数を表示します。` },
  { name: 'total_quantity', label: '数量', field: 'total_quantity', align: 'right', tooltip: `対象期間における、小分類項目ごとの商品販売数量です。` },
  { name: 'total_amount_sales', label: '会計明細合計 (税抜)', field: 'total_amount_sales', align: 'right', tooltip: `対象期間中における、小分類項目ごとの会計明細ベースの合計金額です。` },
  { name: 'average', label: '平均単価（税抜）', field: 'average', align: 'right', tooltip: `対象期間中における、小分類項目ごとの平均単価です。<br/> ※平均単価 ＝ 販売合計金額 ÷ 販売数量`  },
  { name: 'percentage', label: '比率(%)', field: 'percentage', align: 'right', tooltip: `対象期間内における、小分類項目ごとの販売金額が、全体販売金額に占める比率（％）です。<br/> ※比率％ ＝ 金額 ÷ その期間の全体販売金額` },
];

const rows = ref([]);
const summary = ref([])
const formattedRows = ref([]);

const csvColumns = [
  { name: 'clinic_display_order', label: '順序', field: 'clinic_display_order', align: 'left' },
  { name: 'code_clinic', label: '病院CD', field: 'code_clinic', align: 'left' },
  { name: 'name_clinic_display', label: '病院名', field: 'name_clinic_display', align: 'left' },
  { name: 'flg_service', label: '販売区分', field: 'flg_service', align: 'left' },
  { name: 'type_service_item', label: '大分類', field: 'type_service_item', align: 'left' },
  { name: 'name_category1', label: '中分類', field: 'name_category1', align: 'left' },
  { name: 'name_category2', label: '小分類', field: 'name_category2', align: 'left' },
  { name: 'cd_count', label: '件数', field: 'cd_count', align: 'right' },
  { name: 'total_quantity', label: '数量', field: 'total_quantity', align: 'right' },
  { name: 'total_amount_sales', label: '金額', field: 'total_amount_sales', align: 'right' },
  { name: 'ratio', label: '全体比率', field: 'ratio', align: 'right' },
  { name: 'datetime_export', label: '抽出日', field: 'datetime_export', align: 'right' },
];

const closeModal = () => {
  emits('close')
}

const typeItemName = (value: any) =>
  typeItem.find((v) => v.value === value)?.label


const processRows = () => {
  const root = [];
  const serviceMap = {};

  rows.value.forEach(row => {
    const serviceType = row.flg_service ? 'サービス' : '商品';
    const itemType = row.flg_service ? (row.id_cm_type_service || '未分類') : (typeItemName(row.type_item) || '未分類');
    const category1Key = `${row.code_category1} ${row.name_category1}`;

    if (!serviceMap[serviceType]) {
      serviceMap[serviceType] = { header: 1, label: serviceType, total_quantity: 0, total_amount_sales: 0, cd_count: 0, children: [] };
      root.push(serviceMap[serviceType]);
    }

    let itemTypeMap = serviceMap[serviceType].children.find(item => item.label === itemType);
    if (!itemTypeMap) {
      itemTypeMap = { header: 2, label: itemType, total_quantity: 0, total_amount_sales: 0, cd_count: 0, children: [] };
      serviceMap[serviceType].children.push(itemTypeMap);
    }

    let category1Map = itemTypeMap.children.find(cat => cat.label === category1Key);
    if (!category1Map) {
      category1Map = { header: 3, label: category1Key, total_quantity: 0, total_amount_sales: 0, cd_count: 0, children: [] };
      itemTypeMap.children.push(category1Map);
    }

    category1Map.children.push({
      header: 4,
      code_category2: row.code_category2,
      name_category2: row.name_category2,
      cd_count: row.cd_count,
      total_quantity: row.total_quantity,
      total_amount_sales: row.total_amount_sales,
      average: row.total_quantity > 0 ? Math.round(parseInt(row.total_amount_sales) / row.total_quantity) : 0,
      percentage: `${((row.total_amount_sales / summary.value.total_amount) * 100).toFixed(2)}%`
    });

    // Aggregate totals
    category1Map.cd_count += row.cd_count;
    category1Map.total_quantity += row.total_quantity;
    category1Map.total_amount_sales += row.total_amount_sales;

    itemTypeMap.cd_count += row.cd_count;
    itemTypeMap.total_quantity += row.total_quantity;
    itemTypeMap.total_amount_sales += row.total_amount_sales;

    serviceMap[serviceType].cd_count += row.cd_count;
    serviceMap[serviceType].total_quantity += row.total_quantity;
    serviceMap[serviceType].total_amount_sales += row.total_amount_sales;
  });

  formattedRows.value = root; // The top-level structure for rendering in the table
};

const sortRows = () => {
  const sortWithUnclassifiedAtBottom = (a, b) => {
    if (a.label === '未分類') return 1;
    if (b.label === '未分類') return -1;
    return b.total_amount_sales - a.total_amount_sales;
  };

  if (flgSortRows.value) {
    // Sort each level while maintaining the hierarchical structure
    formattedRows.value.forEach(service => {
      service.children.sort(sortWithUnclassifiedAtBottom);
      service.children.forEach(itemType => {
        itemType.children.sort(sortWithUnclassifiedAtBottom);
        itemType.children.forEach(category1 => {
          category1.children.sort(sortWithUnclassifiedAtBottom);
        });
      });
    });
  } else {
    // Reset to original order by re-processing the rows
    processRows();
  }
};


const flattenedRows = () => {
  const totalAmountSales = summary.value.total_amount || 1;  // Ensure no division by zero
  return rows.value.map(detail => {
    const ratio = ((detail.total_amount_sales / totalAmountSales) * 100).toFixed(2) + '%';
    let flg_service = '';
    let type_service_item = '';
    if (detail.flg_service) {
      flg_service = 'サービス';
      type_service_item = detail.id_cm_type_service;
    } else {
      flg_service = '商品';
      type_service_item = typeItemName(detail.type_item)?? '-';
    }
    const datetime_export = getDateTimeNow();
    return {
      ...detail,
      flg_service,
      ratio,
      type_service_item,
      datetime_export
    };
  });
}

const exportCSV = () => {
  // Calculate total amount of sales

  // Flatten the nested structure and calculate ratio
  const data = flattenedRows()

  const csvContent = [
    csvColumns.map(col => col.label).join(','),  // Header row
    ...data.map(row => csvColumns.map(col => row[col.field]).join(','))  // Data rows
  ].join('\n');

  // Convert CSV content to Shift-JIS encoding
  const shiftJISArray = Encoding.stringToCode(csvContent);
  const shiftJIS = Encoding.convert(shiftJISArray, {
    to: 'SJIS',
    from: 'UNICODE',
  });
  const uint8Array = new Uint8Array(shiftJIS);
  const blob = new Blob([uint8Array], { type: 'text/csv;charset=shift-jis;' });

  // Generate the file name using dateFormat and getDateToday
  const formattedDate = dateFormat(getDateToday(), 'YYYYMMDD');
  const formattedSelectedDayRange = `${dateFormat(props.params.date_start, 'YYMMDD')}_${dateFormat(props.params.date_end, 'YYMMDD')}`;
  const fileName = `分類別集計（税別）_${formattedSelectedDayRange}_${clinicStore.getClinic.name_clinic_display}(出力${formattedDate}).csv`;

  // Trigger file download
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', fileName);
  link.style.display = 'none';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  const status = true; // Simulating the file export status
  if (status !== true) {
    console.error('Error exporting file');
  }
};

const copytoclipboard = () => {
  // Calculate total amount of sales

  // Flatten the nested structure and calculate ratio
  const data = flattenedRows()

  const clipboardData = [
    csvColumns.map(col => col.label).join('\t'),  // Header row
    ...data.map(row => csvColumns.map(col => row[col.field]).join('\t'))  // Data rows
  ].join('\n');


  navigator.clipboard.writeText(clipboardData).then(
    () => {
      mtUtils.autoCloseAlert('コピーしました！')
    },
    (err) => {
      console.error('Error copying CSV content to clipboard', err)
    }
  )
};

const exportPDF = () => {
  mtUtils.pdfRender(GetPdfPerCategorySalesReport, {
    resultList: [...formattedRows.value],
    dateParams: {
      date_start: props.params.date_start,
      date_end: props.params.date_end
    },
    summaryData: {...summary.value}
  })
};

const fetchSalesSummary = async (newDate: boolean = false) => {
  
  // set date 
  if (!newDate) {
    searchData.value.date_start = props.params.date_start
    searchData.value.date_end = props.params.date_end
  } 
  
  
  
  // Create queryParams object to hold all request parameters
  const queryParams = {
    ...props.params,
    flg_completed: flgCompleted.value, // Pass the flg_completed value
    date_start: searchData.value.date_start,
    date_end: searchData.value.date_end
    
  };
  // console.log('queryParams', queryParams);
  formattedRows.value = []
  await cartStore.fetchCategorySalesSummary(queryParams).then(res => {
    rows.value = res.data.data.cart_details
    summary.value = res.data.data.summary
    disableExport.value = false
    processRows();
  })
};

const handleFlgCompletedChange = async () => {
  await fetchSalesSummary(); // Re-fetch the data on change
};
const contentRef = ref()
const scrollToTop = () => {
  if (contentRef.value) {
    contentRef.value.$el.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
const updateDateStart = () => {
  searchData.value.date_end = searchData.value.date_start
  fetchSalesSummary(true)
}

onMounted(async () => {
 await fetchSalesSummary()
})

</script>
<template>
  <div style="width: calc(100vw - 50px); overflow-x: hidden;" >
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          集計：分類別
        </q-toolbar-title>
        <div class="flex items-center">
          <MtFormInputDate
            v-model:date="searchData.date_start"
            outlined
            label="会計日：Start"
            type="date"
            autofocus
            tabindex="1"
            @update:date="updateDateStart()"
          />
          <MtFormInputDate
            v-model:date="searchData.date_end"
            outlined
            class="q-mx-sm"
            type="date"
            label="会計日：End"
            tabindex="2"
            @update:date="() => fetchSalesSummary(true)"
          />
          <MtFormRadiobtn v-model="flgCompleted" label="完了会計のみ" :val="1"  @update:modelValue="handleFlgCompletedChange" />
          <MtFormRadiobtn v-model="flgCompleted" label="未完のみ" :val="0"  @update:modelValue="handleFlgCompletedChange" class="q-mr-md" />
          <MtFormCheckBox
            v-model:checked="flgSortRows"
            @update:checked="sortRows"
            label="集計額順"
          />
        </div>
      </q-toolbar>
    </MtModalHeader>
    <q-card-section class="q-px-lg content" style="overflow-x: auto;" ref="contentRef">
      <div class="header-info">
        <div class="left">
          <span class="title">集計期間</span>
          <span class="q-ml-md">{{ searchData.date_start + ' ~ ' + searchData.date_end }}</span>
        </div>
        <div class="right">
          <span class="title">{{ clinicStore.getClinic.code_clinic }} {{ clinicStore.getClinic.name_clinic_display }}</span>
          <span class="caption1 regular">{{ '出力日: ' + new Date().toLocaleString('ja-JP', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }) }}</span>
        </div>
      </div>
      <q-separator color="dark" size="2px" />
      <MtTable2 :columns="columns"  :rows="formattedRows" :rowsBg="true" flat no-data-message="登録がありません。" no-result-message="該当のデータが見つかりませんでした">
        <template v-slot:thead="{ columns }">
          <th
            v-for="(col, index) in columns"
            :key="col.name"
            :class="['text-' + (col.align || 'left')]">
            <div style="display: inline-flex; align-items: center; white-space: nowrap;">
              <span>{{ col.label }}</span>

              <q-icon
                v-if="col.tooltip"
                name="info"
                size="14px"
                color="primary"
                class="q-ml-xs cursor-pointer">
                <q-popup-proxy transition-show="scale" transition-hide="scale">
                  <div v-html="col.tooltip" style="padding: 15px; white-space: normal;" />
                </q-popup-proxy>
              </q-icon>
            </div>
          </th>
        </template>
        <template v-slot:body="{ row: service }">
          <!-- Service Level -->
          <tr :class="'heading' + service.header">
            <td :colspan="columns.length">
              <strong class="text-white">{{ service.label }}</strong>
            </td>
          </tr>
          <!-- Item Type Level -->
          <template v-for="itemType in service.children" :key="itemType.label">
            <tr :class="'heading' + itemType.header">
              <td :colspan="3">
                <strong>{{ itemType.label }}</strong>
              </td>
              <td class="text-right">{{ itemType.cd_count }}</td>
              <td class="text-right">{{ itemType.total_quantity }}</td>
              <td class="text-right">{{ formattedPrice(itemType.total_amount_sales) }}</td>
              <td colspan="2"></td>
            </tr>
            <!-- Category Level -->
            <template v-for="category1 in itemType.children" :key="category1.label">
              <tr :class="'heading' + category1.header">
                <td :colspan="columns.length">
                  <strong>{{ category1.label }}</strong>
                </td>
              </tr>
              <!-- Category2 Level -->
              <template v-for="category2 in category1.children" :key="category2.code_category2">
                <tr :class="'heading' + category2.header">
                  <td></td>
                  <td>{{ category2.code_category2 }}</td>
                  <td>{{ category2.name_category2 }}</td>
                  <td class="text-right">{{ category2.cd_count }}</td>
                  <td class="text-right">{{ category2.total_quantity }}</td>
                  <td class="text-right">{{ formattedPrice(category2.total_amount_sales) }}</td>
                  <td class="text-right">{{ formattedPrice(category2.average) }}</td>
                  <td class="text-right">{{ category2.percentage }}</td>
                </tr>
              </template>
            </template>
          </template>
        </template>
    </MtTable2>
    <div
      class="flex justify-end full-width q-pa-md ipad-top-button-bottom"
      style="bottom: 85px; right: 20px;position: fixed;"
    >
      <q-btn flat @click.stop="scrollToTop()">
        <q-icon class="q-mr-xs" name="vertical_align_top" />
        TOP
      </q-btn>
    </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center">
        <q-btn class="bg-grey-100 text-grey-800" outline @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn class="q-ml-md" color="primary" unelevated @click="exportCSV()">
          <q-icon name="description" class="q-mr-sm"/>
          CSVダウンロード
        </q-btn>
        <q-btn :disable="disableExport" class="q-ml-md" color="primary" unelevated @click="exportPDF()">
          <q-icon name="picture_as_pdf" class="q-mr-sm"/>
          PDFダウンロード
        </q-btn>
        <q-btn :disable="disableExport" class="q-ml-md" outline @click="copytoclipboard()">
          <q-icon name="content_copy" class="q-mr-sm"/>
          コピー
        </q-btn>
      </div>
    </q-card-section>
  </div>
</template>

<style scoped>
:deep(.q-table thead tr) {
  height: 36px;
  padding: 2px 7px;
  color: #000;
}
:deep(.q-table tbody td) {
  height: auto;
  padding: 2px 7px;
  color: #000
}
.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.left {
  display: flex;
  gap: 8px; /* Adjust gap between the first two fields */
}

.right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.table-body-row > td {
  height: 24px;
}

.heading1 {
  font-size: 1.2em;
  padding: 7px 16px;
  background: #757575;
  color: #fff;
  strong {
    margin-left: 0px;
  }
}

.heading2 {
  font-size: 1.1em;
  padding: 7px 16px;
  background: #eee;
  color: #000;
  strong {
    margin-left: 20px;
  }
}

.heading3 {
  font-size: 1em;
  padding: 7px 16px;
  strong {
    margin-left: 40px;
  }
}
</style>

