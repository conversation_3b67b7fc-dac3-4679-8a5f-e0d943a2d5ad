<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import useEmployeeStore from '@/stores/employees'
import useBookingItemStore from '@/stores/booking-items'
import useCommonStore from '@/stores/common'
import useClinicStore from '@/stores/clinics'
import useAvailabilityCalendarBookingStore from '@/stores/availability-calendar-booking'
import useItemStore from '@/stores/items'
import mtUtils from '@/utils/mtUtils'
// @ts-ignore
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
// @ts-ignore
import SchedulerBoard from './components/SchedulerBoard.vue'
// @ts-ignore
import ViewItemServiceDetailModal from './components/ViewItemServiceDetailModal.vue'
// @ts-ignore
import BookingSDRecords from './components/BookingSDRecords.vue'

const props = withDefaults(
  defineProps<{
    data?: any
    searchData?: any
    selectedClinic?: number | null
  }>(),
  {
    data: () => ({}),
    searchData: () => ({}),
    selectedClinic: null
  }
)

const emits = defineEmits(['close'])

// Initialize router
const router = useRouter()

// Initialize stores
const employeeStore = useEmployeeStore()
const bookingItemStore = useBookingItemStore()
const commonStore = useCommonStore()
const clinicStore = useClinicStore()
const availabilityStore = useAvailabilityCalendarBookingStore()
const itemStore = useItemStore()

// Get reactive data from stores
const { getEmployees } = storeToRefs(employeeStore)
const { getBookableItemService } = storeToRefs(bookingItemStore)
const { getCommonTypeServiceOptionList } = storeToRefs(commonStore)
const { getClinics } = storeToRefs(clinicStore)

// Loading states
const loading = ref(false)
const error = ref('')

// Schedule board state
const scheduleBoardData = ref<any>(null)
const scheduleResources = ref<any[]>([])
const scheduleEvents = ref<any[]>([])
const isFormSubmitted = ref(false)
const bookableEmployeeList = ref<any[]>([])
const selectedEmployeeJsonData = ref<any>(null)
const selectedBookingType = ref<number | null>(null)

// Import ScheduleDetailModal for smallPopup

// Form data
const formData = ref({
  clinic: props.selectedClinic || null,
  service: null,
  itemService: null,
  employee: 0,
  startDate: '',
  endDate: ''
})

// Current booking item data
const currentBookingItemData = ref<any>(null)

// Item Service Detail Modal - no longer needed for popup approach
// const showItemServiceDetailModal = ref(false)
// const itemServiceDetailData = ref<any>(null)
// const bookingItemsData = ref<any>(null)

// Pickup time form data
const enable_time_range = ref(false)
const pickup_mode = ref('fixed')
const pickup_start_time = ref('')
const pickup_end_time = ref('')
const pickup_lead_time = ref(15)

// Pickup mode options
const pickupModeOptions = [
  { label: '固定', value: 'fixed' },
  { label: '順次', value: 'sequential' }
]

// Lead time options (in minutes)
const leadTimeOptions = [
  { label: '5分', value: 5 },
  { label: '10分', value: 10 },
  { label: '15分', value: 15 },
  { label: '20分', value: 20 },
  { label: '30分', value: 30 },
  { label: '45分', value: 45 },
  { label: '60分', value: 60 }
]

// Booking type options
const bookingTypeOptions = [
  { label: 'すべて', value: null },
  { label: 'Block Duration', value: 1 },
  { label: 'Max Slot', value: 2 },
  { label: 'Tentative Request', value: 3 },
  { label: 'Simple Request', value: 4 }
]

// Computed options for dropdowns
const clinicOptions = computed(() =>
  getClinics.value.map((clinic: any) => ({
    label: clinic.name_clinic_display,
    value: clinic.id_clinic
  }))
)

const serviceOptions = computed(() =>
  getCommonTypeServiceOptionList.value.map((option: any) => ({
    label: option.label,
    value: option.value
  }))
)

const itemServiceOptions = computed(() => {
  if (!formData.value.service) return []
  return getBookableItemService.value.map((item: any) => ({
    label: item.name_item_service,
    value: item.id_item_service
  }))
})

const employeeOptions = computed(() => {
  // Prevent recursive updates by checking if data exists and is stable
  const bookingData = currentBookingItemData.value
  if (!bookingData?.bookable_employee_list || !Array.isArray(bookingData.bookable_employee_list)) {
    return [{ label: '未選択', value: 0 }]
  }

  const employees = getEmployees.value || []

  const bookableEmployees = bookingData.bookable_employee_list
    .filter((item: any) => item?.booking_item_employee?.flg_booking_available_employee === 1)
    .map((item: any) => {
      const employeeId = item.booking_item_employee?.id_employee_book_id
      if (!employeeId) return null

      const employee = employees.find((emp: any) => emp.id_employee === employeeId)

      return {
        label: employee ? employee.name_display : `Employee ${employeeId}`,
        value: employeeId
      }
    })
    .filter(Boolean) // Remove null values

  return [...bookableEmployees]
})

// Form validation
const isFormValid = computed(() => {
  return (
    formData.value.clinic &&
    formData.value.service &&
    formData.value.itemService &&
    formData.value.startDate &&
    formData.value.endDate
  )
})

// Handle service selection change
const handleServiceChange = async (selectedService: any) => {
  if (selectedService && formData.value.clinic) {
    formData.value.service = selectedService
    formData.value.itemService = null
    formData.value.employee = 0
    currentBookingItemData.value = null

    try {
      await bookingItemStore.fetchBookingItemByType({
        id_clinic: formData.value.clinic as number,
        id_item_service: selectedService
      })
    } catch (error) {
      console.error('Error fetching booking item types:', error)
    }
  }
}

// Handle item service selection change
const handleItemServiceChange = async (selectedItemService: any) => {
  if (selectedItemService && formData.value.clinic) {
    formData.value.itemService = selectedItemService
    formData.value.employee = 0

    try {
      await bookingItemStore.fetchBookingItemByFilter({
        id_clinic: formData.value.clinic as number,
        id_item_service: selectedItemService
      })

      // Use nextTick to prevent reactive update conflicts
      await new Promise((resolve) => {
        setTimeout(() => {
          const bookingItem = bookingItemStore.getCurrentBookingItem
          if (bookingItem && JSON.stringify(bookingItem) !== JSON.stringify(currentBookingItemData.value)) {
            currentBookingItemData.value = bookingItem

            // Update pickup time settings based on json_booking_item data
            updatePickupTimeFromBookingItem(bookingItem)
            bookableEmployeeList.value = bookingItem.bookable_employee_list
          }
          resolve(true)
        }, 0)
      })

      // Fetch employees for the clinic if not already loaded
      if (getEmployees.value.length === 0) {
        await employeeStore.fetchEmployees({ id_clinic: formData.value.clinic })
      }
    } catch (error) {
      console.error('Error fetching booking item data:', error)
    }
  }
}

// Update pickup time settings from booking item data
const updatePickupTimeFromBookingItem = (bookingItem: any) => {
  const additionalInfo = bookingItem?.booking_item?.json_booking_item?.additional_info
  if (additionalInfo && Array.isArray(additionalInfo)) {
    const setPickupTime = additionalInfo.find((item: any) => item.key === 'set_pickup_time')
    const pickupTimeMode = additionalInfo.find((item: any) => item.key === 'pickup_time_mode')
    const pickupStartTime = additionalInfo.find((item: any) => item.key === 'pickup_start_time')
    const pickupEndTime = additionalInfo.find((item: any) => item.key === 'pickup_end_time')
    const pickupLeadTime = additionalInfo.find((item: any) => item.key === 'pickup_lead_time')

    enable_time_range.value = setPickupTime?.value || false
    pickup_mode.value = pickupTimeMode?.value || 'fixed'
    pickup_start_time.value = pickupStartTime?.value || ''
    pickup_end_time.value = pickupEndTime?.value || ''
    pickup_lead_time.value = pickupLeadTime?.value || 15
  }
}

// Reset form
const resetForm = () => {
  formData.value = {
    clinic: props.selectedClinic || null,
    service: null,
    itemService: null,
    employee: 0,
    startDate: '',
    endDate: ''
  }
  currentBookingItemData.value = null
  selectedBookingType.value = null

  // Reset pickup time settings
  enable_time_range.value = false
  pickup_mode.value = 'fixed'
  pickup_start_time.value = ''
  pickup_end_time.value = ''
  pickup_lead_time.value = 15

  // Reset schedule board data
  isFormSubmitted.value = false
  scheduleBoardData.value = null
  scheduleResources.value = []
  scheduleEvents.value = []

  // Clear store data
  availabilityStore.clearData()
}

// Initialize data when component mounts
onMounted(async () => {
  try {
    // Load clinics if not already loaded
    if (getClinics.value.length === 0) {
      await clinicStore.fetchClinics()
    }

    // Load employees if not already loaded
    if (getEmployees.value.length === 0 && formData.value.clinic) {
      await employeeStore.fetchEmployees({ id_clinic: formData.value.clinic })
    }

    // Load common service types if not already loaded
    if (getCommonTypeServiceOptionList.value.length === 0) {
      await commonStore.fetchPreparationCommonList({ code_common: [11] })
    }

    // Set default start date to first day of current month
    const today = new Date()
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 2)

    // Set default end date to last day of current month
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    formData.value.startDate = firstDay.toISOString().split('T')[0]
    formData.value.endDate = lastDay.toISOString().split('T')[0]

  } catch (error) {
    console.error('Error initializing data:', error)
  }
})

// Update start date and automatically set end date to last day of current month
const updateStartDate = (selectedDate: string) => {
  if (selectedDate && selectedDate !== formData.value.startDate) {
    formData.value.startDate = selectedDate

    // Automatically set end date to last day of the current month
    const startDate = new Date(selectedDate)
    const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0)
    formData.value.endDate = endDate.toISOString().split('T')[0]
  }
}

// Update end date
const updateEndDate = (selectedDate: string) => {
  if (selectedDate) {
    formData.value.endDate = selectedDate
  }
}

// Navigate to previous month
const goToPreviousMonth = () => {
  const currentDate = new Date(formData.value.startDate)
  const previousMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 2)
  const lastDayOfPreviousMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0)
  
  formData.value.startDate = previousMonth.toISOString().split('T')[0]
  formData.value.endDate = lastDayOfPreviousMonth.toISOString().split('T')[0]
}

// Navigate to next month
const goToNextMonth = () => {
  const currentDate = new Date(formData.value.startDate)
  const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 2)
  const lastDayOfNextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 2, 0)
  
  formData.value.startDate = nextMonth.toISOString().split('T')[0]
  formData.value.endDate = lastDayOfNextMonth.toISOString().split('T')[0]
}

// Help menu handler (placeholder)
const openHelpMenu2 = () => {
  // Placeholder for help menu functionality
  console.log('Help menu for pickup lead time')
}

// Show Item Service Detail Modal using popup
const showItemServiceDetail = async () => {
  if (!formData.value.itemService || !formData.value.clinic) {
    mtUtils.autoCloseAlert('予約商品と病院を選択してください')
    return
  }

  try {
    // Update router query with item service ID
    router.replace({ query: { id: formData.value.itemService } })
    
    // Open modal using popup utility
    await mtUtils.popup(ViewItemServiceDetailModal, { 
      id: formData.value.itemService,
      sectionTo: 0 
    })
    
    // Reset router query after modal closes
    router.replace({ query: {} })
    
  } catch (error) {
    console.error('Error opening item service detail modal:', error)
    mtUtils.autoCloseAlert('モーダルの表示に失敗しました')
  }
}

// Submit form and fetch Schedule Board data
const submitForm = async () => {
  if (!isFormValid.value) {
    mtUtils.autoCloseAlert('全ての必須項目を入力してください')
    return
  }

  if (!formData.value.clinic || !formData.value.itemService) {
    mtUtils.autoCloseAlert('病院名と予約商品を選択してください')
    return
  }

  try {
    loading.value = true
    error.value = ''

    const params = {
      id_clinic: formData.value.clinic as number,
      id_item_service: formData.value.itemService as number,
      date_start: formData.value.startDate,
      date_end: formData.value.endDate,
      id_employee: formData.value.employee && formData.value.employee !== 0 ? formData.value.employee : undefined,
      time_start: '09:00',
      time_end: '17:00',
      booking_method: selectedBookingType.value || undefined,
      include_type_details: true
    }

    console.log('Submitting schedule board params:', params)
    const response = await availabilityStore.fetchScheduleBoardData(params)
    console.log('Schedule Board API Response:', response)

    if (response) {
      scheduleBoardData.value = response
      isFormSubmitted.value = true
      console.log('Schedule board data processed:', response)

      // Debug: Log the structure of rows and sub_rows
      if (response.data?.schedule_board?.rows) {
        console.log('Number of date rows:', response.data.schedule_board.rows.length)
        response.data.schedule_board.rows.forEach((row: any, index: number) => {
          console.log(`Row ${index} (${row.date}):`, {
            sub_rows_count: row.sub_rows?.length || 0,
            levels: row.sub_rows?.map((sr: any) => sr.level) || []
          })

          // Check specifically for L4 rows
          const l4Rows = row.sub_rows?.filter((sr: any) => sr.level === 'L4') || []
          if (l4Rows.length > 0) {
            console.log(`Found ${l4Rows.length} L4 rows:`, l4Rows)
          } else {
            console.log('No L4 rows found in this date row')
          }
        })
      }
    }
  } catch (err: any) {
    // Handle new API error format
    if (err.response?.data) {
      const apiError = err.response.data
      if (apiError.errors && typeof apiError.errors === 'object') {
        // Handle structured validation errors
        const errorMessages = Object.values(apiError.errors).flat().join(', ')
        error.value = `エラー: ${errorMessages}`
      } else if (apiError.message) {
        error.value = apiError.message
      } else {
        error.value = 'スケジュールデータの取得に失敗しました'
      }
    } else {
      error.value = err.message || 'スケジュールデータの取得に失敗しました'
    }
    console.error('Schedule board fetch error:', err)
  } finally {
    loading.value = false
  }
}

// Refresh schedule board data (reuse the same params from the last successful submission)
const refreshScheduleBoard = async () => {
  if (!isFormSubmitted.value || !formData.value.clinic || !formData.value.itemService) {
    console.log('Cannot refresh: form not yet submitted or missing required data')
    return
  }

  try {
    const params = {
      id_clinic: formData.value.clinic as number,
      id_item_service: formData.value.itemService as number,
      date_start: formData.value.startDate,
      date_end: formData.value.endDate,
      id_employee: formData.value.employee && formData.value.employee !== 0 ? formData.value.employee : undefined,
      time_start: '09:00',
      time_end: '17:00',
      booking_method: selectedBookingType.value || undefined,
      include_type_details: true
    }

    console.log('Refreshing schedule board with params:', params)
    const response = await availabilityStore.fetchScheduleBoardData(params)

    if (response) {
      scheduleBoardData.value = response
      console.log('Schedule board data refreshed successfully')
    }
  } catch (err: any) {
    console.error('Failed to refresh schedule board:', err)
    // Don't show error to user for refresh failures, just log it
  }
}

const handleEmployeeChange = (selectedEmployee: any) => {
  formData.value.employee = selectedEmployee
  selectedEmployeeJsonData.value = bookableEmployeeList.value.find((item: any) => item.booking_item_employee.id_employee_book_id === selectedEmployee)
}

</script>

<template>
  <q-card flat class="form-container">
    <!-- Loading Overlay -->
    <q-linear-progress v-if="loading" indeterminate color="primary" />

    <!-- Error Message -->
    <q-banner v-if="error" rounded class="bg-red-1 text-red-8 q-ma-md">
      <q-icon name="error" />
      {{ error }}
      <template v-slot:action>
        <q-btn flat color="red-8" label="再試行" @click="submitForm" />
      </template>
    </q-banner>

    <!-- Form Section -->
    <q-card-section class="form-section">
      <div class="text-h6 q-mb-md">予約スケジュール検索</div>

      <div class="row q-gutter-md">
        <!-- Clinic Selection -->
        <div class="col-md-6">
          <q-select
            v-model="formData.clinic"
            :options="clinicOptions"
            outlined
            dense
            emit-value
            map-options
            label="病院名 *"
            :rules="[(val) => !!val || '病院名を選択してください']"
          />
        </div>
      </div>

      <!-- Service Selection -->
      <div class="row q-gutter-md">
        <div class="col-md-5">
          <div class="text-subtitle2 q-mb-sm">サービス *</div>
          <div class="horizontal-scroll-container">
            <div class="horizontal-options">
              <MtFormRadiobtn
                v-for="option in serviceOptions"
                :key="option.value"
                :val="option.value"
                :label="option.label"
                :selected="formData.service || false"
                class="option-item"
                @update:selected="handleServiceChange($event)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Item Service Selection -->
      <div class="row q-gutter-md">
        <div v-if="formData.service" class="col-md-5">
          <div class="text-subtitle2 q-mb-sm">予約商品 *</div>
          <div class="horizontal-scroll-container">
          <div class="horizontal-options">
            <MtFormRadiobtn
              v-for="option in itemServiceOptions"
              :key="option.value"
              :val="option.value"
              :label="option.label"
              :selected="formData.itemService || false"
              :disable="!formData.service"
              class="option-item"
              @update:selected="handleItemServiceChange($event)"
            />
          </div>
        </div>
        
        <!-- Item Service Detail Button -->
        <div v-if="formData.itemService" class="col-md-2">
          <q-btn 
            color="primary" 
            label="詳細表示" 
            @click="showItemServiceDetail"
            :loading="loading"
            outline
            class="q-mt-sm"
          />
        </div>
      </div>
      <!-- Service Item JSON Data and Employee Data Row -->
      <div v-if="formData.itemService && currentBookingItemData?.booking_item" class="col-md-5">
          <div class="text-subtitle2 q-mb-sm">予約商品データ</div>
          <div class="json-display">
            <pre>{{
              JSON.stringify(
                {
                  ...currentBookingItemData,
                  item_service: undefined,
                  bookable_employee_list: undefined
                },
                null,
                2
              )
            }}</pre>
          </div>
        </div>
      </div>

      <!-- Employee Selection -->
      <div class="row q-gutter-md q-mt-md">
        <div v-if="formData.itemService && employeeOptions.length > 1" class="col-md-5">
          <div class="text-subtitle2 q-mb-sm">担当者</div>
          <div class="horizontal-scroll-container">
          <div class="horizontal-options">
            <MtFormRadiobtn
              v-for="option in employeeOptions"
              :key="option.value"
              :val="option.value"
              :label="option.label"
              :selected="formData.employee"
              class="option-item"
              @update:selected="handleEmployeeChange($event)"
            />
          </div>
        </div>
      </div>
      <!-- Employee Booking Item JSON Data -->
      <div v-if="formData.itemService && selectedEmployeeJsonData" class="col-md-5">
        <div class="text-subtitle2 q-mb-sm">担当者データ</div>
        <div class="json-display">
          <pre>{{ JSON.stringify(selectedEmployeeJsonData, null, 2) }}</pre>
        </div>
      </div>
      </div>

      <!-- Booking Type Filter -->
      <!-- <div class="row q-gutter-md q-mt-md">
        <div class="col-md-5">
          <div class="text-subtitle2 q-mb-sm">予約タイプフィルター</div>
          <div class="horizontal-scroll-container">
            <div class="horizontal-options">
              <MtFormRadiobtn
                v-for="option in bookingTypeOptions"
                :key="option.value || 'all'"
                :val="option.value"
                :label="option.label"
                :selected="selectedBookingType || false"
                class="option-item"
                @update:selected="selectedBookingType = $event"
              />
            </div>
          </div>
        </div>
      </div> -->

      <div class="row q-gutter-md q-mt-md">
      <!-- Date Navigation and Inputs -->
      <div class="col-12">
        <div class="date-navigation-container">
          <!-- Previous Month Button -->
          <q-btn 
            icon="chevron_left" 
            color="primary" 
            outline 
            round 
            size="sm"
            @click="goToPreviousMonth"
            title="前月"
          />
          
          <!-- Date Inputs -->
          <div class="date-inputs-wrapper">
            <!-- Start Date -->
            <div class="date-input-item">
              <q-input v-model="formData.startDate" type="date" label="開始日 *" outlined readonly />
            </div>

            <!-- End Date -->
            <div class="date-input-item">
              <q-input v-model="formData.endDate" type="date" label="終了日 *" outlined readonly />
            </div>
          </div>
          
          <!-- Next Month Button -->
          <q-btn 
            icon="chevron_right" 
            color="primary" 
            outline 
            round 
            size="sm"
            @click="goToNextMonth"
            title="次月"
          />
        </div>
      </div>
      </div>
      <!-- Submit and Reset Buttons -->
      <div class="row q-mt-md q-gutter-md">
        <div class="col-12">
          <q-btn
            color="primary"
            label="スケジュール検索"
            @click="submitForm"
            :loading="loading"
            :disable="!isFormValid"
            class="q-mr-md"
          />
          <q-btn color="grey-6" label="リセット" @click="resetForm" outline />
        </div>
      </div>
    </q-card-section>

    <!-- Schedule Board Section -->
    <q-card-section v-if="isFormSubmitted" class="schedule-board-section">
      <div class="text-h6 q-mb-md">スケジュールボード</div>

      <!-- Schedule Board with Data -->
      <div v-if="scheduleBoardData?.schedule_board" class="row q-gutter-md">
        <!-- Left Column - Schedule Board Table -->
        <div class="col-12 col-lg-12">
          <div class="schedule-board-container">
            <SchedulerBoard
              :schedule-data="scheduleBoardData.schedule_board"
              :bookable-slots="scheduleBoardData.bookable_slots || []"
              :loading="loading"
              :selected-clinic="formData.clinic"
              :booking-item-data="currentBookingItemData"
              @refresh="refreshScheduleBoard"
            />
          </div>
        </div>
      </div>

      <!-- No Data Message -->
      <div v-else-if="isFormSubmitted && !scheduleBoardData?.schedule_board?.rows?.length" class="no-data-message">
        <q-icon name="event_busy" size="64px" color="grey-5" />
        <div class="text-h6 text-grey-6 q-mt-md">指定された条件でのスケジュールデータが見つかりません</div>
      </div>

      <!-- Summary Information -->
      <div v-if="scheduleBoardData?.summary" class="summary-info q-mt-md">
        <q-card flat bordered class="q-pa-md">
          <div class="text-subtitle1 q-mb-md">サマリー情報</div>
          <div class="summary-chips">
            <q-chip color="primary" text-color="white" icon="event">
              全スロット: {{ scheduleBoardData.summary.total_slots }}
            </q-chip>
            <q-chip color="positive" text-color="white" icon="check_circle">
              予約可能: {{ scheduleBoardData.summary.available_slots }}
            </q-chip>
            <q-chip color="info" text-color="white" icon="calendar_today">
              日数: {{ scheduleBoardData.summary.dates_count }}
            </q-chip>
          </div>
        </q-card>
      </div>

      <!-- Legend -->
      <!-- <div v-if="isFormSubmitted" class="legend q-mt-md">
        <q-card flat bordered class="q-pa-md">
          <div class="text-subtitle2 q-mb-sm">凡例</div>
          <div class="row q-gutter-sm">
            <q-chip size="sm" color="orange" text-color="white" icon="square">
              営業時間
            </q-chip>
            <q-chip size="sm" color="light-green" text-color="white" icon="square">
              利用可能スロット
            </q-chip>
            <q-chip size="sm" color="blue" text-color="white" icon="square">
              担当者スケジュール
            </q-chip>
            <q-chip size="sm" color="grey" text-color="white" icon="square">
              利用不可
            </q-chip>
          </div>
        </q-card>
      </div> -->
    </q-card-section>

    <!-- Booking SD Records Component -->
    <BookingSDRecords :selected-clinic="formData.clinic" />
    
    <!-- Item Service Detail Modal is now handled via mtUtils.popup -->
  </q-card>
</template>

<style lang="scss" scoped>
.form-container {
  width: 100%;
}

.form-section {
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e0;
}

.booking-item-section {
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
}

.json-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.json-data-panel {
  .json-response {
    max-height: 600px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
  }
}

.horizontal-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0;
  margin-bottom: 16px;
}

.horizontal-options {
  display: flex;
  gap: 16px;
  min-width: fit-content;
}

.option-item {
  flex-shrink: 0;
  white-space: nowrap;
  min-width: fit-content;
}

/* Custom scrollbar for horizontal scroll */
.horizontal-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.horizontal-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Date navigation styles */
.date-navigation-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.date-inputs-wrapper {
  display: flex;
  gap: 16px;
  flex: 1;
  justify-content: center;
  max-width: 600px;
}

.date-input-item {
  flex: 1;
  min-width: 200px;
}

@media (max-width: 768px) {
  .date-navigation-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .date-inputs-wrapper {
    flex-direction: column;
    width: 100%;
    max-width: none;
  }
  
  .date-input-item {
    min-width: auto;
  }
}

/* Pickup time form styles */
.underline-input {
  border-bottom: 1px solid #ddd;
  border-radius: 0;
}

.underline-input :deep(.q-field__control) {
  border-radius: 0;
}

.underline-input :deep(.q-field__control:before) {
  border-bottom: 1px solid #ddd;
}

.underline-input :deep(.q-field__control:after) {
  border-bottom: 2px solid #1976d2;
}

/* Schedule Board Section Styles */
.schedule-board-section {
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.schedule-board-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.summary-info {
  .q-chip {
    font-weight: 500;
  }
}

.summary-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.legend {
  .q-chip {
    font-weight: 500;
  }
}

// Responsive adjustments for schedule board
@media (max-width: 768px) {
  .schedule-board-container {
    :deep(.schedule-board) {
      font-size: 12px;
    }
  }

  .summary-info .row {
    flex-direction: column;

    .col {
      margin-bottom: 8px;
    }
  }

  .legend .row {
    flex-direction: column;

    .q-chip {
      margin-bottom: 4px;
      align-self: flex-start;
    }
  }
}

</style>
