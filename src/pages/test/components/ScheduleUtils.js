/**
 * Utility functions for Schedule Board API data parsing and formatting
 */

/**
 * Parse schedule board API response data
 * @param {Object} apiResponse - The API response data
 * @returns {Object} Parsed schedule data ready for UI display
 */
export function parseScheduleBoardData(apiResponse) {
  if (!apiResponse?.data?.schedule_board) {
    return {
      time_headers: [],
      rows: [],
      bookable_slots: [],
      summary: null
    }
  }

  const { schedule_board, bookable_slots, summary } = apiResponse.data

  return {
    time_headers: schedule_board.time_headers || [],
    rows: schedule_board.rows || [],
    bookable_slots: bookable_slots || [],
    summary: summary || null
  }
}

/**
 * Format time slot display text
 * @param {string} startTime - Start time (HH:MM format)
 * @param {string} endTime - End time (HH:MM format)
 * @param {number} slotCount - Number of available slots
 * @returns {string} Formatted display text
 */
export function formatTimeSlot(startTime, endTime, slotCount) {
  if (!startTime || !endTime) {
    return '-'
  }
  
  if (slotCount !== undefined && slotCount !== null) {
    return `${startTime} ~ ${endTime} (${slotCount})`
  }
  
  return `${startTime} ~ ${endTime}`
}

/**
 * Extract slot count from formatted time slot text
 * @param {string} timeSlotText - Formatted time slot text
 * @returns {number|null} Extracted slot count or null if not found
 */
export function extractSlotCount(timeSlotText) {
  if (!timeSlotText || typeof timeSlotText !== 'string') {
    return null
  }
  
  const match = timeSlotText.match(/\((\d+)\)/)
  return match ? parseInt(match[1], 10) : null
}

/**
 * Parse employee schedule text
 * @param {string} scheduleText - Employee schedule text (e.g., "L3 (Adam) => 11:00 ~ 14:00 (5)")
 * @returns {Object|null} Parsed employee schedule data
 */
export function parseEmployeeSchedule(scheduleText) {
  if (!scheduleText || typeof scheduleText !== 'string') {
    return null
  }
  
  const match = scheduleText.match(/L3\s*\(([^)]+)\)\s*=>\s*(\d{2}:\d{2})\s*~\s*(\d{2}:\d{2})\s*\((\d+)\)/)
  
  if (match) {
    return {
      level: 'L3',
      employeeName: match[1].trim(),
      startTime: match[2],
      endTime: match[3],
      slotCount: parseInt(match[4], 10)
    }
  }
  
  return null
}

/**
 * Check if a time slot is bookable
 * @param {Array} bookableSlots - Array of bookable slots from API
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} timeStart - Start time in HH:MM format
 * @returns {boolean} True if slot is bookable
 */
export function isSlotBookable(bookableSlots, date, timeStart) {
  if (!Array.isArray(bookableSlots)) {
    return false
  }
  
  return bookableSlots.some(slot => 
    slot.date === date && 
    slot.time_start === timeStart &&
    slot.booking_metadata?.can_book === true
  )
}

/**
 * Find bookable slot data
 * @param {Array} bookableSlots - Array of bookable slots from API
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} timeStart - Start time in HH:MM format
 * @returns {Object|null} Bookable slot data or null if not found
 */
export function findBookableSlot(bookableSlots, date, timeStart) {
  if (!Array.isArray(bookableSlots)) {
    return null
  }
  
  return bookableSlots.find(slot => 
    slot.date === date && 
    slot.time_start === timeStart
  ) || null
}

/**
 * Get hierarchy level display name
 * @param {string} level - Level code (L1, L2, L3, L4)
 * @returns {string} Display name for the level
 */
export function getLevelDisplayName(level) {
  const levelNames = {
    'L1': 'Business Hours',
    'L2': 'Booking Slots', 
    'L3': 'Employee Schedule',
    'L4': 'Individual Employee'
  }
  
  return levelNames[level] || level
}

/**
 * Get level color for UI styling
 * @param {string} level - Level code (L1, L2, L3, L4)
 * @returns {string} Color code for the level
 */
export function getLevelColor(level) {
  const levelColors = {
    'L1': '#ff9800', // Orange
    'L2': '#4caf50', // Green
    'L3': '#2196f3', // Blue
    'L4': '#6f42c1'  // Purple
  }
  
  return levelColors[level] || '#666'
}

/**
 * Format date for display
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @param {string} format - Format type ('short' or 'full')
 * @returns {string} Formatted date string
 */
export function formatDisplayDate(dateString, format = 'short') {
  if (!dateString) {
    return ''
  }
  
  const date = new Date(dateString)
  
  if (format === 'short') {
    return `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`
  }
  
  if (format === 'full') {
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }
  
  return dateString
}

/**
 * Calculate availability rate for a time slot
 * @param {number} available - Available slots
 * @param {number} total - Total slots
 * @returns {number} Availability rate as percentage
 */
export function calculateAvailabilityRate(available, total) {
  if (!total || total === 0) {
    return 0
  }
  
  return Math.round((available / total) * 100)
}

/**
 * Get availability status color
 * @param {number} availabilityRate - Availability rate as percentage
 * @returns {string} Color code based on availability
 */
export function getAvailabilityColor(availabilityRate) {
  if (availabilityRate >= 70) {
    return '#4caf50' // Green - Good availability
  } else if (availabilityRate >= 30) {
    return '#ff9800' // Orange - Medium availability
  } else {
    return '#f44336' // Red - Low availability
  }
}

/**
 * Filter schedule data by employee
 * @param {Object} scheduleData - Schedule board data
 * @param {number} employeeId - Employee ID to filter by
 * @returns {Object} Filtered schedule data
 */
export function filterScheduleByEmployee(scheduleData, employeeId) {
  if (!employeeId || !scheduleData?.rows) {
    return scheduleData
  }
  
  const filteredRows = scheduleData.rows.map(row => ({
    ...row,
    sub_rows: row.sub_rows.map(subRow => {
      if (subRow.level !== 'L3') {
        return subRow
      }
      
      // Filter L3 (employee) data for specific employee
      const filteredColumns = {}
      
      Object.entries(subRow.columns).forEach(([timeHeader, cellData]) => {
        if (Array.isArray(cellData)) {
          // Filter employee schedules
          const filteredSchedules = cellData.filter(schedule => {
            const parsed = parseEmployeeSchedule(schedule)
            return parsed && parsed.employeeName === employeeId
          })
          
          filteredColumns[timeHeader] = filteredSchedules.length > 0 ? filteredSchedules : ['-']
        } else {
          filteredColumns[timeHeader] = cellData
        }
      })
      
      return {
        ...subRow,
        columns: filteredColumns
      }
    })
  }))
  
  return {
    ...scheduleData,
    rows: filteredRows
  }
}

/**
 * Process and transform the schedule data to ensure it's displayed correctly in the table
 * Handles both legacy L3 processing and new API V2 L4 rows
 * @param {Object} scheduleData - The schedule board data from API
 * @returns {Object} Processed schedule data with formatted rows
 */
export function processEmployeeScheduleData(scheduleData) {
  if (!scheduleData || !scheduleData.rows || !Array.isArray(scheduleData.rows)) {
    return scheduleData;
  }

  const processedRows = scheduleData.rows.map(row => {
    // Check if this row already has L4 data (API V2 format)
    const hasL4Rows = row.sub_rows.some(sr => sr.level === 'L4');
    
    if (hasL4Rows) {
      // API V2 format - L4 rows are already provided, use as-is
      return row;
    }
    
    // Legacy format - process L3 data to create individual employee rows
    const l1l2Rows = row.sub_rows.filter(sr => sr.level === 'L1' || sr.level === 'L2');
    const l3Row = row.sub_rows.find(sr => sr.level === 'L3');
    const l4Rows = row.sub_rows.filter(sr => sr.level === 'L4'); // Preserve any existing L4 rows
    
    if (!l3Row) {
      // No L3 data to process, return original row
      return row;
    }

    // Extract employee data from L3 row
    const employees = new Map();
    const timeHeaders = scheduleData.time_headers || [];
    
    // Process each time slot to extract employee data
    timeHeaders.forEach(timeHeader => {
      const cellData = l3Row.columns[timeHeader];
      
      if (Array.isArray(cellData)) {
        // Process multiple employee entries in an array
        cellData.forEach(item => {
          if (typeof item === 'string') {
            // Format: "L3 (Name) => HH:MM ~ HH:MM (N)"
            const match = item.match(/L3\s*\(([^)]+)\)\s*=>\s*([^\(]+)\((\d+)\)/);
            if (match) {
              const [_, name, timeRange, slotCount] = match;
              
              if (!employees.has(name)) {
                employees.set(name, {
                  name: name.trim(),
                  columns: {}
                });
              }
              
              employees.get(name).columns[timeHeader] = `${timeRange.trim()}(${slotCount})`;
            }
          }
        });
      } else if (typeof cellData === 'string') {
        // Handle single string format or "No Coverage"
        if (cellData.includes('L3 (')) {
          const match = cellData.match(/L3\s*\(([^)]+)\)\s*=>\s*([^\(]+)\((\d+)\)/);
          if (match) {
            const [_, name, timeRange, slotCount] = match;
            
            if (!employees.has(name)) {
              employees.set(name, {
                name: name.trim(),
                columns: {}
              });
            }
            
            employees.get(name).columns[timeHeader] = `${timeRange.trim()}(${slotCount})`;
          }
        } else if (cellData.toLowerCase().includes('no coverage')) {
          // No employee coverage for this time slot
          // Do nothing specific here, as this will be handled by the component
        }
      }
    });

    // Create individual L3 rows for each employee (for legacy data)
    const processedL3Rows = Array.from(employees.values()).map(emp => {
      const employeeColumns = {};
      
      // Initialize all columns as "-"
      timeHeaders.forEach(timeHeader => {
        employeeColumns[timeHeader] = '-';
      });
      
      // Add employee schedule data for each time slot
      Object.entries(emp.columns).forEach(([timeHeader, data]) => {
        employeeColumns[timeHeader] = data;
      });
      
      // Create the employee row
      return {
        level: 'L3',
        label: `Employee Schedule - ${emp.name}`,
        employeeName: emp.name,
        columns: employeeColumns
      };
    });

    // If no employees were extracted but we have L3 data, keep the original row
    if (processedL3Rows.length === 0 && l3Row) {
      processedL3Rows.push(l3Row);
    }

    // Return modified row with L1, L2, processed L3 rows, and any existing L4 rows
    return {
      ...row,
      sub_rows: [...l1l2Rows, ...processedL3Rows, ...l4Rows]
    };
  });

  // Return the processed data
  return {
    ...scheduleData,
    rows: processedRows
  };
}

/**
 * Validate schedule data structure
 * @param {Object} data - Schedule data object
 * @returns {Boolean} True if data is valid
 */
export function validateScheduleData(data) {
  if (!data || typeof data !== 'object') {
    return false
  }
  
  if (!Array.isArray(data.time_headers) || !Array.isArray(data.rows)) {
    return false
  }
  
  // Validate each row structure
  for (const row of data.rows) {
    if (!row.date || !row.full_date || !Array.isArray(row.sub_rows)) {
      return false
    }
    
    // Validate sub rows
    for (const subRow of row.sub_rows) {
      if (!subRow.level || !subRow.label || !subRow.columns) {
        return false
      }
    }
  }
  
  return true
}