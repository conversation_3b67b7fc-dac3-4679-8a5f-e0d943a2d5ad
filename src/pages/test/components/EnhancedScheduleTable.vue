<template>
  <div class="schedule-table-wrapper">
    <table class="schedule-table">
      <!-- Table Header -->
      <thead>
        <tr class="header-row">
          <th class="month-date-header">month/date</th>
          <th class="info-header">info</th>
          <th 
            v-for="timeHeader in scheduleData.time_headers" 
            :key="timeHeader"
            class="time-header"
          >
            {{ timeHeader }}
          </th>
        </tr>
      </thead>

      <!-- Table Body -->
      <tbody>
        <template 
          v-for="(row, rowIndex) in processedRows" 
          :key="rowIndex"
        >
          <!-- Each sub-row (L1, L2, L3) -->
          <tr 
            v-for="(subRow, subIndex) in row.sub_rows" 
            :key="`${rowIndex}-${subIndex}`"
            class="schedule-row"
            :class="getLevelRowClass(subRow.level)"
          >
            <!-- Month/Date Column with rowspan - Only show on first sub-row -->
            <td 
              v-if="subIndex === 0"
              class="month-date-cell" 
              :rowspan="row.sub_rows.length"
            >
              {{ row.date }}
            </td>
            
            <!-- Info Column (Level + Employee Name if L3) -->
            <td class="info-cell">
              <span class="level-indicator" :class="getLevelIndicatorClass(subRow.level)">
                {{ formatLevelInfo(subRow) }}
              </span>
            </td>
            
            <!-- Time Slots - Using Time Slot Component for stretching across columns -->
            <template v-if="hasTimeSpan(subRow.columns)">
              <!-- Render time slots with colspan -->
              <template v-for="(timeSpan, timeIndex) in processedTimeSpans(row, subRow)" :key="`span-${timeIndex}`">
                <td 
                  v-if="timeSpan.show"
                  class="time-slot-cell" 
                  :class="getTimeSpanClass(timeSpan)"
                  :colspan="timeSpan.colspan"
                  @click="handleTimeSpanClick($event, row, subRow, timeSpan)"
                >
                  <div class="cell-content time-span-cell">
                    {{ timeSpan.displayText }} 
                    <span v-if="timeSpan.slotInfo && timeSpan.displayText !== '-'">{{ timeSpan.slotInfo }}</span>
                    <span v-else-if="timeSpan.slotCount && timeSpan.displayText !== '-'">({{ timeSpan.slotCount }})</span>
                  </div>
                </td>
              </template>
            </template>
            <template v-else>
              <!-- Standard time slot columns -->
              <td 
                v-for="timeHeader in scheduleData.time_headers" 
                :key="timeHeader"
                class="time-slot-cell"
                :class="getCellClass(subRow.columns[timeHeader], subRow.level)"
                @click="handleCellClick($event, row, subRow, timeHeader, subRow.columns[timeHeader])"
              >
                <div class="cell-content">
                  {{ formatCellDisplayText(subRow.columns[timeHeader], subRow.level) || '-' }}
                </div>
              </td>
            </template>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import mtUtils from '@/utils/mtUtils'
import ScheduleDetailModal from '@/pages/test/components/ScheduleDetailModal.vue'


interface ColumnData {
  display: string
  can_book?: boolean
  booking_method?: number
  booking_method_name?: string
  is_exclusive?: boolean
  capacity_info?: string
}

interface SubRow {
  level: string
  label: string
  columns: Record<string, ColumnData>
  slot_max?: number
  booked?: number
  available_capacity?: number
  available_status?: string
  can_book?: boolean
  id_item_service?: number
  id_booking_item?: number
  employee_id?: number
  booking_method?: number
  booking_method_name?: string
  booking_type_display?: string
}

interface ScheduleRow {
  type: string
  date: string
  full_date: string
  sub_rows: SubRow[]
}

interface ScheduleData {
  time_headers: string[]
  rows: ScheduleRow[]
  filters?: {
    id_clinic: number
    id_item_service: number
    date_start: string
    date_end: string
    id_employee?: number
    time_start?: string
    time_end?: string
  }
}

interface TimeSpan {
  startTime: string
  endTime: string
  startIndex: number
  endIndex: number
  colspan: number
  show: boolean
  displayText: string
  slotCount?: number
  slotInfo?: string
  currentBookings?: number
  availableCapacity?: number
  status?: string
  isAvailable?: boolean
  bookableSlot?: any
  level: string
  timeHeader: string
  bookingMethod?: number
  bookingMethodName?: string
}

interface Props {
  scheduleData: ScheduleData
  bookableSlots?: any[]
  loading?: boolean
  selectedClinic?: number
  bookingItemData?: any
}

const props = withDefaults(defineProps<Props>(), {
  bookableSlots: () => [],
  loading: false,
  selectedClinic: undefined,
  bookingItemData: undefined
})

const emit = defineEmits<{
  cellClick: [data: {
    row: ScheduleRow
    subRow: SubRow
    timeHeader: string
    cellData: ColumnData
    isBookable: boolean
    bookableSlot?: any
  }]
  refresh: []
}>()

// Flag to prevent duplicate modal opens
const isModalOpen = ref(false)

// Get CSS class for level rows
const getLevelRowClass = (level: string) => {
  return {
    'level-l1-row': level === 'L1',
    'level-l2-row': level === 'L2', 
    'level-l3-row': level === 'L3',
    'level-l4-row': level === 'L4'
  }
}

// Get CSS class for level indicators
const getLevelIndicatorClass = (level: string) => {
  return {
    'level-l1-indicator': level === 'L1',
    'level-l2-indicator': level === 'L2',
    'level-l3-indicator': level === 'L3',
    'level-l4-indicator': level === 'L4'
  }
}

// Format level info for display based on new API V2 structure
const formatLevelInfo = (subRow: SubRow) => {
  if (subRow.level === 'L1') {
    return 'L1 Business Hours'
  } else if (subRow.level === 'L2') {
    return 'L2 Booking Slot'
  } else if (subRow.level === 'L3') {
    return subRow.label
  } else if (subRow.level === 'L4') {
    // Show booking type information for L4 rows
    const typeInfo = subRow.booking_type_display || subRow.booking_method_name || ''
    return `L4 Bookable Slot${typeInfo ? ` (${typeInfo})` : ''}`
  }
  return subRow.level
}


// Extract time range and slot count from new API V2 cell data format
const extractTimeRangeAndCount = (cellData: ColumnData, level: string) => {
  if (!cellData || !cellData.display || cellData.display === '-') {
    return null
  }
  
  const displayText = cellData.display
  
  // For L4 data with various formats
  if (level === 'L4') {
    // Format: "HH:MM ~ HH:MM (Available)" for available slots
    const availableMatch = displayText.match(/(\d{1,2}:\d{2})\s*~\s*(\d{1,2}:\d{2})\s*\(Available\)/)
    if (availableMatch) {
      return {
        startTime: availableMatch[1],
        endTime: availableMatch[2],
        isAvailable: true,
        slotInfo: 'Available'
      }
    }
    
    // Format: "HH:MM ~ HH:MM (current_bookings/available_capacity)"
    const capacityMatch = displayText.match(/(\d{1,2}:\d{2})\s*~\s*(\d{1,2}:\d{2})\s*\((\d+)\/(\d+)\)/)
    if (capacityMatch) {
      return {
        startTime: capacityMatch[1],
        endTime: capacityMatch[2],
        currentBookings: parseInt(capacityMatch[3], 10),
        availableCapacity: parseInt(capacityMatch[4], 10),
        slotInfo: `(${capacityMatch[3]}/${capacityMatch[4]})`
      }
    }
  }
  
  // For general time range with format "HH:MM ~ HH:MM (N)"
  const match = displayText.match(/(\d{1,2}:\d{2})\s*~\s*(\d{1,2}:\d{2})(?:\s*\((\d+)\))?/)
  if (match) {
    return {
      startTime: match[1],
      endTime: match[2],
      slotCount: match[3] ? parseInt(match[3], 10) : undefined
    }
  }
  
  return null
}

// Format cell display text for new API V2 format
const formatCellDisplayText = (cellData: ColumnData | undefined | null, level: string) => {
  if (!cellData || !cellData.display || cellData.display === '-') {
    return '-'
  }
  
  return cellData.display
}

// Check if a row has time spans (time ranges that need colspan)
const hasTimeSpan = (columns: Record<string, ColumnData>) => {
  for (const timeHeader in columns) {
    const cellData = columns[timeHeader]
    if (cellData && cellData.display && cellData.display.includes('~')) {
      return true
    }
  }
  return false
}

// Process time spans for a row to create colspan cells
const processedTimeSpans = (row: ScheduleRow, subRow: SubRow) => {
  const timeHeaders = props.scheduleData.time_headers
  const timeSpans: TimeSpan[] = []
  const usedHeaders = new Set()
  
  // Process each time header to find time spans
  timeHeaders.forEach((timeHeader, index) => {
    // Skip if this header is already part of a span
    if (usedHeaders.has(timeHeader)) return
    
    const cellData = subRow.columns[timeHeader]
    
    // Handle empty cells
    if (!cellData || !cellData.display || cellData.display === '-') {
      // Add a single cell for this header
      timeSpans.push({
        startTime: timeHeader,
        endTime: timeHeader,
        startIndex: index,
        endIndex: index,
        colspan: 1,
        show: true,
        displayText: '-',
        level: subRow.level,
        timeHeader,
        isAvailable: false,
        status: 'UNAVAILABLE'
      })
      return
    }
    
    // For ColumnData with time range
    const timeRange = extractTimeRangeAndCount(cellData, subRow.level)
    if (timeRange) {
      // Find the end index for this time span
      let endIndex = index
      for (let i = index + 1; i < timeHeaders.length; i++) {
        const nextHeader = timeHeaders[i]
        if (timeRange.endTime > nextHeader) {
          endIndex = i
          usedHeaders.add(nextHeader)
        } else {
          break
        }
      }
      
      // Calculate colspan
      const colspan = endIndex - index + 1
      
      // Create the time span
      const isAvailable = determineAvailability(subRow.level, cellData)
      const bookableSlot = findBookableSlot(row.full_date, timeHeader)
      
      timeSpans.push({
        startTime: timeRange.startTime,
        endTime: timeRange.endTime,
        startIndex: index,
        endIndex,
        colspan,
        show: true,
        displayText: `${timeRange.startTime} ~ ${timeRange.endTime}`,
        slotCount: timeRange.slotCount,
        slotInfo: timeRange.slotInfo,
        currentBookings: timeRange.currentBookings,
        availableCapacity: timeRange.availableCapacity,
        isAvailable,
        bookableSlot,
        status: isAvailable ? 'AVAILABLE' : 'UNAVAILABLE',
        level: subRow.level,
        timeHeader,
        bookingMethod: cellData.booking_method || subRow.booking_method,
        bookingMethodName: cellData.booking_method_name || subRow.booking_method_name
      })
      
      // Add hidden spans for the used headers
      for (let i = index + 1; i <= endIndex; i++) {
        timeSpans.push({
          startTime: timeHeaders[i],
          endTime: timeHeaders[i],
          startIndex: i,
          endIndex: i,
          colspan: 1,
          show: false,
          displayText: '',
          level: subRow.level,
          timeHeader: timeHeaders[i]
        })
      }
    } else {
      // Regular single-cell data
      timeSpans.push({
        startTime: timeHeader,
        endTime: timeHeader,
        startIndex: index,
        endIndex: index,
        colspan: 1,
        show: true,
        displayText: formatCellDisplayText(cellData, subRow.level),
        level: subRow.level,
        timeHeader,
        isAvailable: false,
        status: 'UNAVAILABLE'
      })
    }
  })
  
  return timeSpans
}

// Determine if a time span is available for booking based on new API V2
const determineAvailability = (level: string, cellData: ColumnData) => {
  // For level L4 (final bookable slots), check can_book flag
  if (level === 'L4') {
    return cellData.can_book === true
  }
  
  // For other levels, not directly bookable
  return false
}

// Find bookable slot data
const findBookableSlot = (date: string, timeHeader: string) => {
  return props.bookableSlots?.find(slot => 
    slot.date === date && 
    slot.time_start === timeHeader
  )
}

// Get CSS class for time span
const getTimeSpanClass = (timeSpan: TimeSpan) => {
  const isEmpty = !timeSpan.displayText || timeSpan.displayText === '-'
  
  // Start with base classes
  const classes: Record<string, boolean> = {
    'time-span': true
  }
  
  // Add empty class if applicable
  if (isEmpty) {
    classes['cell-empty'] = true
  }
  
  // Add clickable class if applicable
  if (timeSpan.level === 'L4' && !isEmpty) {
    classes['clickable'] = true
  }
  
  // Only apply level-specific styling if the cell is not empty
  if (!isEmpty) {
    if (timeSpan.level === 'L1') {
      classes['cell-l1'] = true
    } else if (timeSpan.level === 'L2') {
      classes['cell-l2'] = true
    } else if (timeSpan.level === 'L3') {
      classes['cell-l3'] = true
    } else if (timeSpan.level === 'L4') {
      classes['cell-l4'] = true
    }
    
    if (timeSpan.isAvailable) {
      classes['cell-available'] = true
    } else if (timeSpan.status === 'UNAVAILABLE') {
      classes['cell-unavailable'] = true
    }
  }
  
  return classes
}

// Handle time span click
const handleTimeSpanClick = async (event: Event, row: ScheduleRow, subRow: SubRow, timeSpan: TimeSpan) => {
  console.log('🔥 TIME SPAN CLICK:', {
    level: subRow.level,
    label: subRow.label,
    timeSpan: timeSpan.displayText,
    employee_id: subRow.employee_id
  })
  
  // Stop event propagation to prevent duplicate handlers
  event.stopPropagation()
  event.preventDefault()
  
  
  // Only allow clicks on L4 rows (final bookable slots in new API)
  if (subRow.level !== 'L4') {
    return // Ignore clicks on L1, L2, L3
  }
  
  // Prevent clicks on unavailable slots
  if (timeSpan.status === 'UNAVAILABLE' || !timeSpan.isAvailable) {
    return // Ignore clicks on unavailable slots
  }
  
  // Only show modal for L4 rows with valid time data
  if (timeSpan.displayText && timeSpan.displayText !== '-') {
    // Prepare modal data for L4 booking
    const modalData = {
      selectedDate: row.full_date,
      startTime: timeSpan.startTime,
      endTime: timeSpan.endTime,
      selectedClinic: props.selectedClinic || props.scheduleData?.filters?.id_clinic || 1,
      itemServiceId: props.scheduleData?.filters?.id_item_service || subRow.id_item_service,
      availableEmployees: subRow.employee_id ? [subRow.employee_id] : [],
      selectedEmployee: subRow.employee_id ? String(subRow.employee_id) : undefined,
      slotData: {
        date: row.full_date,
        time_start: timeSpan.startTime,
        time_end: timeSpan.endTime,
        employee_id: subRow.employee_id,
        employee_name: subRow.label,
        slot_max: subRow.slot_max,
        booked: subRow.booked,
        available_capacity: subRow.available_capacity,
        available_status: subRow.available_status,
        can_book: subRow.can_book,
        id_booking_item: subRow.id_booking_item,
        id_item_service: subRow.id_item_service,
      }
    }
    
    try {
      isModalOpen.value = true
      await mtUtils.smallPopup(ScheduleDetailModal, {
        ...modalData,
        bookingItemData: props.bookingItemData,
        onRefresh: () => {
          emit('refresh')
        }
      })
      
      // Emit event so parent can refresh data if needed
      emit('cellClick', {
        row,
        subRow,
        timeHeader: timeSpan.timeHeader,
        cellData: typeof timeSpan.displayText === 'string' ? 
          { display: timeSpan.displayText, can_book: !!timeSpan.isAvailable } : timeSpan.displayText,
        isBookable: true,
        bookableSlot: modalData.slotData
      })
    } catch (error) {
      console.log('Modal cancelled or error:', error)
    } finally {
      isModalOpen.value = false
    }
  }
}

// Get CSS class for cells based on content and level
const getCellClass = (cellData: ColumnData, level: string) => {
  if (!cellData || !cellData.display || cellData.display === '-') {
    return 'cell-empty'
  }
  
  // Add level-specific styling
  if (level === 'L1') {
    return 'cell-l1'
  } else if (level === 'L2') {
    return 'cell-l2'
  } else if (level === 'L3') {
    return 'cell-l3'
  } else if (level === 'L4') {
    return 'cell-l4'
  }
  
  return 'cell-available'
}


// Handle cell click for regular cells (not time spans)
const handleCellClick = async (
  event: Event,
  row: ScheduleRow, 
  subRow: SubRow, 
  timeHeader: string, 
  cellData: ColumnData
) => {
  console.log('🔵 REGULAR CELL CLICK:', {
    level: subRow.level,
    label: subRow.label,
    cellData: cellData
  })
  
  // Stop event propagation to prevent duplicate handlers
  event.stopPropagation()
  event.preventDefault()
  
  // Only allow clicks on L4 rows (final bookable slots in new API)
  if (subRow.level !== 'L4') {
    return // Ignore clicks on L1, L2, L3
  }
  
  // Prevent clicks on unavailable slots
  if (!cellData || cellData.can_book !== true) {
    return // Ignore clicks on unavailable slots
  }
  
  // Only show modal for L4 rows with valid time data and can_book = true
  if (cellData && cellData.display !== '-' && cellData.can_book === true) {
    // Extract time range from cell data
    const timeRange = extractTimeRangeAndCount(cellData, subRow.level)
    
    if (timeRange) {
      // Prepare modal data for L4 booking
      const modalData = {
        selectedDate: row.full_date,
        startTime: timeRange.startTime,
        endTime: timeRange.endTime,
        selectedClinic: props.selectedClinic || props.scheduleData?.filters?.id_clinic || 1,
        itemServiceId: props.scheduleData?.filters?.id_item_service || subRow.id_item_service,
        slotData: {
          date: row.full_date,
          time_start: timeRange.startTime,
          time_end: timeRange.endTime,
          slot_count: timeRange.slotCount,
          current_bookings: timeRange.currentBookings,
          available_capacity: timeRange.availableCapacity,
          slot_info: timeRange.slotInfo,
          slot_max: subRow.slot_max,
          booked: subRow.booked,
          available_status: subRow.available_status,
          can_book: subRow.can_book,
          id_item_service: subRow.id_item_service,
          id_booking_item: subRow.id_booking_item
        }
      }
      
      try {
        await mtUtils.smallPopup(ScheduleDetailModal, {
          ...modalData,
          bookingItemData: props.bookingItemData,
          onRefresh: () => {
            emit('refresh')
          }
        })
        
        // Emit event so parent can refresh data if needed
        emit('cellClick', {
          row,
          subRow,
          timeHeader,
          cellData,
          isBookable: true,
          bookableSlot: modalData.slotData
        })
      } catch (error) {
        console.log('Modal cancelled or error:', error)
      }
    }
  }
}

// Helper functions for booking types
const getBookingTypeColor = (bookingMethod: number) => {
  switch (bookingMethod) {
    case 1: return 'orange' // BLOCK_DURATION
    case 2: return 'blue'   // MAX_SLOT
    case 3: return 'purple' // TENTATIVE_REQ
    case 4: return 'green'  // SIMPLE_REQ
    default: return 'grey'
  }
}

const getBookingTypeLabel = (bookingMethod: number) => {
  switch (bookingMethod) {
    case 1: return 'Block'
    case 2: return 'Max Slot'
    case 3: return 'Tentative'
    case 4: return 'Simple'
    default: return 'Unknown'
  }
}

// Process rows - now API provides L4 rows directly, so we just use the data as-is
const processedRows = computed(() => {
  if (!props.scheduleData?.rows) return []
  
  // API V2 now provides L4 rows directly, no need for complex processing
  return props.scheduleData.rows
})
</script>

<style lang="scss" scoped>
.schedule-table-wrapper {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 100%;
  table-layout: fixed;
  
  // Header styles
  thead {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    
    .header-row {
      th {
        padding: 12px 8px;
        text-align: center;
        font-weight: 600;
        border-right: 1px solid #dee2e6;
        color: #495057;
        
        &:last-child {
          border-right: none;
        }
        
        &.month-date-header {
          width: 12%;
          background: #e9ecef;
        }
        
        &.info-header {
          width: 18%;
          background: #e9ecef;
        }
        
        &.time-header {
          width: auto;
        }
      }
    }
  }
  
  // Body styles
  tbody {
    .schedule-row {
      border-bottom: 1px solid #dee2e6;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      // Add visual separation between date groups
      &:first-child {
        border-top: 2px solid #adb5bd;
      }
      
      td {
        padding: 8px 12px;
        text-align: center;
        border-right: 1px solid #dee2e6;
        vertical-align: middle;
        
        &:last-child {
          border-right: none;
        }
      }
      
      .month-date-cell {
        font-weight: 600;
        background-color: #f8f9fa;
        width: 12%;
        vertical-align: middle;
        text-align: center;
        border-right: 2px solid #adb5bd;
        position: relative;
        
        // Enhanced styling for rowspan cells
        &[rowspan] {
          border-bottom: 1px solid #dee2e6;
        }
      }
      
      .info-cell {
        text-align: left;
        width: 18%;
        
        .level-indicator {
          font-weight: 500;
          
          &.level-l1-indicator {
            color: #fd7e14;
          }
          
          &.level-l2-indicator {
            color: #28a745;
          }
          
          &.level-l3-indicator {
            color: #007bff;
          }
          
          &.level-l4-indicator {
            color: #6f42c1;
          }
          
          &.level-l5-indicator {
            color: #e83e8c;
          }
        }
      }
      
      .time-slot-cell {
        font-size: 13px;
        
        .cell-content {
          .cell-item {
            margin-bottom: 2px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        
        // Empty cell styling - no background color
        &.cell-empty {
          color: #6c757d;
          background-color: transparent !important;
        }
        
        // Level-specific styling - only L5 gets background color, others stay white
        &.cell-l1:not(.cell-empty) {
          background-color: #ffffff;
          color: #fd7e14;
          border: 1px solid rgba(253, 126, 20, 0.3);
        }
        
        &.cell-l2:not(.cell-empty) {
          background-color: #ffffff;
          color: #28a745;
          border: 1px solid rgba(40, 167, 69, 0.3);
        }
        
        &.cell-l3:not(.cell-empty) {
          background-color: #ffffff;
          color: #007bff;
          border: 1px solid rgba(0, 123, 255, 0.3);
        }
        
        &.cell-l4:not(.cell-empty) {
          background-color: rgba(111, 66, 193, 0.15);
          color: #6f42c1;
          border: 1px solid rgba(111, 66, 193, 0.4);
          cursor: pointer;
          transition: background-color 0.2s;
          
          &:hover {
            background-color: rgba(111, 66, 193, 0.25);
          }
        }
        
        &.cell-unavailable:not(.cell-empty) {
          cursor: not-allowed;
        }
        
        &.cell-multiple:not(.cell-empty) {
          background-color: #d1ecf1;
          color: #0c5460;
        }
        
        &.cell-no-coverage:not(.cell-empty) {
          background-color: #f8d7da;
          color: #721c24;
        }
        
        &.cell-available:not(.cell-empty) {
          background-color: rgba(40, 167, 69, 0.25);
          color: #155724;
          font-weight: 500;
          
          &.clickable {
            cursor: pointer;
            transition: background-color 0.2s;
            
            &:hover {
              background-color: rgba(40, 167, 69, 0.4);
            }
          }
        }
        
        // Time span specific styles
        &.time-span {
          position: relative;
          
          .slot-count-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.7);
          }
          
          // Badge colors based on level (only for non-empty cells)
          &.cell-l1:not(.cell-empty) .slot-count-badge {
            color: #fd7e14;
          }
          
          &.cell-l2:not(.cell-empty) .slot-count-badge {
            color: #28a745;
          }
          
          &.cell-l3:not(.cell-empty) .slot-count-badge {
            color: #007bff;
          }
          
          &.cell-l4:not(.cell-empty) .slot-count-badge {
            color: #6f42c1;
          }
          
          &.cell-l5:not(.cell-empty) .slot-count-badge {
            color: #e83e8c;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .schedule-table-wrapper {
    font-size: 12px;
    
    .schedule-table {
      min-width: 600px;
      
      thead th {
        padding: 8px 4px;
        font-size: 12px;
        
        &.month-date-header,
        &.info-header {
          min-width: 80px;
        }
        
        &.time-header {
          min-width: 100px;
        }
      }
      
      tbody td {
        padding: 6px 8px;
        font-size: 12px;
      }
    }
  }
}
</style>