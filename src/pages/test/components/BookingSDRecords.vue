<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import useBookingTransactionStore from '@/stores/booking-transaction'
import mtUtils from '@/utils/mtUtils'
// @ts-ignore
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'

const props = withDefaults(
  defineProps<{
    selectedClinic?: number | null
  }>(),
  {
    selectedClinic: null
  }
)

// Initialize store
const bookingTransactionStore = useBookingTransactionStore()
const { sdRecords, sdRecordsLoading, sdRecordsError } = storeToRefs(bookingTransactionStore)

// Local state
const sdRecordsFilterDate = ref('')

// Computed values for easier access
const bookingSDRecords = computed(() => sdRecords.value)
const isLoading = computed(() => sdRecordsLoading.value)
const errorMessage = computed(() => sdRecordsError.value)

// Fetch booking SD records using store
const fetchBookingSDRecords = async (filterDate?: string) => {
  if (!props.selectedClinic) {
    mtUtils.autoCloseAlert('病院を選択してください')
    return
  }

  const dateToUse = filterDate || sdRecordsFilterDate.value
  if (!dateToUse) {
    mtUtils.autoCloseAlert('日付を選択してください')
    return
  }

  // Convert date to API format (YYYY-MM-DD) if it's in YYYY/MM/DD format
  const apiFormattedDate = dateToUse.includes('/') ? dateToUse.replace(/\//g, '-') : dateToUse

  try {
    await bookingTransactionStore.fetchBookingSDRecords({
      id_clinic: props.selectedClinic,
      date_start: apiFormattedDate
    })
  } catch (err: any) {
    console.error('Failed to fetch booking SD records:', err)
    // Error is already handled by the store
  }
}

// Handle SD records date filter change
const handleSDRecordsDateChange = (selectedDate: string | null) => {
  if (selectedDate) {
    sdRecordsFilterDate.value = selectedDate
  }
}

// Handle when date is selected from calendar
const handleDateSelect = (selectedDate: string) => {
  if (selectedDate) {
    sdRecordsFilterDate.value = selectedDate
  }
}

// Format datetime for display
const formatDateTime = (datetime: string) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  return date.toLocaleString('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Initialize component
onMounted(() => {
  // Set default SD records filter date to today in YYYY/MM/DD format for MtFormInputDate
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  sdRecordsFilterDate.value = `${year}/${month}/${day}`
})
</script>

<template>
  <!-- Booking SD Records Section -->
  <q-card-section class="booking-sd-records-section">
    <div class="text-h6 q-mb-md">予約サービス詳細記録</div>

    <!-- Date Filter -->
    <div class="row q-gutter-md q-mb-md">
      <div class="col-md-4">
        <MtFormInputDate
          :date="sdRecordsFilterDate"
          label="記録取得日"
          outlined
          @update:date="handleSDRecordsDateChange"
          @selectDate="handleDateSelect"
        />
      </div>
      <div class="col-md-3">
        <q-btn
          color="primary"
          label="記録取得"
          @click="() => fetchBookingSDRecords()"
          :loading="isLoading"
          :disable="!selectedClinic"
          outline
        />
      </div>
    </div>

    <!-- Error Message -->
    <q-banner v-if="errorMessage" rounded class="bg-red-1 text-red-8 q-mb-md">
      <q-icon name="error" />
      {{ errorMessage }}
      <template v-slot:action>
        <q-btn flat color="red-8" label="再試行" @click="fetchBookingSDRecords()" />
      </template>
    </q-banner>

    <!-- Loading -->
    <q-linear-progress v-if="isLoading" indeterminate color="primary" class="q-mb-md" />

    <!-- Records List -->
    <div v-if="bookingSDRecords.length > 0" class="records-container">
      <!-- Records Table -->
      <q-table
        :rows="bookingSDRecords"
        :columns="[
          { name: 'id_service_detail', label: 'サービス詳細ID', field: 'id_service_detail', align: 'left', sortable: true },
          { name: 'datetime_start', label: '開始時間', field: 'datetime_start', align: 'left', sortable: true },
          { name: 'customer_name', label: '顧客名', field: 'customer_name', align: 'left', sortable: true },
          { name: 'pet_name', label: 'ペット名', field: 'pet_name', align: 'left', sortable: true },
          { name: 'isu_name', label: 'ISU名', field: 'isu_name', align: 'left', sortable: true },
          { name: 'employee_doctor_name', label: '担当医', field: 'employee_doctor_name', align: 'left', sortable: true }
        ]"
        row-key="id_service_detail"
        flat
        bordered
        dense
        hide-pagination
        :rows-per-page-options="[0]"
        class="booking-records-table"
      >
        <!-- Custom slot for datetime formatting -->
        <template v-slot:body-cell-datetime_start="props">
          <q-td :props="props">
            {{ formatDateTime(props.value) }}
          </q-td>
        </template>

        <!-- Custom slot for customer info -->
        <template v-slot:body-cell-customer_name="props">
          <q-td :props="props">
            <div class="customer-info">
              <div class="customer-name">{{ props.row.customer_name }}</div>
              <div class="customer-code text-caption text-grey-6">{{ props.row.customer_code }}</div>
            </div>
          </q-td>
        </template>
      </q-table>
    </div>

    <!-- No Data Message -->
    <div v-else-if="!isLoading && sdRecordsFilterDate" class="no-data-message">
      <q-icon name="event_note" size="64px" color="grey-5" />
      <div class="text-h6 text-grey-6 q-mt-md">指定された日付の記録が見つかりません</div>
      <div class="text-body2 text-grey-6 q-mt-sm">{{ sdRecordsFilterDate }} の予約記録はありません</div>
    </div>

    <!-- Initial State Message -->
    <div v-else-if="!isLoading && !sdRecordsFilterDate" class="initial-message">
      <q-icon name="calendar_today" size="64px" color="grey-5" />
      <div class="text-h6 text-grey-6 q-mt-md">日付を選択して記録を取得してください</div>
      <div class="text-body2 text-grey-6 q-mt-sm">上記の日付フィルターで取得したい日付を選択してください</div>
    </div>
  </q-card-section>
</template>

<style lang="scss" scoped>
/* Booking SD Records Section Styles */
.booking-sd-records-section {
  background-color: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

.records-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
}

.booking-records-table {
  .customer-info {
    .customer-name {
      font-weight: 500;
      margin-bottom: 2px;
    }
    
    .customer-code {
      font-size: 11px;
    }
  }
}

.pagination-info {
  .q-chip {
    font-weight: 500;
  }
}

.no-data-message,
.initial-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

// Responsive adjustments for booking records
@media (max-width: 768px) {
  .booking-records-table {
    font-size: 12px;
    
    :deep(.q-table__container) {
      .q-table {
        th, td {
          padding: 8px 4px;
          font-size: 11px;
        }
      }
    }
  }
  
  .customer-info {
    .customer-name {
      font-size: 12px;
    }
    
    .customer-code {
      font-size: 10px;
    }
  }
}
</style>