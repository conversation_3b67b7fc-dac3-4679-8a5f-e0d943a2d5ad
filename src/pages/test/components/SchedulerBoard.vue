<template>
  <div class="board">
    <!-- Enhanced Schedule Table for API V2 Format -->
    <div v-if="scheduleData && scheduleData.time_headers" class="enhanced-schedule">
      <enhanced-schedule-table
        :schedule-data="processedScheduleData"
        :bookable-slots="bookableSlots"
        :loading="loading"
        :selected-clinic="selectedClinic"
        :booking-item-data="bookingItemData"
        @cell-click="handleCellClick"
        @refresh="handleRefresh"
      />
    </div>

    <!-- Legacy Schedule Board (fallback) -->
    <div v-else class="legacy-schedule">
      <div class="board__hourlist">
        <div class="left"></div>
        <div class="hour-label" v-for="i in (endHour-startHour-1)" :key="i">
          {{(i+startHour)%12==0?12:(i+startHour)%12}} {{i+startHour>=12?'PM':'AM'}}
        </div>
      </div>
      <div class="board__schedulelist">
        <div class="board__hourspan">
          <div class="date"></div>
          <div class="left"></div>
          <div class="hour-span" v-for="i of (endHour-startHour)*2" :key="i.x">
          </div>
        </div>
        <div class="schedules">
          <day v-for="(it,index) in dayList" :key="index" :startHour="startHour" @click="handleClick"
            @dblclick="handleDblClick" :persons="filterPersons(it)" :day='it' />
        </div>
        <div class="board__bottom"> </div>
      </div>
    </div>
  </div>
</template>
<script>
import Day from "./Day.vue";
import EnhancedScheduleTable from "./EnhancedScheduleTable.vue";
import { sameDay } from "./DateUtil.js";
import { validateScheduleData, processEmployeeScheduleData } from "./ScheduleUtils.js";

export default {
  components: {
    Day,
    EnhancedScheduleTable
  },
  props: {
    // Legacy props for backward compatibility
    persons: {
      type: Array,
      default: () => []
    },
    startHour: {
      type: Number,
      default: 8
    },
    endHour: {
      type: Number,
      default: 18
    },
    // New props for API V2 format
    scheduleData: {
      type: Object,
      default: () => null
    },
    bookableSlots: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    selectedClinic: {
      type: Number,
      default: null
    },
    bookingItemData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      spanList: [],
      dayList: []
    };
  },
  computed: {
    personLength() {
      return this.persons.length;
    },
    
    // Process the schedule data to format employee rows properly
    processedScheduleData() {
      if (!this.scheduleData) return null;
      
      console.log('SchedulerBoard: Processing schedule data:', this.scheduleData);
      
      // New API V2 provides data in the correct format, minimal processing needed
      const processed = {
        ...this.scheduleData,
        // Ensure we have the summary data with booking type breakdown
        summary: this.scheduleData.summary || {
          total_slots: 0,
          available_slots: 0,
          dates_count: 0,
          time_headers_count: this.scheduleData.time_headers?.length || 0,
          availability_rate: 0,
          booking_type_breakdown: {}
        }
      };
      
      // Log booking type breakdown for debugging
      if (processed.summary?.booking_type_breakdown) {
        console.log('SchedulerBoard: Booking type breakdown:', processed.summary.booking_type_breakdown);
      }
      
      // Check for L4 rows in processed data
      if (processed?.rows) {
        const processedL4Count = processed.rows.reduce((count, row) => {
          return count + (row.sub_rows?.filter(sr => sr.level === 'L4').length || 0);
        }, 0);
        console.log(`SchedulerBoard: Found ${processedL4Count} L4 rows in processed data`);
      }
      
      return processed;
    }
  },
  watch: {
    personLength() {
      this.render();
    }
  },
  methods: {
    // Legacy methods for backward compatibility
    handleClick(day, person) {
      this.$emit("click", day, person);
    },
    handleDblClick(day, person) {
      this.$emit("dblclick", day, person);
    },
    
    // New method for enhanced schedule table cell clicks
    handleCellClick(cellData) {
      console.log('Schedule cell clicked:', cellData);
      
      // Emit slot-click event with enhanced data
      this.$emit("slot-click", {
        date: cellData.row.full_date,
        timeHeader: cellData.timeHeader,
        level: cellData.subRow.level,
        cellData: cellData.cellData,
        isBookable: cellData.isBookable,
        bookableSlot: cellData.bookableSlot,
        row: cellData.row,
        subRow: cellData.subRow
      });
    },
    
    handleRefresh() {
      console.log('Schedule refresh requested');
      this.$emit("refresh");
    },
    
    /**
     *
     * @param {Date} date
     */
    getDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
    filterPersons(date) {
      return this.persons.filter(
        it =>
          it.timeSlots.filter(time =>
            sameDay(new Date(time.startTime), new Date(date))
          ).length > 0
      );
    },

    render() {
      this.dayList = [];
      let dateSet = new Set();
      this.persons.forEach(it => {
        it.timeSlots.forEach(time => {
          let date = this.getDate(new Date(time.startTime));
          dateSet.add(date);
        });
      });
      this.dayList = Array.from(dateSet);
      this.dayList.sort();
    },

    // Validate and parse schedule data
    validateScheduleData(data) {
      return validateScheduleData(data);
    }
  },
  mounted() {
    this.render();
  }
};
</script>

<style lang="scss">
$line-color: #ddd;

.board {
  color: #182026;
  
  .enhanced-schedule {
    width: 100%;
    overflow-x: auto;
  }
  
  .legacy-schedule {
    width: 100%;
  }

  &__bottom {
    position: sticky;
    border: 1px solid $line-color;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 500;
  }

  &__hourlist {
    display: flex;
    padding: 0 5%;
    position: sticky;
    background-color: #fff;
    border-bottom: 1px solid $line-color;
    z-index: 2000;
    top: 0;
    .left {
      flex-shrink: 0;
      width: 180px;
    }
    .hour-label {
      padding: 5px;
      font-size: 0.9em;
      order: 1;
      width: 100%;
      text-align: center;
    }
  }

  &__schedulelist {
    position: relative;
    left: 0px;
    top: 0px;
    width: 100%;
    height: auto;

    .schedules {
      left: 0px;
      top: 0px;
      width: 100%;
    }
  }

  &__hourspan {
    width: 100%;
    position: absolute;
    display: flex;
    height: 100%;

    .date {
      flex-shrink: 0;
      width: 80px;
      border-left: 1px solid $line-color;
    }

    .left {
      flex-shrink: 0;
      width: 100px;
      border-left: 1px solid $line-color;
      border-right: 1px solid $line-color;
    }
    .hour-span {
      font-size: 0.8em;
      border-right: 1px solid $line-color;
      border-top: 1px solid $line-color;
      order: 1;
      width: 100%;
    }
  }
}
</style>