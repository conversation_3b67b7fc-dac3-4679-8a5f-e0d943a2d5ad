<script lang="ts" setup>
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputText from '@/components/form/MtFormInputText.vue'
import MtSearchCustomer from '@/components/MtSearchCustomer.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import { computed, nextTick, onMounted, ref } from 'vue'
import useCustomerStore from '@/stores/customers'
import { storeToRefs } from 'pinia'
import {
  aahTruncate, copyText, formatDate,
  formatJPPhoneNumber, getCurrentPetAge,
  getCustomerLabelColor,
  getFullPetNameWithoutHonorific,
  isDateOutOfToday
} from '@/utils/aahUtils'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import dayjs from '@/boot/dayjs'
import useCommonStore from '@/stores/common'
import { typePetGender, typeTitlePetCustomerUpdated } from '@/utils/enum'
import mtUtils from '@/utils/mtUtils'
import UpdateCustomerPetMemoModal from '@/pages/master/customerPet/UpdateCustomerPetMemoModal.vue'
import OptionModal from '@/components/OptionModal.vue'
const customerStore = useCustomerStore()
const { getCustomerListOptions, getCustomer, getPet } = storeToRefs(customerStore)

const props = defineProps<{
  id_customer: string | null | undefined,
  id_pet: string | null | undefined,
  is_edit: boolean
}>()

const emits = defineEmits(['close'])

const commonStore = useCommonStore()
const selectedCustomer = ref(props.customerId)
const selectedPet = ref(props.petId)
const defaultCustomersList = ref([])
const sampleConversation = [
  {
    "speaker": "1",
    "clinic_staff": true,
    "speech": "おはようございます。本日のご予約内容を確認させていただきます。",
    "datetime": "2025-04-29T09:00:00+09:00"
  },
  {
    "speaker": "2",
    "clinic_staff": false,
    "speech": "はい、10時に歯科検診で予約しています。",
    "datetime": "2025-04-29T09:00:07+09:00"
  },
  {
    "speaker": "1",
    "clinic_staff": true,
    "speech": "承知しました。では、お名前と生年月日をお願いします。",
    "datetime": "2025-04-29T09:00:15+09:00"
  },
  {
    "speaker": "2",
    "clinic_staff": false,
    "speech": "山田太郎、1990年5月1日です。",
    "datetime": "2025-04-29T09:00:22+09:00"
  }
]
const callHistoryForm = ref({
  phone: '',
  id_pet: '',
  id_customer: '',
  datetime_call_start: '',
  datetime_call_end: '',
  call_duration: 0,
  flg_confirmed: false,
  datetime_memocarte: '',
  json_call_transcript: sampleConversation,
  memo_call_summary: '',
  memo_call_other: ''
})
const init = () => {
  
}

const getDateTimeCallStart = computed(() => {
  return callHistoryForm.value.datetime_call_start ? dayjs(callHistoryForm.value.datetime_call_start).format('YYYY/MM/DD   hh:mm') : '---'
})
const getDateTimeCallEnd = computed(() => {
  return callHistoryForm.value.datetime_call_end ? dayjs(callHistoryForm.value.datetime_call_end).format('YYYY/MM/DD   hh:mm') : '---'
})
const getDateTimeMemoCarte = computed(() => {
  return callHistoryForm.value?.datetime_memocarte ? dayjs(callHistoryForm.value?.datetime_memocarte, 'YYYY/MM/DD  hh:mm') : '---'
})
const getDuration = computed(() => {
  const seconds = callHistoryForm.value?.call_duration;
  if (!seconds) return '---';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = '';
  if (hours > 0) result += `${hours}h `;
  if (minutes > 0) result += `${minutes}m `;
  if (secs > 0) result += `${secs}s`;

  return result.trim();
})
const closeModal = () => {
  emits('close')
}
onMounted(async () => {
  
  if (getCustomerListOptions.value.length === 0) {
    await customerStore.fetchPreparationCustomers()
  }
  defaultCustomersList.value.push(...getCustomerListOptions.value)
  // check for customer
  if(props.id_customer) {

    await customerStore.selectCustomer(props.id_customer, true, {
      include_upfront_balance: 1
    }).then(() => {
      callHistoryForm.value.id_customer = props.id_customer
      customerPetList.value.length = 0
      customerPetList.value.push(...getCustomer.value?.pets || [])
      if(props.id_pet) {
        callHistoryForm.value.id_pet = props.id_pet
        customerStore.selectPet(props.id_pet)
      }
    })
  } else {
    customerStore.customer = {}
  }
})


const selectPetKey = ref(0)
const customerPetList = ref([])
const defaultCustomerPetList = ref([])
const selectingCustomer = async (e) => {
  if(!e) {
    customerStore.pet = {}
  }
  await customerStore.selectCustomer(e, true, {
    include_upfront_balance: 1
  })
  await nextTick()
  customerPetList.value.length = 0;
  customerPetList.value.push(...getCustomer.value?.pets || [])
  defaultCustomerPetList.value.push(...getCustomer.value?.pets || [])
  ++selectPetKey.value
}

const getCustomerCode = computed(() => {
  return getCustomer.value?.code_customer || ''
})

const getCustomerDisplayName = computed(() => {
  return getCustomer.value?.name_customer_display || ''
})

const getCustomerTelephone = computed(() => {
  return getCustomer?.value?.customer_tel || []
})


const selectingPet = async(e) => {
  console.log("selecting pet ", e)
  await customerStore.selectPet(e)
  callHistoryForm.value.id_pet = e
}

const selectPhoneNumber = (phoneNumber) => {
  callHistoryForm.value.phone = phoneNumber
}

const getTypeAnimalColor = (id_cm_animal: number) => {
  const typeAnimal = getTypeAnimal(id_cm_animal)
  return typeAnimal && typeAnimal.text1 ? typeAnimal.text1 : 'transparent'
}

const getTypeAnimal = (id_cm_animal: number) => {
  return commonStore.getCommonTypeAnimalOptionList.find(
    (v: any) => v.id_common == id_cm_animal
  )
}

const typeTitlePetCustomerUpdatedName = (value: any) =>
  typeTitlePetCustomerUpdated.find((item) => item.value === value)

const typeGenderName = (value: number) =>
  typePetGender.find((v) => v.value === value)?.label

const breedName = (value: number) =>
  commonStore.getCommonBreedOptionList.find((v) => v.id_common === value)
    ?.name_common

const memoCustomerPetModal = async () => {
  await mtUtils.mediumPopup(UpdateCustomerPetMemoModal, {
    id_pet: getPet.value?.id_pet
  })
}

const openMenu = async () => {
  let menuOptions = [
    {
      title: 'キャンセル',
      name: 'cancel',
      isChanged: false,
    },
    {
      title: '削除する',
      name: 'delete',
    }
  ]

  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
}

const launchForCall = () => {
  window.location.href = 'tel:'+callHistoryForm.value.phone;
}
const scrollbarContainerRef = ref(null)
const gotoSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if(!element) {
    return
  }
  const top = element.offsetTop
  scrollbarContainerRef.value.setScrollPosition('vertical', top, 400)
}
</script>

<template>
  <MtModalHeader class="col-auto" @closeModal="closeModal">
    <q-toolbar-title
      class="text-grey-900 title2 bold prescription-title"
    >
      通話履歴
    </q-toolbar-title>
    <q-btn round flat @click="openMenu" class="q-mx-sm">
      <q-icon size="xs" name="more_horiz" />
    </q-btn>
  </MtModalHeader>
  <q-card-section class="content">
    <q-splitter :model-value="30" style="height: 100%">
      <template v-slot:before class="">
        <q-scroll-area class="call-section">
          <!--    call input editable    -->
          <div class="row">
            <div class="col-8 flex justify-start items-center flex-1">
              <MtFormInputText class="flex-1" v-model="callHistoryForm.phone" label="対象番号" />
            </div>
            <div class="col-4 flex justify-start  items-center">
              <q-img @click="launchForCall" src="/img/callHistory/add-to-call.png" class="cursor-pointer" v-ripple style="margin-left: 30px" width="67px" height="67px" />
            </div>
          </div>
          <!--     instructions   -->
          <div class="text-grey-700 q-my-md text-subtitle-1">
            <span> 開始方法： </span><br>
            <span> 対象番号を指定後、コールボタンを押下したら発信します。</span><br>
            <span> 通話内容は記録後、自動反映されます。</span><br>
            <span>オーナーを指名し、登録番号から選択も可能です。</span>
          </div>
          <!--    customer select input    -->
          <div class="row">
            <div class="col-8">
              <MtSearchCustomer 
                :applyDefaultClass="false"
                :preSelectedId="callHistoryForm.id_customer"
                class="q-my-md"
                label="顧客を選択"
                custom-option
                @update:selecting="(val) => {
                  callHistoryForm.id_customer = val;
                  selectingCustomer(val)
                }"
              />
            </div>
          </div>
          <!--   Customer Info     -->
          <div class="row" v-if="getCustomer && Object.keys(getCustomer).length > 0">
            <div class="col-8">
              <div class="q-my-md" style="margin-top: 20px">
                <div class="customer-info">
                  <div class=""> {{getCustomerCode}} </div>
                  <div class="flex justify-start items-center">
                    <div class="">
                      {{getCustomerDisplayName}}
                    </div>
                    <div>
                      <q-icon
                        size="12px"
                        name="circle"
                        class="q-ml-xs"
                        :style="{
                          color: getCustomerLabelColor(getCustomer?.type_customer_color)
                        }"
                        :color="getCustomerLabelColor(getCustomer?.type_customer_color)"
                      />
                    </div>
                  </div>
                </div>
                <div class="">
                  <div class="">Phone:</div>
                  <div class="row cursor-pointer" v-for="(item, index) in getCustomerTelephone" :key="index">
                    <div class="flex justify-center items-center cursor-pointer" @click="selectPhoneNumber(item.tel_full)">
                      <span style="margin-right: 8px">{{index + 1}}:</span>
                      <span class="text-blue"  style="letter-spacing: 2px;" >{{formatJPPhoneNumber(item.tel_full)}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--    Pet selection    -->
          <div class="row q-mt-lg">
            <div class="col-8">
              <MtFilterSelect
                :key="selectPetKey"
                label="ペットを選択"
                v-model:options="customerPetList"
                :options-default="defaultCustomerPetList"
                v-model:selecting="callHistoryForm.id_pet"
                @update:selecting="selectingPet"
              />
            </div>
          </div>
          
          <div v-if="getPet && Object.keys(getPet).length > 0 && getCustomer && Object.keys(getCustomer).length" class="flex column q-mt-md" style="gap: 8px">
            <div>
              <span class="caption2 regular text-grey-500"
                          >ペットCD</span
                          >
              <p class="text-grey-700">
                {{ getPet?.code_pet }}
              </p>
            </div>
              <span class="flex items-center justify-start" style="gap: 8px">
              <span class="caption1 regular text-grey-500 q-mb-xs">
                          {{ getCustomer?.name_kana_family + ' ' }}
                          {{ getPet?.name_kana_pet }}
              </span>
              <q-icon
                size="12px"
                name="circle"
                class="q-ml-xs"
                :color="getTypeAnimalColor(getPet?.id_cm_animal)"
                :style="{
                        color: getTypeAnimalColor(getPet?.id_cm_animal)
                      }"
              />
            </span>
              <div class="title2 bold text-black">
                {{
                  getFullPetNameWithoutHonorific(getPet, getCustomer)
                }}
                <span class="q-ml-xs caption2 regular">
                            {{
                    typeTitlePetCustomerUpdatedName(
                      getPet?.type_title_pet_customer_updated
                    )?.label
                  }}
                          </span>
              </div>
              <div class="col-auto">
                        <span class="caption1 regular text-grey-500 q-mt-xs"
                        >性別</span
                        >
                <div class="title2 bold">
                  {{ typeGenderName(getPet?.type_pet_gender) }}
                </div>
              </div>
              <div class="col-auto">
                        <span class="caption1 regular text-grey-500 q-mt-xs"
                        >品種名</span
                        >
                <div class="title2 bold">
                  {{ breedName(getPet?.id_cm_breed) }}
                </div>
              </div>
              <div
                v-if="
                          getPet?.date_birth &&
                          !getPet?.flg_pet_excluded &&
                          !getPet?.flg_deceased
                        "
                class="caption2 regular text-grey-700"
              >
                        <span class="caption1 regular text-grey-500 q-mt-xs"
                        >生年月日</span
                        >
                <div class="title2 bold">
                  {{ formatDate(getPet?.date_birth) }}
                  <span class="q-ml-md">
                            ( 年齢： {{ getCurrentPetAge(getPet) }} )
                          </span>
                </div>
              </div>
              <div>
                <q-btn
                  v-if="getPet && getPet.flg_existence"
                  class="body2 regular border-btn q-ml-sm"
                  color="accent-800"
                  padding="7px 12px"
                  text-color="grey-500"
                  unelevated
                >
                  平均寿命以上
                </q-btn>
              </div>
              <div>
                <q-btn
                  class="bg-grey-300 btn-memo"
                  unelevated
                  @click="memoCustomerPetModal"
                >
                  <div
                    class="text-left"
                    v-html="
                              aahTruncate(
                                customerStore?.getPet?.memo_pet || 'メモ入力',
                                30
                              )
                            "
                  ></div>
                </q-btn>
              </div>
            
          </div>
          
          <!--   pet name section from the view pet detail modal       -->
          
        </q-scroll-area>
      </template>
      <template v-slot:after>
        <div class="q-px-lg ">
          <div class="section-navigation">
            <div class="item" @click="gotoSection('key-points')">要点</div>
            <div class="item" @click="gotoSection('call-content')">通話内容</div>
            <div class=item @click="gotoSection('memo')">メモ</div>
          </div>
        </div>
        <q-scroll-area :visible="false" class="q-px-lg" ref="scrollbarContainerRef" style="height: calc(100% - 50px)">

          <div class="row call-overview dense" id="key-points">
            <div class="col-4 flex column justify-center items-center">
              <div class="header">通話開始時刻</div>
              <div class="row-item">{{getDateTimeCallStart}}</div>
            </div>
            <div class="col-4 flex column justify-center items-center">
              <div class="header">通話終了時刻</div>
              <div class="row-item">{{getDateTimeCallEnd}}</div>
            </div>
            <div class="col-4 flex column justify-center items-center">
              <div class="header">通話開始時刻</div>
              <div class="row-item">{{getDuration}}</div>
            </div>
          </div>
          <MtFormInputText 
            type="textarea" 
            v-model="callHistoryForm.memo_call_summary" 
            :maxlength="3000"
            :rows="6"
            :max-rows="6"
            :counter="true"
            flat
            class="q-mt-lg bg-amber-3"  />
          <div v-if="props.is_edit" class="row q-mb-xl q-mt-lg">
            <div class="col">
            <MtFormCheckBox label="確認した" v-model="callHistoryForm.flg_confirmed" />
            </div>
            <div class="col flex justify-end text-subtitle1">
              <span class="label">前回メモカルテ作成時刻　{{getDateTimeMemoCarte}}</span>
            </div>
          </div>
          <div v-if="props.is_edit" class="">
            <div class="text-weight-bold q-mb-md" id="call-content">通話内容</div>
            <div class="q-mb-md">
              <div class="call-history">
                <div class="title">再生</div>
                <div class="">60日間保存 (ファイル削除日 yyyy/mm/dd )</div>
              </div>
            </div>
            <div class="customer-info">
              <div class="conversation" v-for="(item, index) in callHistoryForm.json_call_transcript" :key="index">
                <div 
                  class="text-weight-bolder"
                  :class="item.speaker == '1' ? 'left-title' : 'right-title'" 
                >Speaker {{item.speaker}}</div>
                <div class="flex items-center" :class="item.speaker == '1' ? 'left' : 'right'" >
                  <div class="">{{item.speech}}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="props.is_edit" style="margin-top: 32px">
            <div class="text-weight-bolder" id="memo">メモ</div>
            <MtFormInputText v-model="callHistoryForm.memo_call_other" autogrow type="textarea" />
          </div>
     
        </q-scroll-area>
      </template>
    </q-splitter>
  </q-card-section>
  
  <q-card-section class="q-bt bg-white col-auto">
    <div class="text-center modal-btn">
      <q-btn outline @click="closeModal()" class="bg-grey-100 text-grey-800">
        <span>閉じる</span>
      </q-btn>
      <q-btn
        unelevated
        color="primary"
        class="q-ml-md"
      >
        <span>保存</span>
      </q-btn>
    </div>
  </q-card-section>
</template>

<style scoped lang="scss">
.customer-info {
  background-color: $disablebtnPulldownBackgroundColor;
  padding: 18px 24px;
}

.conversation {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
  
  .left-title {
    display: flex;
    justify-content: flex-start;
    align-items: start;
  }
  .right-title {
    display: flex;
    justify-content: flex-end;
    align-items: end;
    color: #0057FF;
  }
  .left {
    display: flex;
    justify-content: flex-start;
    padding-left: 25px;
  }
  
  .right {
    display: flex;
    justify-content: flex-end;
    align-items: end;
    padding-right: 0;
    padding-left: 80px;
    color: #0057FF;
  }
  
}

.call-section {
  margin: auto;
  max-width: 100%;
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
  height: 100%;
  display: flex;
  justify-content: start;
  align-items: center;
  flex-direction: column;
}

.label {
  color: #868686;
}

.call-history {
  display: flex;
  justify-content: start;
  align-items: center;
  .title {
    color: #004B81;
    margin-right: 12px;
  }
}
.call-overview {
  width: 100%;
  .header {
    color: #555555;
  }
  .row-item {
    color: #090909;
  }
}

.section-navigation {
  display: flex;
  justify-content: start;
  border-bottom: 1px solid #000000;
  margin-bottom: 22px;
  padding-right: 15px;
  .item {
    min-width: 121px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    cursor: pointer;
    height: 30px;
  }
}
</style>