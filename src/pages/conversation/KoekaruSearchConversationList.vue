<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import MtTable2 from '@/components/MtTable2.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtCustomerInfoLabel from '@/components/customers/MtCustomerInfoLabel.vue'
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'

import mtUtils from '@/utils/mtUtils'
import UpdateSummaryAudioModal from '@/pages/conversation/UpdateSummaryAudioModal.vue'
import useRequestStore from '@/stores/requests'
import useCommonStore from '@/stores/common'

import { getPetImageUrl, handleImageError } from '@/utils/aahUtils'
import { setPageTitle } from '@/utils/pageTitleHelper'
import koekaruApi, { getInstance, secretKey } from '@/boot/axiosKoekaru'
import {
  EmployeeType
} from '@/types/types'
import {
  conversationTypeStatusList
} from '@/utils/enum'

import dayjs from 'dayjs'

import { useRouter } from 'vue-router'
import useClinicStore from '@/stores/clinics'
import useEmployeeStore from '@/stores/employees'

const requestStore = useRequestStore()
const commonStore = useCommonStore()
const employeeStore = useEmployeeStore()

const router = useRouter()

const taskListData = ref<any>([])
const requestCache = ref<any>({})
const tableElement = ref(null),
  page = ref(1)
let timeout: any = null

const employeeList = ref<EmployeeType[]>([]), 
  employeeListDefault = reactive<EmployeeType[]>([])

const columns = [
  // {
  //   name: 'id_conversation',
  //   label: '会話番号',
  //   field: 'id_conversation',
  //   align: 'left'
  // },
  {
    name: 'createdAt',
    label: '診察日時',
    field: 'createdAt',
    align: 'left'
  },
  {
    name: 'name_employee',
    label: '担当者',
    field: 'name_employee',
    align: 'left'
  },
  {
    name: 'name_customer',
    label: 'オーナー',
    field: 'name_customer',
    align: 'left',
    style: 'width: 14%'
  },
  {
    name: 'pet_info',
    label: 'ペット',
    field: 'pet_info',
    align: 'left',
    style: 'width: 14%'
  },
  {
    name: 'number_request',
    label: 'リクエスト番号',
    field: 'number_request',
    align: 'left'
  },
  {
    name: 'task_status',
    label: 'ステータス',
    field: 'task_status',
    align: 'left',
    style: 'width: 10%'
  },
  {
    name: 'mic',
    label: '',
    field: 'mic',
    align: 'center'
  }
]
const searchData = ref({
  id_vet_user: null,
  datetime_created_from: dayjs().subtract(7, 'day').format('YYYY/MM/DD'),
  datetime_created_to: dayjs().format('YYYY/MM/DD'),
  id_employee_info: null,
  task_status: null
})
const moveNext = (e) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    inputs[index + 1].focus()
  } else {
    inputs[1].blur()
    search()
  }
}

const fetchData = async () => {
  try {
    const clinicStore = useClinicStore()

    if (!clinicStore.getClinic) {
      await clinicStore.fetchClinics()
    }

    if (!clinicStore.getClinic) {
      throw new Error('No clinic data available')
    }

    const requestData = await requestStore.fetchSearchConversationList({
      code_clinic: clinicStore.getClinic.code_clinic,
      date_start: searchData.value.datetime_created_from,
      date_end: searchData.value.datetime_created_to,
      id_employee_info: searchData.value.id_employee_info,
      task_status: searchData.value.task_status
    })

    taskListData.value = requestData
  } catch (error) {
    console.error('Error fetching data:', error)
  }
}

const updateConversation = async (conversation: any) => {
  if (!secretKey) await getInstance(useClinicStore().getClinic?.code_clinic)
  const resAudio = await koekaruApi.get(
    `/task-details?id_request=${conversation.id_request}`
  )
  const resOutput = await koekaruApi.get(
    `/output-data?id_conversation=${conversation.id_conversation}`
  )

  const data = {
    ...conversation,
    transcription: resOutput.data.data.transcript,
    transcript_with_timeline: resOutput.data.data.transcript_with_timeline,
    path_full_audio_file: resAudio.data.data.full_audio,
    summary: resOutput.data.data.summary?.answers || []
  }

  if (!resOutput.data.data.summary?.answers) {
    console.warn('No summary data available for conversation:', conversation.id_conversation)
  }

  mtUtils.popup(UpdateSummaryAudioModal, {
    data: data
  })
}

const recordingTime = (row: any) => {
  const startTime = dayjs(row.startTime)
  const endTime = dayjs(row.endTime)
  const duration = endTime.diff(startTime, 'seconds')
  const minutes = String(Math.floor(duration / 60)).padStart(2, '0')
  const seconds = String(duration % 60).padStart(2, '0')
  return `${minutes}:${seconds}`
}

const switchStatusLabel = (status: string) => {
  if (!status) return ''
  const lowerarcaseStatus = status.toLowerCase()
  switch (lowerarcaseStatus) {
    case 'completed':
      return '完了'
    case 'upload':
      return '保留中'
    case 'error':
      return 'エラー | 予期せぬエラーが発生しました。'
    case 'ready':
      return '処理中'
    default:
      return ''
  }
}

const search = async () => {
  try {
    const requestData = await requestStore.fetchSearchConversationList({
      code_clinic: useClinicStore().getClinic.code_clinic,
      date_start: searchData.value.datetime_created_from,
      date_end: searchData.value.datetime_created_to,
      id_employee_info: searchData.value.id_employee_info,
      task_status: searchData.value.task_status
    })

    taskListData.value = requestData
  } catch (error) {
    console.error('Error during search:', error)
  }
}

const getCustomerInfoLabelProps = (row) => {
  return {
    code: row?.code_customer,
    fullKanaName: `${row.name_kana_family} ${row.name_kana_first}`,
    fullName: `${row.name_customer_display}`,
    colorType: row.type_customer_color,
  }
}
const getEmployeeName = (id_employee: number) => {
  const employee = useEmployeeStore().employees.find((employee: any) => employee.id_employee == id_employee)
  return employee?.name_display
}

const openRequest = (row) => {
  const requestDetailRoute = router.resolve({
    name: 'RequestDetail',
    params: { id: row.id_vetty_request }
  })
  window.open(requestDetailRoute.href, '_blank')
}

onMounted(async () => {
  try {
    const clinicStore = useClinicStore()
    await clinicStore.fetchClinics()

    // if (!secretKey) await getInstance()
    await fetchData()
  } catch (error: any) {
    if (error.response?.data?.detail === 'unauthorised access') {
      await getInstance(useClinicStore().getClinic?.code_clinic)
      await fetchData()
    } else {
      console.error('Error in mounting:', error)
    }
  }

  employeeList.value = employeeStore.getAllEmployees.map((employee: EmployeeType) => employee)
  employeeListDefault.push(...employeeList.value)

  setPageTitle('自動要約一覧')
})
</script>

<template>
  <q-page>
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          AI カルテ 一覧
        </q-toolbar-title>
        <div class="row mobile-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate v-model:date="searchData.datetime_created_from" autofocus label="作成日：Start" outlined
                type="date" @keydown.enter="moveNext"
                @update:date="() => searchData.datetime_created_to = searchData.datetime_created_from" />
              <MtFormInputDate v-model:date="searchData.datetime_created_to" class="q-mx-sm" label="作成日：End" outlined
                type="date" @keydown.enter="moveNext" />
              <MtFilterSelect
                :options="employeeList"
                :options-default="employeeListDefault"
                v-model:selecting="searchData.id_employee_info"
                class="q-mr-sm"
                outlined
              />
              <MtFormPullDown
                :options="conversationTypeStatusList"
                v-model:selected="searchData.task_status"
                class="q-mr-sm"
                style="min-width: 220px;"
                outlined
              />
              <q-btn color="accent-800" text-color="white" unelevated @click="search">
                <q-icon name="search" size="20px" />
                検索
              </q-btn>
            </div>
          </div>
        </div>
        <div class="row desktop-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtFormInputDate v-model:date="searchData.datetime_created_from" autofocus label="作成日：Start" outlined
                type="date" @keydown.enter="moveNext"
                @update:date="() => searchData.datetime_created_to = searchData.datetime_created_from" />
              <MtFormInputDate v-model:date="searchData.datetime_created_to" class="q-mx-sm" label="作成日：End" outlined
                type="date" @keydown.enter="moveNext" />

              <q-btn class="q-mx-sm" color="grey-800" text-color="white" unelevated>
                <q-icon name="search" size="20px" />
              </q-btn>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>
    <span ref="tableElement">
      <MtTable2 :columns="columns" :rows="taskListData" :rowsBg="true" class="custody-table q-pt-sm" flat>
        <template v-slot:row="{ row }">
          <td v-for="(col, index) in columns" :key="index"
            :class="col.field === 'id_conversation' ? 'selectable-text' : ''"
            class="text-black cursor-pointer"
            @click="updateConversation(row)">
            <!-- <template v-if="col.field === 'id_conversation'">
              {{ row.id_conversation }}
            </template> -->
            <template v-if="col.field === 'createdAt'">
              <div>{{ dayjs(row.createdAt).format('YYYY/MM/DD') }}</div>
              <div><small class="text-grey-700">{{ row.id_conversation }}</small></div>
            </template>
            <template v-else-if="col.field == 'name_employee'">
              {{ getEmployeeName(row.id_employee_info) }}
            </template>
            <div v-else-if="col.field == 'name_customer'">
              <div v-if="row?.request?.customer">
                <MtCustomerInfoLabel :customer="getCustomerInfoLabelProps(row.request.customer)" show-customer-code />
              </div>
            </div>
            <div v-else-if="col.field == 'number_request'">
              <div v-if="row?.id_vetty_request" class="text-blue cursor-pointer"  @click.stop="openRequest(row)">
                {{ row?.request?.number_request }}
              </div>
            </div>
            <div v-else-if="col.field === 'pet_info'">
              <template v-for="pet in row.request?.customer?.pets" :key="pet.id_pet">
                <template v-if="pet.id_pet == row.id_pet_info">
                  <MtPetInfoLabel :pet="pet" />
                </template>
              </template>
            </div>
            <template v-else-if="col.field === 'task_status'">
              {{ switchStatusLabel(row.task_status) }}
            </template>
            <template v-else-if="col.field === 'mic'">
              <div class="flex items-center q-gutter-x-sm cursor-pointer">
                <div class="flex items-center justify-center">
                  <img class="cursor-pointer" height="22" src="@/assets/img/aiVetty/mic.png" width="17" />
                </div>
                <div class="">確認</div>
              </div>
            </template>
          </td>
        </template>
      </MtTable2>
    </span>
  </q-page>
</template>

<style lang="scss" scoped>
.selectable-text {
  -webkit-touch-callout: text !important;
  -webkit-user-select: text !important;
  user-select: text !important;
}
</style>
