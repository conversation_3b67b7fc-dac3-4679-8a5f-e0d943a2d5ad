<script setup lang="ts">
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import AudioPlayer from '@liripeng/vue-audio-player'
import { useRecording } from '@/pages/memoCarte/useRecording'
import useConversationStore from '@/stores/Conversation'
import { typeAudioSpeedOptions } from '@/utils/enum'
import { storeToRefs } from 'pinia'
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import TextEditModal from '@/components/modals/TextEditModal.vue'
import mtUtils from '@/utils/mtUtils'
import { useTextSelection } from '@/utils/composables/UseTextSelection'
import dayjs from 'dayjs'
import { getCustomerLabelColor, getCustomerName, getPetImageUrl, handleImageError } from '@/utils/aahUtils'
import { useRouter } from 'vue-router'



const { updatedSummary } = useRecording()

const emits = defineEmits(['close'])

const closeModal = () => {
  emits('close')
}

const props = defineProps({ data: Object, searchData: Function })

const conversationStore = useConversationStore()
const router = useRouter()


const searchedQuery = ref(''),
  transcription = ref(props.data.transcription),
  transcriptionSearched = ref('')

const isFeedbackMode = ref(false)

const toggleFeedbackMode = () => {
  isFeedbackMode.value = !isFeedbackMode.value
}

const handleFeedbackComplete = () => {
  isFeedbackMode.value = false
}

const handleMouseSelection = () => {
  if (!isFeedbackMode.value) return

  const selection = window.getSelection()
  const transcriptWindow = document.getElementById('transcript_window')

  if (
    selection &&
    selection.toString().trim() !== '' &&
    transcriptWindow &&
    transcriptWindow.contains(selection.anchorNode)
  ) {
    const fullSentence = selection.anchorNode.parentNode.textContent
    handleSelectionComplete(selection.toString(), fullSentence)
  }
}

const handleSelectionComplete = (text: string, fullSentence: string) => {
  mtUtils.smallPopup(TextEditModal, {
    text,
    fullSentence,
    selectedWord: text,
    conversationId: conversationStore.conversationId,
    onFeedbackComplete: handleFeedbackComplete,
    popup: {
      popupClassName: 'text-edit-popup'
    }
  })
}

const getFullAudioSrc = () => {
  return [props.data?.path_full_audio_file]
}

const audioRef = ref(null)
const currentTime = ref(0)
const isPlaying = ref(false)
const activeChunkIndex = ref({ segmentIndex: -1, chunkIndex: -1 })
const audioElement = ref<HTMLAudioElement | null>(null)

const handleTimeUpdate = (event) => {
  currentTime.value = event.target.currentTime
}

watch([currentTime, isPlaying], ([newTime, playing]) => {

  if (!playing) {
    activeChunkIndex.value = { segmentIndex: -1, chunkIndex: -1 }
    return
  }

  for (let i = 0; i < props.data.transcript_with_timeline.length; i++) {
    const segment = props.data.transcript_with_timeline[i]
    for (let j = 0; j < segment.chunk.length; j++) {
      const chunk = segment.chunk[j]
      if (newTime >= chunk.start_time && newTime <= chunk.end_time) {
        activeChunkIndex.value = { segmentIndex: i, chunkIndex: j }
        return
      }
    }
  }
  activeChunkIndex.value = { segmentIndex: -1, chunkIndex: -1 }
}, { immediate: true })

const handleSegmentClick = async (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const startTime = parseFloat(target.getAttribute('data-start-time') || '0')

  if (!audioRef.value) {
    console.warn('Audio ref not available')
    return
  }

  const audioElement = audioRef.value.$el.querySelector('audio')
  if (!audioElement) {
    console.warn('Audio element not found')
    return
  }

  try {
    audioElement.currentTime = startTime
    currentTime.value = startTime

    if (isPlaying.value) {
      await audioElement.play()
    }
  } catch (error) {
    console.error('Error controlling audio:', error)
  }
}
const setCustomerFullName = (customer: any) => {
  return `${customer.name_kana_family} ${customer.name_kana_first}`
}

onMounted(() => {
  const transcriptWindow = document.getElementById('transcript_window')
  if (transcriptWindow) {
    transcriptWindow.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (target.classList.contains('transcript-segment')) {
        handleSegmentClick(e)
      }
    })
  }
})

const scrollToHighlight = () => {
  setTimeout(() => {
    const highlightElements = document.getElementsByClassName('highlight')
    if (highlightElements.length > 0) {
      const firstHighlight = highlightElements[0]
      const scrollArea = firstHighlight.closest('.q-scroll-area')

      if (scrollArea) {
        const container = scrollArea.querySelector('.q-scrollarea__container')
        if (container) {
          const highlightOffset = firstHighlight.offsetTop
          container.scrollTop = highlightOffset - 100

          firstHighlight.style.transition = 'background-color 0.3s'
          firstHighlight.style.backgroundColor = '#FFEB3B'
          setTimeout(() => {
            firstHighlight.style.backgroundColor = 'yellow'
          }, 500)
        }
      } else {
        firstHighlight.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    } else {
      console.warn('No highlights found')
    }
  }, 200)
}

const transcriptionHighlighted = computed(() => {
  if (searchedQuery.value && props.data.transcription && !props.data.transcript_with_timeline) {
    const regex = new RegExp(`(${searchedQuery.value})`, 'gi')
    return props.data.transcription.replace(
      regex,
      '<span class="highlight">$1</span>'
    )
  }

  if (!props.data.transcript_with_timeline) {
    return props.data.transcription
  }

  const formattedText = props.data.transcript_with_timeline.map((segment, segmentIndex) => {
    return segment.chunk.map((chunk, chunkIndex) => {
      const isActive = isPlaying.value &&
        activeChunkIndex.value.segmentIndex === segmentIndex &&
        activeChunkIndex.value.chunkIndex === chunkIndex

      let text = chunk.text.trim()

      if (searchedQuery.value) {
        const regex = new RegExp(`(${searchedQuery.value})`, 'gi')
        text = text.replace(regex, '<span class="highlight">$1</span>')
      }

      return `<span 
        class="transcript-segment${isActive ? ' active-transcript' : ''}"
        data-segment="${segmentIndex}"
        data-chunk="${chunkIndex}"
        data-start-time="${chunk.start_time}"
        data-end-time="${chunk.end_time}"
      >${text}</span>`
    }).join(' ')
  }).join(' ')

  return formattedText
})

watch(activeChunkIndex, (newValue) => {
}, { deep: true })

const searchKeyword = () => {
  if (!searchedQuery.value?.trim()) return

  // Normalize search query
  searchedQuery.value = searchedQuery.value.trim()

  // Force Vue to update the DOM first
  nextTick(() => {
    scrollToHighlight()
  })
}

const newUpdatedSummary = computed(() => {
  return updatedSummary(props.data.summary)
})

const {
  handleTouchStart: onTouchStart,
  handleTouchMove: onTouchMove,
  handleTouchEnd: onTouchEnd
} = useTextSelection({
  containerId: 'transcript_window',
  onSelectionComplete: (text) => {
    if (!isFeedbackMode.value) return
    handleSelectionComplete(text, text)
  }
})

const onPlay = () => {
  isPlaying.value = true
}

const onPause = () => {
  isPlaying.value = false
}
</script>

<template>
  <MtModalHeader @closeModal="closeModal">
    <div class="flex items-center q-gutter-md full-width col">
      <div class="text-grey-900 bold title2"> 文字おこし / 要約 / 音声</div>
      <div class="text-grey-900">
        {{ dayjs(data.createdAt).format('YYYY/MM/DD') }}
      </div>
      <div class="">
        <span class="caption2 regular text-grey-700">
          {{ setCustomerFullName(data.request.customer) }}
        </span>
        <div>
          {{ getCustomerName({
            name_customer_display: data.request.customer.name_customer_display,
            name_family: data.request.customer.name_kana_family,
            name_corporate: data.request.customer.name_corporate
          }) }}
          <q-icon v-if="data.request.customer.type_customer_color"
            :color="getCustomerLabelColor(data.request.customer.type_customer_color)" :style="{
              color: getCustomerLabelColor(data.request.customer.type_customer_color)
            }" class="q-ml-xs" name="circle" size="12px" />
        </div>
      </div>
      <div class="cursor-pointer" @click="router.push(`/SearchRequestList/${data.id_vetty_request}`)">
        {{ data?.request?.number_request }}
      </div>

    </div>
  </MtModalHeader>
  <q-card-section class="q-px-xl">
    <div class="row">
      <div :class="newUpdatedSummary && newUpdatedSummary.length > 0 ? 'col-6' : 'col-12'
        ">
        <div class="text-center header text-white q-py-xs q-mb-sm" style="flex: 1">
          文字おこし
        </div>
        <div class="q-py-sm q-pr-xs">
          <div v-if="isFeedbackMode" class="feedback-mode-header">
            誤変換がある単語を選択してください
          </div>
          <q-scroll-area style="width: 100%; max-width: 100%; height: calc(100vh - 385px)"
            class="separate-scrollbar summary q-pa-sm" :class="{ 'feedback-mode': isFeedbackMode }">
            <div class="flex-grow-container relative">
              <div id="transcript_window" class="highlightable-input" v-html="transcriptionHighlighted"
                @mouseup="handleMouseSelection" @touchstart.stop="onTouchStart" @touchmove.stop="onTouchMove"
                @touchend.stop="onTouchEnd" />
              <q-btn class="absolute corner-button" round flat color="#4E6E9D" icon="o_feedback"
                @click="toggleFeedbackMode" />
            </div>
          </q-scroll-area>
        </div>
        <div class="flex items-center q-mt-md">
          <MtInputForm type="text" v-model="searchedQuery" outlined @keydown.enter="searchKeyword" />
          <q-btn icon="search" color="primary" rounded class="q-ml-sm" @click="searchKeyword" />
        </div>
      </div>
      <div class="col-6" v-if="newUpdatedSummary && newUpdatedSummary.length > 0">
        <div class="text-center bg-accent-900 text-white q-py-xs q-mb-sm">
          要約
        </div>
        <div class="q-py-sm q-pl-xs">
          <q-scroll-area style="width: 100%; max-width: 100%; height: calc(100vh - 385px)"
            class="separate-scrollbar summary q-pa-sm">
            <div class="row q-col-gutter-md">
              <template v-for="(item, idx) in newUpdatedSummary" :key="`${idx}-${item.id_question_detail}`">
                <div :class="newUpdatedSummary.length && newUpdatedSummary.length > 1
                  ? 'col-6'
                  : 'col-12'
                  ">
                  <div class="summary-title">
                    {{ item.question_display_title }}
                  </div>
                  <q-input type="textarea" v-model="item.ai_summary" autogrow borderless
                    class="q-mt-xs answer summary-ai-answer bg-white q-pa-sm" />
                </div>
              </template>
            </div>
          </q-scroll-area>
        </div>
      </div>
    </div>
    <div class="q-mt-md full-audio-player">
      <audio-player v-if="getFullAudioSrc().length > 0" ref="audioRef" :audio-list="getFullAudioSrc()"
        theme-color="#BC6EFF" :show-next-button="false" :show-prev-button="false"
        :playback-rates="typeAudioSpeedOptions.map((v) => v.value)" @timeupdate="handleTimeUpdate" @play="onPlay"
        @pause="onPause" />
    </div>
  </q-card-section>

</template>

<style lang="scss" scoped>
.header {
  background-color: var(--System-Gray-900, #212121);
}

.summary {
  background-color: $accent-050;

  &.content-sec {
    height: calc(100vh - 385px);
  }

  .summary-title {
    color: var(--System-Gray-900, #212121);
    font-weight: bold;
    font-size: 15px;
  }

  .answer {
    min-height: calc(100% - 32px);
    margin: 10px 0;

    :deep(textarea) {
      padding-top: 0 !important;
      line-height: 1.6;
    }
  }
}

.separate-scrollbar {
  :deep(.q-scrollarea__content) {
    max-height: unset !important;
  }
}

.full-audio-player {
  width: 50%;
  margin: 16px auto;
}

.highlightable-input {
  min-height: 300px;
  font-weight: 400;
  letter-spacing: 0.00937em;
  padding: 12px;
  color: rgba(0, 0, 0, 0.87);
  border-radius: 8px 8px 0 0;
  line-height: 1.8;

  :deep(.transcript-segment) {
    display: inline-block;
    padding: 2px 4px;
    margin: 0 2px;
    border-radius: 3px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background-color: rgba(188, 110, 255, 0.1) !important;
    }

    &.active-transcript {
      background-color: rgba(188, 110, 255, 0.2) !important;
      box-shadow: 0 0 0 2px rgba(188, 110, 255, 0.1) !important;
    }
  }
}

:deep(.highlight) {
  background-color: yellow;
  padding: 2px;
  border-radius: 2px;
  scroll-margin: 20px;
}

.q-scroll-area {
  scroll-behavior: smooth;
}

.feedback-mode-header {
  background-color: #FFFF;
  color: black;
  padding: 8px 16px;
  border-radius: 4px 4px 0 0;
  font-size: 14px;
  text-align: center;
  margin-bottom: -4px;
}

.corner-button {
  position: absolute;
  top: -10px;
  right: -10px;
}

.summary {
  &.feedback-mode {
    background-color: #FFE8E8;

    ::selection {
      background-color: #FFEB3B;
      color: black;
    }

    :deep(::selection) {
      background-color: #FFEB3B;
      color: black;
    }
  }
}
</style>
