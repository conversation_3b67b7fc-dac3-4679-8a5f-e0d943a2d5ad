<script lang="ts" setup>
import { onMounted, ref, computed, defineAsyncComponent, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'

// Components
import MtTable2 from '@/components/MtTable2.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import UpdateEmpInfoModal from '@/pages/empInfo/UpdateEmpInfoModal.vue'
import UpdateMessageThreadModal from '@/pages/message/UpdateMessageThreadModal.vue'
import UpdateTaskModal from '@/pages/task/UpdateTaskModal.vue'
import UploadFile from '@/components/UploadFile.vue'


// Lazy-loaded Components (if applicable)
const ViewEmpInfoModal = defineAsyncComponent(() => import('@/pages/empInfo/ViewEmpInfoModal.vue'))

// Stores
import useAuthStore from '@/stores/auth'
import useEmployeeStore from '@/stores/employees'
import useTask from '@/stores/task'
import useEmpInfoStore from '@/stores/empInfo'
import { useDashboardStore } from '@/stores/dashboard'
import useCliCommonStore from '@/stores/cli-common'
import useMessageStore from '@/stores/message-clinic'

// Types
import {
  CliCommon,
  Common,
  EmployeeType,
  MessageThreadType,
  TaskType
} from '@/types/types'

// Utils
import { formatDate, formatHoursMinutes, formatDateTime } from '@/utils/aahUtils'
import { typeThreadClassification, typeEmpInfo } from '@/utils/enum'
import { orderBy } from 'lodash'
import dayjs from 'dayjs'
import mtUtils from '@/utils/mtUtils'

const router = useRouter()
const authStore = useAuthStore()
const employeeStore = useEmployeeStore()
const taskStore = useTask()
const cliCommonStore = useCliCommonStore()
const empInfoStore = useEmpInfoStore()

const { getAuthUser } = storeToRefs(authStore)

const { getEmployees } = storeToRefs(employeeStore)
const { getCliCommonTypeDepartmentList } = storeToRefs(cliCommonStore)

// Mobile detection
const isMobile = ref(false)
const detectDeviceAndBrowser = () => {
  // Detect mobile devices based on screen width
  isMobile.value = window.innerWidth < 1368
}

const dashboardStore = useDashboardStore();
const getDashboard = computed(() => dashboardStore.state.dashboard)
const getBusinessPlanToday = computed(() => dashboardStore.state.business_plan_today)

const typeDeptList = ref(<Array<Common>>[])
const notificationListByPerson = ref([])
const messageStore = useMessageStore()

const threadColumns = [
  {
    name: 'datetime_update',
    label: '最新メッセージ日時',
    field: 'datetime_update',
    align: 'left',
    style: 'width: 20%'
  },
  {
    name: 'id_employee_ask',
    label: '質問者',
    field: 'id_employee_ask',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'id_employee_answer',
    label: '対象',
    field: 'id_employee_answer',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'purpose',
    label: '目的',
    field: 'purpose',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'name_thread',
    label: 'スレッド名',
    field: 'name_thread',
    align: 'left',
    style: 'width: 25%'
  },
  {
    name: 'status',
    label: '状態',
    field: 'status',
    align: 'left',
    style: 'width: 10%'
  }
]


const customerMessageColumns = [
  {
    name: 'datetime_update',
    label: '最新メッセージ日時',
    field: 'datetime_update',
    align: 'left',
    style: 'width: 25%'
  },
  {
    name: 'owner_name',
    label: 'オーナー',
    field: 'owner_name',
    align: 'left',
    style: 'width: 25%'
  },
  {
    name: 'name_thread',
    label: 'スレッド名',
    field: 'name_thread',
    align: 'left',
    style: 'width: 40%'
  },
  {
    name: 'status',
    label: '状態',
    field: 'status',
    align: 'left',
    style: 'width: 10%'
  }
]

const departmentTaskColumns = [
  {
    name: 'datetime_request',
    label: '最新メッセージ日時',
    field: 'datetime_request',
    align: 'left',
    style: 'width: 20%'
  },
  {
    name: 'id_employee_request',
    label: '依頼者',
    field: 'id_employee_request',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'id_employee_staff',
    label: '主担当者',
    field: 'id_employee_staff',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'title_task',
    label: 'タスク名',
    field: 'title_task',
    align: 'left',
    style: 'width: 40%'
  },
  {
    name: 'status',
    label: '状態',
    field: 'status',
    align: 'left',
    style: 'width: 10%'
  }
]

const empInfoColumns = [
  {
    name: 'type_emp_info',
    label: '区分・連絡日',
    field: 'type_emp_info',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'title',
    label: 'タイトル',
    field: 'title',
    align: 'left',
    style: 'width: 20%'
  },
  {
    name: 'id_employee_posted',
    label: 'From',
    field: 'id_employee_posted',
    align: 'left',
    style: 'width: 15%'
  },
  {
    name: 'memo_emp_info',
    label: '連絡内容',
    field: 'memo_emp_info',
    align: 'left',
    style: 'width: 48%'
  },
  {
    name: 'file_path1',
    label: '',
    field: 'file_path1',
    align: 'left',
    style: 'width: 2%'
  },
]

const selectedDepartment = ref(localStorage.getItem('userTypeDepartment'))
const selectedEmployee = ref(localStorage.getItem('id_employee'))

const handleThreadType = (value: number) => {
  return typeThreadClassification.find((items: any) => items.value === value)
    ?.label
}

const openMessageClinic = (message_thread: MessageThreadType) => {
  let link = router.resolve({
    name: 'MessageClinic',
    query: { mt: message_thread.id_message_thread }
  })?.href
  if (link) {
    window.open(link, '_blank')
  }
}

const openDetailTaskPage = (task: TaskType) => {
  taskStore.selectTask(task.id_task)
  localStorage.setItem('pageAction', 'taskDetails')
  localStorage.setItem('pageActionParam', task.id_task)

  let link = router.resolve({
    name: 'SearchTaskList',
    query: { id: task.id_task }
  })?.href
  if (link) {
    window.open(link, '_blank')
  }
}
const openTaskPage = (task: TaskType) => {
  let link = router.resolve({ name: 'SearchTaskList' })?.href
  if (link) {
    window.open(link, '_blank')
  }
}
const openCustomerMessagePage = () => {
  let link = router.resolve({ name: 'MessageCustomer' })?.href
  if (link) {
    window.open(link, '_blank')
  }
}
const openCustomerMessageByIdPage = (message_thread: MessageThreadType) => {
  let link = router.resolve({
    name: 'MessageCustomer',
    query: { mt: message_thread.id_message_thread }
  })?.href
  if (link) {
    window.open(link, '_blank')
  }
}
const openMessageClinicPage = () => {
  let link = router.resolve({ name: 'MessageClinic' })?.href
  if (link) {
    window.open(link, '_blank')
  }
}
const openClinicPlanList = () => {
  let link = router.resolve({ name: 'SearchClinicPlanList' })?.href
  if (link) {
    window.open(link, '_blank')
  }
}

const openEmpInfoModal = async () => {
  await mtUtils.popup(UpdateEmpInfoModal, {reinitDashoard: init})
}

const openMessageModal = async () => {
  messageStore.selectMessage(null)
  await mtUtils.popup(UpdateMessageThreadModal, {reinitDashoard: init})
}

const openTaskPageModal = async () => {
  taskStore.selectTask(null)
  await mtUtils.popup(UpdateTaskModal, {reinitDashoard: init}, true)
}

const openEmpInfoListPage = () => {
  let link = router.resolve({ name: 'SearchEmpInfoList' })?.href
  if (!link) return
  
  // For iOS/iPad Safari
  if (navigator.userAgent.match(/ipad|iphone|ipod/i)) {
    // Create and trigger a click on an anchor element
    const a = document.createElement('a')
    a.setAttribute('href', link)
    a.setAttribute('target', '_blank')
    a.setAttribute('rel', 'noopener noreferrer')
    
    // The element needs to be on the DOM for Safari
    document.body.appendChild(a)
    a.click()
    
    // Clean up
    document.body.removeChild(a)
  } else {
    // For other browsers, continue using window.open
    window.open(link, '_blank')
  }
}

const getEmployeeName = (id_employee: number) => {
  const employee = getEmployees.value.find(
    (v: EmployeeType) => v.id_employee === id_employee
  )
  return employee?.name_display
}

const changeSelectedDepartment = async (v: string) => {
  dashboardStore.setSelectedDepartment(v)
  await init()
}

const init = async () => {
  if (
    getAuthUser.value.id_clinic_list.length > 1 &&  
    !localStorage.getItem('selectedClinic')             
  ) {
    return
  }
  dashboardStore.setSelectedDepartment(selectedDepartment.value)
  const storedClinicId = localStorage.getItem('id_clinic')
  const authClinicID = authStore!.getAuthUser?.id_clinic;  

  await mtUtils.promiseAllWithLoader([
    dashboardStore.fetchDashBoard({
      type_department: selectedDepartment.value,
      id_clinic: authClinicID ?? storedClinicId,
      id_employee: selectedEmployee.value,
    })])


  let listPerPerson = <any>[]

  // Count Customer Message List Per Person
  getDashboard.value.customer_message_thread_list.filter((data) => {
    return data.type_department === parseInt(selectedDepartment.value)
  }).forEach((curr) => {
    const data = listPerPerson.find(
      (item) => item.id_employee === curr.id_employee_answer
    )
    if (data) data.count_customer_mtl++
    else
      listPerPerson.push({
        id_employee: curr.id_employee_answer,
        count_customer_mtl: 1,
        count_clinic_mtl: 0,
        count_task_list: 0
      })
  })

  // Count Task List Per Person
  getDashboard.value.task_list.filter((data) => {
    return data.type_department === parseInt(selectedDepartment.value)
  }).forEach((curr) => {
    const data = listPerPerson.find(
      (item) => item.id_employee === curr.id_employee_staff
    )
    if (data) data.count_task_list++
    else
      listPerPerson.push({
        id_employee: curr.id_employee_staff,
        count_customer_mtl: 0,
        count_clinic_mtl: 0,
        count_task_list: 1
      })
  })

  notificationListByPerson.value = orderBy(listPerPerson, 'id_employee')
}

// this function use for Auto Reset Password
function daysBetweenDates(date: string): number {
  const givenDate: Date = new Date(date)
  const currentDate: Date = new Date()
  const differenceInMilliseconds: number =
    currentDate.getTime() - givenDate.getTime()
  const millisecondsPerDay: number = 1000 * 60 * 60 * 24
  const differenceInDays: number = differenceInMilliseconds / millisecondsPerDay

  return Math.floor(differenceInDays)
}

const loginPasswordUpdatedDays = daysBetweenDates(
  authStore!.getAuthUser?.datetime_login_pw_updated as string
)

const isNotificationEmpty = computed(() => {
  const { employee_message_thread_list, customer_message_thread_list, task_list } = getDashboard.value;

  return (
    getDashboard.value &&
    (
      (employee_message_thread_list && employee_message_thread_list.length > 0) ||
      (customer_message_thread_list && customer_message_thread_list.length > 0) ||
      (task_list && task_list.length > 0)
    )
  );
});

const getTypeEmpInfo = (type_emp_info: number) => typeEmpInfo.find((item: any) => item.value === type_emp_info)?.label

const openViewEmpInfoModal = async (row: typeEmpInfo) => {
  let attr = {
    isConfirmed: false
  }
  await empInfoStore.selectEmpInfo(row.id_emp_info)
  await mtUtils.mediumPopup(ViewEmpInfoModal, {data: row, attr})

  // If changes were made (read status updates), refresh the dashboard to update count_read
  if (attr.isConfirmed) {
    await init()
  }
}

const getEmployeeInfo = (id: number) => {
  const info = getEmployees.value.find((emp: EmployeeType) => {
    return emp.id_employee === id
  })

  return {
    name: info?.name_display,
    picture: info?.image_path1
  }
}

// Carousel functionality
const cardsContainer = ref<HTMLElement | null>(null)
const showLeftArrow = ref(false)
const showRightArrow = ref(true)

const handleScroll = () => {
  if (!cardsContainer.value) return

  const container = cardsContainer.value
  const scrollLeft = container.scrollLeft
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth

  showLeftArrow.value = scrollLeft > 10
  showRightArrow.value = scrollLeft < (scrollWidth - clientWidth - 10)
}

// Always show list button when there are items (simplified approach)
const shouldShowListButton = computed(() => {
  const empInfoList = getDashboard.value?.emp_info_list || []
  return empInfoList.length > 0
})

const scrollLeft = () => {
  if (!cardsContainer.value) return
  cardsContainer.value.scrollBy({ left: -300, behavior: 'smooth' })
}

const scrollRight = () => {
  if (!cardsContainer.value) return

  // If right arrow is not showing (meaning we're at end), open list page
  // Otherwise, scroll right
  if (!showRightArrow.value) {
    openEmpInfoListPage()
    return
  }

  cardsContainer.value.scrollBy({ left: 300, behavior: 'smooth' })
}

// Watch for data changes and update scroll arrows
watch(() => getDashboard.value?.emp_info_list, () => {
  nextTick(() => {
    handleScroll()
  })
}, { deep: true })

const departmentTaskList = computed(() => {
  return getDashboard.value.task_list.filter((task) => {
    return task.type_department === parseInt(selectedDepartment.value)
  })
})

const empMessageThreadList = computed(() => {
  return getDashboard.value.employee_message_thread_list.filter((msg) => {
    return msg.type_department === parseInt(selectedDepartment.value)
  })
})

const handleTypeDepartementName = (depId: number): string | undefined => {
  const foundDepartment = cliCommonStore.getCliCommonTypeDepartmentList.find(
    (department) => parseInt(department.code_func1) === depId
  );
  return foundDepartment?.label;
};

const formatDateTimePlan = (dateTimeString) => {
  const date = dayjs(dateTimeString);
  // Check if the time part is 00:00:00
  if (date.format("HH:mm:ss") === "00:00:00") {
    return '';
  } else {
    return date.format("HH:mm");
  }
};

onMounted(async () => {
  // Initialize mobile detection
  detectDeviceAndBrowser()
  window.addEventListener('resize', detectDeviceAndBrowser)

  await employeeStore.fetchEmployees()
  typeDeptList.value = getCliCommonTypeDepartmentList.value.map((obj: CliCommon) => ({
    label: obj.name_cli_common,
    value: obj.code_func1
  }))
  if (getAuthUser.value)
    selectedDepartment.value = getAuthUser.value.type_department
      ? getAuthUser.value.type_department
      : 1


  init()

  // Initialize carousel scroll arrows
  nextTick(() => {
    handleScroll()
    // Add resize listener for responsive arrow updates
    window.addEventListener('resize', () => {
      setTimeout(handleScroll, 100)
    })
  })
})
</script>

<template>
  <div class="row">
    <div class="col-xs-12 col-sm-6 col-md-4" style="max-height: calc(100vh - 60px); overflow-y: scroll;">
      <aside class="container-aside">
        <div class="aside-title">
          <q-space></q-space>
          <MtFormPullDown
            v-model:selected="selectedDepartment"
            @update:selected="changeSelectedDepartment"
            :clearable="false"
            :options="typeDeptList"
            class="department-pulldown q-mr-sm"
            label="部署"
            outlined
          />
          <InputEmployeeOptGroup
            v-model:selected="selectedEmployee"
            :department-selected="selectedDepartment?.toString()"
            class="w-200 q-pa-none"
            label="担当者 *"
            show-select-default-employee
            @update:selected="init()"
          />
        </div>
        <template v-if="isNotificationEmpty">
          <div class="all-department">
            <div class="divider-dashboard">
              <span class="body1 bold text-accent-800">部門全体</span>
            </div>
            <div class="all-notifications">
              <div class="notification-content">
                <div class="flex items-center">
                  <p class="notification-title">
                    <q-icon class="notification-icon" name="app_shortcut"/>
                  </p>
                  <div class="q-ml-md">予約受付スレッド</div>
                </div>
                <span class="notification-count">{{
                    empMessageThreadList?.length
                }}</span>
              </div>
              <div class="notification-content">
                <div class="flex items-center">
                  <p class="notification-title">
                    <q-icon class="notification-icon" name="forum"/>
                  </p>
                  <div class="q-ml-md">院内メッセージ</div>
                </div>
                <span class="notification-count">{{
                  getDashboard.customer_message_thread_list?.length
                }}</span>
              </div>
              <div class="notification-content">
                <div class="flex items-center">
                  <p class="notification-title">
                    <q-icon class="notification-icon" name="assignment_turned_in"/>
                  </p>
                  <div class="q-ml-md">部門宛タスク</div>
                </div>
                <span class="notification-count">{{
                  departmentTaskList?.length
                }}</span>
              </div>
            </div>
          </div>
          <div class="assignments">
            <div class="divider-dashboard">
              <span class="body1 bold text-accent-800">担当者別</span>
            </div>
            <div v-for="employee in notificationListByPerson" class="row">
              <div class="col-8">
                <span>{{
                  employee.id_employee
                    ? getEmployeeName(employee.id_employee)
                    : '🐶😸'
                }}</span>
              </div>
              <div class="col-2">
                <span class="assignment-count">
                  <q-icon name="forum" /> {{ employee.count_customer_mtl }}
                </span>
              </div>
              <div class="col-2 q-pl-sm">
                <span class="assignment-count">
                  <q-icon name="assignment_turned_in" />
                  {{ employee.count_task_list }}
                </span>
              </div>
            </div>
          </div>
        </template>
        <div class="noTaskImg" v-else>
          <q-img
            src="@/assets/img/task/no_top_task.png"
            style="width: 70%"
            alt="no_task"
            loading="lazy"
            spinner-color="white"
          />
        </div>
      </aside>
    </div>
    <div class="col-xs-12 col-sm-6 col-md-8">
      <main class="container-main">
        <div class="card">
          <div class="flex justify-end items-center">
            <div @click="openClinicPlanList" class="cursor-pointer text-blue">
              院内予定一覧
            </div>
          </div>

          <div class="card-main">
            <div v-if="getBusinessPlanToday.normal" class="time-table">
              <span class="body1">{{ getBusinessPlanToday?.normal?.name_business_hour }}</span>
              <div v-if="getBusinessPlanToday.normal.time_business_start1" class="body2">
                時間枠1 {{ getBusinessPlanToday.normal.time_business_start1 }} ～
                {{ getBusinessPlanToday.normal.time_business_end1 }}
              </div>
              <div v-if="getBusinessPlanToday.normal.time_business_start2" class="body2">
                時間枠2 {{ getBusinessPlanToday.normal.time_business_start2 }} ～
                {{ getBusinessPlanToday.normal.time_business_end2 }}
              </div>
              <div v-if="getBusinessPlanToday.normal.time_business_start3" class="body2">
                時間枠3 {{ getBusinessPlanToday.normal.time_business_start3 }} ～
                {{ getBusinessPlanToday.normal.time_business_end3 }}
              </div>

            </div>
            <div class="divider-dashboard" />

            <!-- today clinic plan -->
            <div class="time-table">
              <div class="time-table-header">
                <span class="body1 bold text-grey-800">今日の院内予定</span>
                <template v-if="getDashboard && getDashboard?.clinical_plan_today && getDashboard.clinical_plan_today?.length > 3">
                  <span class="body1 text-blue cursor-pointer" @click="openClinicPlanList">他 {{getDashboard.clinical_plan_today?.length - 3}} 件</span>
                </template>
              </div>

              <template v-if="getDashboard && getDashboard?.clinical_plan_today && getDashboard.clinical_plan_today?.length > 0">
                <span v-for="plan in getDashboard.clinical_plan_today?.slice(0, 3)" :key="plan.id_clinic_plan" class="body2">
                  {{
                    plan.datetime_clinic_plan_start ? formatDateTimePlan(plan.datetime_clinic_plan_start) : '-'
                  }} {{plan.title_clinic_plan}}
                </span>
              </template>
              <template v-else>
                <span class="text-grey-500">
                  本日の院内予定なし
                </span>
              </template>
            </div>

            <!-- tomorrow clinic plan -->
            <div class="time-table">
              <span class="body1 bold text-grey-800">明日の院内予定</span>
              <template v-if="getDashboard && getDashboard?.clinical_plan_tomorrow">
                <span v-for="plan in getDashboard.clinical_plan_tomorrow" :key="plan.id_clinic_plan" class="body2">
                  {{
                    plan.datetime_clinic_plan_start ? formatDateTimePlan(plan.datetime_clinic_plan_start) : '-'
                  }} {{plan.title_clinic_plan}}
                </span>
              </template>
              <template v-else>
                <span class="text-grey-500">
                  明日の院内予定なし
                </span>
              </template>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <span class="body1 bold">
              <q-icon name="app_shortcut" /> 院内連絡一覧
            </span>
            <div class="flex items-center">
              <div @click="openEmpInfoModal" class="cursor-pointer text-blue q-mr-md">
                ＋院内連絡
              </div>
            </div>
          </div>
        <!-- Horizontal Card Layout -->
        <div class="emp-info-carousel-container">
          <!-- Left Arrow with Space - Only when visible -->
          <div v-if="showLeftArrow" class="carousel-arrow-container left">
            <span  @click="scrollLeft" class="material-icons-outlined carousel-arrow">
            arrow_circle_left
            </span>
          </div>

          <!-- Cards Container -->
          <div
            ref="cardsContainer"
            class="emp-info-cards-container"
            @scroll="handleScroll"
          >
            <div
              v-for="(row, index) in (getDashboard?.emp_info_list || [])"
              :key="index"
              class="emp-info-card cursor-pointer"
              :class="{ 'has-thumbnail': row.file_path1 || row.file_url }"
              @click.stop="openViewEmpInfoModal(row)"
            >
              <!-- Thumbnail - Only show if file_path1 exists and fill entire card width -->
              <div v-if="row.file_url" class="card-thumbnail-section">
                <UploadFile
                  :disabledPreview="true"
                  :isOnlyView="true"
                  v-if="row.file_url"
                  :fileUrl="row.file_url"
                  :cardThumbnail="true"
                />
              </div>

              <!-- Card Content -->
              <div class="card-content" :class="{ 'no-thumbnail': !(row.file_url) }">
                <!-- Title -->
                <div class="card-title text-grey-900 truncate-lines lines-1" :class="{'mt-title-thumbnail-title': row.file_url}">
                  {{ row.title }}
                </div>

                <!-- Memo -->
                <div
                  class="card-memo text-grey-900 "
                  :class="{ 'truncate-lines lines-2 q-mt-xs': row.file_url, 'truncate-lines lines-7': !row.file_url }"
                  v-html="row.memo_emp_info"
                ></div>

                <!-- Type and Date -->
                <div class="card-type-date flex items-center justify-between" :class="{'q-pt-sm': row.file_url}">
                  <span class="type-emp-info">#{{ getTypeEmpInfo(row.type_emp_info) }}</span>
                  <span class="text-grey-600 font-15px">
                    {{ formatDateTime(row?.datetime_posted) }}
                  </span>
                </div>

                <!-- Employee and Count -->
                <div class="card-employee-count flex items-center justify-between"
                  :class="{ '': row.file_url }">
                  <div class="flex items-center">
                    <q-avatar size="sm" class="q-mr-xs">
                      <img v-if="getEmployeeInfo(row.id_employee_posted).picture" :src="getEmployeeInfo(row.id_employee_posted).picture" alt="profile picture" />
                      <q-icon v-else name="account_circle" size="sm" class="text-grey-500" />
                    </q-avatar>
                    <span class=" font-15px text-grey-900 truncate-lines lines-1">
                      {{ getEmployeeInfo(row.id_employee_posted).name }}
                    </span>
                  </div>
                  <div v-if="row.count_read != null && row.count_read >= 0" class="text-grey-600  font-15px">
                  <span class="material-icons">done_all</span>
                    {{ row.count_read }} 名
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Arrow with Space - Show scroll arrow when can scroll, always show list button -->
          <div v-if="showRightArrow || shouldShowListButton" class="carousel-arrow-container right">
            <span
              @click="scrollRight"
              class="material-icons-outlined carousel-arrow"
              :title="showRightArrow ? '' : '院内連絡一覧を見る'"
            >
              {{ showRightArrow ? 'arrow_circle_right' : 'arrow_circle_right' }}
            </span>
          </div>
        </div>
        </div>  

        <div class="card">
          <div class="card-header">
            <span class="body1 bold">
              <q-icon name="app_shortcut" /> {{typeDeptList.find((el) => el.value == selectedDepartment)?.label}} 予約受付スレッド
            </span>
            <div @click="openCustomerMessagePage" class="cursor-pointer text-blue"> 
              予約受付スレッド 
            </div>
          </div>
          <MtTable2
            v-if="getDashboard && getDashboard.customer_message_thread_list"
            :columns="customerMessageColumns"
            :rows="getDashboard.customer_message_thread_list.slice().splice(0, 5)"
          >
            <template v-slot:row="{ row }">
              <td
                @click="openCustomerMessageByIdPage(row)"
                class="cursor-pointer"
                v-for="(col, index) in customerMessageColumns"
                :key="index"
              >
                <span v-if="col.field === 'datetime_update'">{{
                  formatDate(row.datetime_update) +
                  ' ' +
                  formatHoursMinutes(row.datetime_update)
                }}</span>
                <span v-else-if="col.field === 'owner_name'">{{
                  row?.name_customer_display
                }}</span>
                <span v-else-if="col.field === 'name_thread'">{{
                  row.name_thread
                }}</span>
                <span v-else-if="col.field === 'status'">
                  <q-chip
                    dense
                    class="status-chip"
                    :class="row.flg_closed ? 'end' : 'not-finish'"
                    text-color="white"
                  >
                    {{ row.flg_closed ? '完了' : '未完了'}}
                  </q-chip>
                </span>
              </td>
            </template>
          </MtTable2>
        </div>
        <div class="card">
          <div class="card-header">
            <span class="body1 bold">
              <q-icon name="forum" /> {{typeDeptList.find((el) => el.value == selectedDepartment)?.label}} 院内メッセージ
            </span>
            <div class="flex items-center">
              <div @click="openMessageModal" class="cursor-pointer text-blue q-mr-md">
                ＋院内メッセージ
              </div>
              <div @click="openMessageClinicPage" class="cursor-pointer text-blue">
                院内メッセージ
              </div>
            </div>
          </div>
          <MtTable2
            v-if="getDashboard && getDashboard.employee_message_thread_list"
            :columns="threadColumns"
            :rows="getDashboard.employee_message_thread_list.filter(thread => thread.type_thread_list === 1).slice(0, 5)"
          >
            <template v-slot:row="{ row }">
              <td
                @click="openMessageClinic(row)"
                class="cursor-pointer"
                v-for="(col, index) in threadColumns"
                :key="index"
              >
                <span v-if="col.field === 'datetime_update'">{{
                  formatDate(row.datetime_update) +
                  ' ' +
                  formatHoursMinutes(row.datetime_update)
                }}</span>
                <span v-else-if="col.field === 'id_employee_ask'">{{
                  getEmployeeName(row.id_employee_ask)
                }}</span>
                <span v-else-if="col.field === 'id_employee_answer'">
                  <span v-if="row.type_department">{{ handleTypeDepartementName(row.type_department) }}</span>
                  <span v-if="getEmployeeName(row.id_employee_answer)">（{{ getEmployeeName(row.id_employee_answer)}}）</span>
                </span>
                <span v-else-if="col.field === 'purpose'">{{
                  handleThreadType(row?.type_thread)
                }}</span>
                <div v-else-if="col.field === 'name_thread'">{{
                  row.name_thread
                }}</div>
                <span v-else-if="col.field === 'status'">
                  <q-chip
                    dense
                    class="status-chip"
                    :class="row.flg_closed ? 'end' : 'not-finish'"
                    text-color="white"
                  >
                    {{ row.flg_closed ? '完了' : '未完了'}}
                  </q-chip>
                </span>
              </td>
            </template>
          </MtTable2>
        </div>
        <div class="card">
          <div class="card-header">
            <span class="body1 bold">
              <q-icon name="assignment_turned_in" /> {{typeDeptList.find((el) => el.value == selectedDepartment)?.label}} 部門タスク
            </span>
            <div class="flex items-center">
              <div @click="openTaskPageModal" class="cursor-pointer text-blue q-mr-md">
                ＋タスク
              </div>
              <div @click="openTaskPage" class="cursor-pointer text-blue">
                タスク一覧
              </div>
            </div>
          </div>
          <MtTable2
            v-if="getDashboard && getDashboard.task_list"
            :columns="departmentTaskColumns"
            :rows="getDashboard.task_list.slice().splice(0, 5)"
          >
            <template v-slot:row="{ row }">
              <td
                @click="openDetailTaskPage(row)"
                class="cursor-pointer"
                v-for="(col, index) in departmentTaskColumns"
                :key="index"
              >
                <span v-if="col.field === 'datetime_request'">{{
                  formatDate(row.datetime_request) +
                  ' ' +
                  formatHoursMinutes(row.datetime_request)
                }}</span>
                <span v-else-if="col.field === 'id_employee_request'">{{
                  getEmployeeName(row.id_employee_request)
                }}</span>
                <span v-else-if="col.field === 'id_employee_staff'">{{
                  getEmployeeName(row.id_employee_staff)
                }}</span>
                <span v-else-if="col.field === 'title_task'">{{
                  row.title_task
                }}</span>
                <span v-else-if="col.field === 'status'">
                  <q-chip
                    dense
                    class="status-chip"
                    :class="row.flg_closed ? 'end' : 'not-finish'"
                    text-color="white"
                  >
                    {{ row.flg_completed ? '完了' : '未完了'}}
                  </q-chip>
                </span>
              </td>
            </template>
          </MtTable2>
        </div>
      </main>
    </div>
  </div>
</template>
<style lang="scss">
.container {
  &-aside {
    display: flex;
    flex-direction: column;
    padding: 24px 32px;
    gap: 20px;
    background-color: $white;
    height: 100%;

    .aside-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .department-pulldown {
        min-width: 120px;
      }
    }

    .noTaskImg {
      flex: 1 1 0%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .divider-dashboard {
      padding: 0 0 8px;
      border-bottom: 1px solid $accent-800;
      background-color: transparent;
    }

    .all-department {
      background-color: $accent-050;
      padding: 20px 32px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      @media screen and (max-width: 1200px) {
        padding: 16px 20px;
        gap: 16px;
      }

      @media screen and (max-width: 1024px) {
        padding: 14px 18px;
        gap: 14px;
      }

      @media screen and (max-width: 768px) {
        padding: 12px 16px;
        gap: 12px;
      }
    }

    .all-notifications {
      align-items: center;
      gap: 8px;

      @media screen and (max-width: 768px) {
        gap: 6px;
      }

      .notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        gap: 20px;

        @media screen and (max-width: 1024px) {
          gap: 16px;
        }

        @media screen and (max-width: 768px) {
          gap: 12px;
        }

        .notification-title {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 6px;
          margin: 0;

          @media screen and (max-width: 1200px) {
            font-size: 14px;
          }

          @media screen and (max-width: 1024px) {
            font-size: 13px;
            gap: 4px;
          }

          @media screen and (max-width: 768px) {
            font-size: 12px;
            gap: 3px;
          }

          .notification-icon {
            font-size: 32px;

            @media screen and (max-width: 1200px) {
              font-size: 24px;
            }

            @media screen and (max-width: 1024px) {
              font-size: 22px;
            }

            @media screen and (max-width: 768px) {
              font-size: 20px;
            }
          }
        }

        .notification-count {
          font-size: 40px;
          font-weight: 700;

          @media screen and (max-width: 1200px) {
            font-size: 32px;
          }

          @media screen and (max-width: 1024px) {
            font-size: 28px;
          }

          @media screen and (max-width: 768px) {
            font-size: 24px;
          }
        }
      }
    }

    .assignments {
      background-color: $white;
      padding: 20px 32px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      @media screen and (max-width: 1200px) {
        padding: 16px 20px;
        gap: 16px;
      }

      @media screen and (max-width: 1024px) {
        padding: 14px 18px;
        gap: 14px;
      }

      @media screen and (max-width: 768px) {
        padding: 12px 16px;
        gap: 12px;
      }
      

      .row {
        align-items: center;

        @media (max-width: 768px) {
          padding: 0 5px;
        }
      }

      .assignment-count {
        display: flex;
        align-items: center;
        gap: 5px;
        font-weight: 700;
        font-size: 19px;
        justify-content: end;

        @media (max-width: 1024px) {
          font-size: 17px;
          gap: 6px;
        }

        @media (max-width: 768px) {
          font-size: 15px;
          gap: 4px;
        }
      }
    }
  }

  &-main {
    display: flex;
    flex-direction: column;
    padding: 24px 20px;
    gap: 30px;
    background-color: $grey-100;
    height: calc(100vh - 60px);
    overflow-y: scroll;

    // Responsive design for smaller screens
    @media (max-width: 1024px) {
      padding: 20px 16px;
      gap: 24px;
    }

    @media (max-width: 768px) {
      padding: 16px 12px;
      gap: 20px;
    }

    .card {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 6px 16px;

      @media (max-width: 1024px) {
        gap: 12px;
        padding: 4px 12px;
      }

      @media (max-width: 768px) {
        gap: 10px;
        padding: 3px 10px;
      }

      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: stretch;
          text-align: center;
        }
      }

      &-main {
        background-color: $white;
        padding: 6px 16px;

        @media (max-width: 1024px) {
          padding: 4px 12px;
        }

        @media (max-width: 768px) {
          padding: 3px 10px;
        }
        display: flex;
        gap: 24px;

        .divider {
          min-height: 100px;
          max-height: 100px;
          height: 100%;
          background-color: $grey-300;
          width: 2px;
        }

        .time-table {
          display: flex;
          flex-direction: column;
          gap: 5px;

          &-header {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
.no-wrap {
  flex-wrap: nowrap!important;
}
.min-h-24px {
  min-height: 24px!important;
}
.status-chip {
  font-size: 12px;
  padding: 0px 12px;
  margin-left: -1px;
  &.end {
    background-color: #34c759;
  }

  &.not-finish {
    background-color: #EC9819;
  }

  :deep(.q-chip__content) {
    padding: 0 8px;
  }
}

// Employee Info Carousel Styles
.emp-info-carousel-container {
  display: flex;
  align-items: center;
  width: 100%;
  // Dynamic gap - only when arrows are present
  &:has(.carousel-arrow-container) {
    gap: 16px;
  }
}

.carousel-arrow-container {
  flex-shrink: 0;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  &.left {
    justify-content: flex-start;
  }

  &.right {
    justify-content: flex-end;
  }
}

.carousel-arrow {
  width: 24px;
  height: 24px;
  font-size: 24px;
  cursor: pointer;
  color: #0057FF;
}

.emp-info-cards-container {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  border-radius: 15px;
  flex: 1;

  // Hide scrollbar
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  @media (max-width: 768px) {
    gap: 12px;
    padding: 6px 0;
  }
}

.emp-info-card {
  flex: 0 0 auto;
  width:208px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  min-height: 278px;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  // Cards with thumbnails - thumbnail fills entire width with 16:9 ratio
  &.has-thumbnail {
    height: 278px; // Fixed height based on Figma

    .card-thumbnail-section {
      padding: 13px;
      height: 138px; // 16:9 ratio for 280px width (280 * 9/16 = 157.5)
      object-fit: cover;
    }

    .card-content {
      flex: 1;
      padding-top: 0px;
      display: flex;
      flex-direction: column;
      height: calc(278px - 157.5px); // Remaining height after thumbnail

      .card-memo {
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        max-height: 20px; // Approximately 3 lines
      }
    }
  }

  // Cards without thumbnails - more padding and height
  &:not(.has-thumbnail) {
    min-height: 220px;
    .card-content {
      padding: 12px;
      flex: 1;
      display: flex;
      flex-direction: column;

      .card-memo {
        margin-top: 8px;
        flex: 1;
        max-height: 160px;
      }

      .card-type-date {
        margin-top: auto;
      }
    }
  }

  @media (max-width: 1024px) {
    width: 260px;
  }

  @media (max-width: 768px) {
    width: 240px;
  }

  @media (max-width: 480px) {
    width: 220px;
  }
}

.card-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  .card-title {
    font-weight: 600;
    font-size: 15px;
    min-height: 18px;
    line-height: 1.3;
  }

  .card-memo {
    font-size: 15px;
    line-height: 1.5;
    color: #666;
    min-height: 44px;
  }

  .card-type-date {
    font-size: 12px;

    .caption1 {
      font-size: 11px;
    }
  }

  .card-employee-count {
    margin-top: 4px;

    .caption1 {
      font-size: 11px;
    }

    .q-avatar {
      width: 20px;
      height: 20px;
    }
  }
}
.font-15px {
  font-size: 15px;
}
.type-emp-info {
  font-size: 15px;
  color: #0057FF;
  font-weight: bold;
}
.mt-title-thumbnail-title {
  margin-top: 4px;
}
</style>
