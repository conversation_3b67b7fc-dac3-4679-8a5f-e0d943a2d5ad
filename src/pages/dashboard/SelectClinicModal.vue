<script setup lang="ts">
import { computed } from 'vue'
import useClinicStore from '@/stores/clinics'
import useAuthStore from '@/stores/auth'
import { storeToRefs } from 'pinia'
import { ClinicType } from '@/types/types'

const authStore = useAuthStore()
const clinicStore = useClinicStore()
const { getAuthUser } = storeToRefs(authStore)

const props = defineProps<{
  callBack?: () => void
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const onSelectClinic = (clinic: ClinicType) => {
  clinicStore.selectClinic(clinic.id_clinic)
  localStorage.setItem('selectedClinic', clinic.id_clinic.toString())
  localStorage.setItem('id_clinic', clinic.id_clinic.toString())
  if (props.callBack) {
    props.callBack()
  }
  return close()
}

const close = () => {
  emit('close')
}

const clinicOptions = computed(() => {
  const currentLoginUserClinics = clinicStore.getClinics.filter((c: ClinicType) => {
    return getAuthUser.value.id_clinic_list.includes(c.id_clinic)
  })
  return currentLoginUserClinics
    .map((c: ClinicType) => ({
      ...c,
      label: c.name_clinic_display,
      value: c.id_clinic
    }))
    .sort((a: ClinicType, b: ClinicType) => a.display_order - b.display_order)
})
</script>

<template>
  <div class="q-pa-lg">
    <div class="flex column gap-3">
      <h3 class="text-center">病院の選択</h3>
      <p class="text-center">複数病院の所属があります。<br />ログイン病院を選択してください。</p>
      <section class="row gap-4">
        <q-card
          v-for="clinic in clinicOptions"
          :key="clinic.id_clinic"
          flat
          bordered
          class="col-6 cursor-pointer clinic__box"
          @click="onSelectClinic(clinic)"
        >
          <q-card-section class="column q-pa-sm">
            <div>
              <small class="text-grey-400 q-mr-sm">病院CD</small>
              <small class="text-weight-bold">{{ clinic.code_clinic }}</small>
            </div>
            <div>
              <span class="text-weight-bold">
                {{ clinic.name_clinic_display }}
              </span>
              <small>
                {{ clinic.name_clinic_short ? ` / ${clinic.name_clinic_short} ` : '' }} 
              </small>
            </div>
            <div>
              <small class="text-grey-600">{{ clinic.address_prefecture }} {{ clinic.address_city }} </small>
            </div>
          </q-card-section>
        </q-card>
      </section>
    </div>
  </div>
</template>

<style scoped lang="scss">
.clinic__box {
  width: calc((100% / 2) - 8px);
  border-radius: 8px;
  background: rgb(234, 250, 246);
}
</style>
