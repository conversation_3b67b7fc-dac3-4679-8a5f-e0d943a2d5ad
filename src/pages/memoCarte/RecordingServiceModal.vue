<script setup lang="ts">
import {
  ref,
  onMounted,
  onBeforeUnmount,
  computed,
  reactive,
  nextTick,
  watch
} from 'vue'
import UpdateConversationSpeechModal from '@/pages/memoCarte/UpdateConversationSpeechModal.vue'
import useConversationStore from '@/stores/Conversation'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import { useRecording } from './useRecording'
import { event_bus } from '@/utils/eventBus'
import _ from 'lodash'

import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import ConfirmKoekaruQuestionTemplateModal from './ConfirmKoekaruQuestionTemplateModal.vue'
import ConfirmKoekaruRequestModal from './ConfirmKoekaruRequestModal.vue'
import RecordingLimitConfirmation from '@/pages/memoCarte/confirmation/RecordingLimitConfirmation.vue'
import MicrophoneIndicator from '@/components/MicrophoneIndicator.vue'

const MIN_HEIGHT = 100
const MIN_WIDTH = 100

const width = ref(500)
const height = ref(700)
const pos = ref({
  x: window.innerWidth - 510,
  y: window.innerHeight - 710
})

const isMoveDragging = ref(false)
const resizeDraggingSide = ref(null)
const resizeDraggingVertical = ref(null)
const dragStartX = ref(null)
const dragStartY = ref(null)
const startClientRect = ref(null)
const elModal = ref(null)
// prettier-ignore
const scrollArea = ref()

// Add WebSocket status overlay state
const showWsOverlay = ref(false)
const wsStatusMessage = ref('')

const {
  resumeRecording,
  pauseRecording,
  recordingTime,
  deleteConversation,
  recordingButtonIcon,
  toggleTranscribing,
  tempTranscript,
  tempFullTranscript,
  fullTranscript,
  createNotification,
  stopRecording,
  isPaused,
  selectedServiceType,
  isRecording,
  questionTemplatesList,
  michrophoneList,
  microphonesListDefault,
  recordConfig,
  isExternalMic,
  wsConnection,
  isWsConnected,
  simulateWebSocketDisconnect
} = useRecording()

const conversationStore = useConversationStore()
const {
  getFlgRecording,
  getSummaryGenerating,
  getSummaryGenerated,
  getSummaryError,
  getQuestionTemplate,
  getTranscriptChats
} = storeToRefs(conversationStore)

const emits = defineEmits(['close'])

const disableReq = ref(false)

const closeModal = () => {
  emits('close')
}

function onDrag(event) {
  if (
    dragStartX.value == null ||
    dragStartY.value == null ||
    startClientRect.value == null
  ) {
    return
  }

  if (isMoveDragging.value) {
    const currentWidth = isCompactView.value ? 185 : width.value
    const currentHeight = isCompactView.value ? 200 : height.value
    
    pos.value.x = _.clamp(
      startClientRect.value.x + (event.clientX - dragStartX.value),
      0,
      window.innerWidth - currentWidth
    )
    pos.value.y = _.clamp(
      startClientRect.value.y + (event.clientY - dragStartY.value),
      0,
      window.innerHeight - currentHeight
    )
    return
  }

  if (resizeDraggingVertical.value === 'top') {
    const changeValue = event.clientY - dragStartY.value
    const bottomY = startClientRect.value.y + startClientRect.value.height
    pos.value.y = _.clamp(
      startClientRect.value.y + changeValue,
      0,
      bottomY - MIN_HEIGHT
    )
    height.value = bottomY - pos.value.y
  }

  if (resizeDraggingVertical.value === 'bottom') {
    const changeValue = event.clientY - dragStartY.value
    height.value = _.clamp(
      startClientRect.value.height + changeValue,
      MIN_HEIGHT,
      window.innerHeight - startClientRect.value.y
    )
  }

  if (resizeDraggingSide.value === 'left') {
    const changeValue = event.clientX - dragStartX.value
    const rightX = startClientRect.value.x + startClientRect.value.width
    pos.value.x = _.clamp(
      startClientRect.value.x + changeValue,
      0,
      rightX - MIN_WIDTH
    )
    width.value = rightX - pos.value.x
  }

  if (resizeDraggingSide.value === 'right') {
    const changeValue = event.clientX - dragStartX.value
    width.value = _.clamp(
      startClientRect.value.width + changeValue,
      MIN_WIDTH,
      window.innerWidth - startClientRect.value.x
    )
  }
}

function getCoordinates(event) {
  if (event.touches) {
    return {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY
    }
  } else {
    return {
      x: event.clientX,
      y: event.clientY
    }
  }
}

function onTouchMove(event) {
  const { x, y } = getCoordinates(event)

  if (
    dragStartX.value == null ||
    dragStartY.value == null ||
    startClientRect.value == null
  ) {
    return
  }

  if (isMoveDragging.value) {
    const currentWidth = isCompactView.value ? 185 : width.value
    const currentHeight = isCompactView.value ? 200 : height.value
    
    pos.value.x = _.clamp(
      startClientRect.value.x + (x - dragStartX.value),
      0,
      window.innerWidth - currentWidth
    )
    pos.value.y = _.clamp(
      startClientRect.value.y + (y - dragStartY.value),
      0,
      window.innerHeight - currentHeight
    )
    return
  }

  if (resizeDraggingVertical.value === 'top') {
    const changeValue = y - dragStartY.value
    const bottomY = startClientRect.value.y + startClientRect.value.height
    pos.value.y = _.clamp(
      startClientRect.value.y + changeValue,
      0,
      bottomY - MIN_HEIGHT
    )
    height.value = bottomY - pos.value.y
  }

  if (resizeDraggingVertical.value === 'bottom') {
    const changeValue = y - dragStartY.value
    height.value = _.clamp(
      startClientRect.value.height + changeValue,
      MIN_HEIGHT,
      window.innerHeight - startClientRect.value.y
    )
  }

  if (resizeDraggingSide.value === 'left') {
    const changeValue = x - dragStartX.value
    const rightX = startClientRect.value.x + startClientRect.value.width
    pos.value.x = _.clamp(
      startClientRect.value.x + changeValue,
      0,
      rightX - MIN_WIDTH
    )
    width.value = rightX - pos.value.x
  }

  if (resizeDraggingSide.value === 'right') {
    const changeValue = x - dragStartX.value
    width.value = _.clamp(
      startClientRect.value.width + changeValue,
      MIN_WIDTH,
      window.innerWidth - startClientRect.value.x
    )
  }
}

function onDragEnd() {
  isMoveDragging.value = false
  resizeDraggingSide.value = null
  resizeDraggingVertical.value = null
  dragStartX.value = null
  dragStartY.value = null
  startClientRect.value = null
}

function onTouchEnd() {
  isMoveDragging.value = false
  resizeDraggingSide.value = null
  resizeDraggingVertical.value = null
  dragStartX.value = null
  dragStartY.value = null
  startClientRect.value = null
}

function onMoveDragStart(event) {
  if (event.target.closest('.no-drag')) {
    return
  }
  event.preventDefault()
  event.stopPropagation()

  elModal.value.focus()

  const { x, y } = getCoordinates(event)

  isMoveDragging.value = true
  dragStartX.value = x
  dragStartY.value = y
  startClientRect.value = {
    x: pos.value.x,
    y: pos.value.y,
    width: width.value,
    height: height.value
  }
}

function onTouchDragStart(event) {
  if (event.target.closest('.no-drag')) {
    return
  }
  event.preventDefault()
  event.stopPropagation()

  elModal.value.focus()

  const { x, y } = getCoordinates(event)

  isMoveDragging.value = true
  dragStartX.value = x
  dragStartY.value = y
  startClientRect.value = {
    x: pos.value.x,
    y: pos.value.y,
    width: width.value,
    height: height.value
  }
}

const deleteRecording = async () => {
  disableReq.value = true
  if (!isPaused.value) {
    await pauseRecording()
  }
  if (await deleteConversation()) closeModal()
  else disableReq.value = false
}

const selectedTab = ref('声カル作成')
const tabsData = [
  { id: '1', label: '声カル作成' },
  { id: '2', label: '設定' }
]




// const serviceType = ref(1)
const itemServiceType = [
  {
    label: '文字起こしのみ',
    value: 1
  },
  {
    label: '要約付き',
    value: 2
  }
]

const recordingState = ref('recording')

const zIndex = computed(() => {
  return conversationStore.getSource === 'create_memo_carte' ? 99 : 10
})

const startKoekaruRequest = async (isFromLimit = false) => {
  disableReq.value = true
  if (!isPaused.value) {
    await pauseRecording()
  }

  if (isFromLimit) {
    if (conversationStore.getSource === 'create_memo_carte') {
      if (document.querySelector('.q-dialog')) {
        return
      }

      const confirmLimit = await mtUtils.confirm(
        '利用制限に達しました。現在、このアプリケーションでは1回の録音につき最大25分までご利用いただけます。\n\nこれまでの内容をリクエストする場合は「リクエスト」ボタンを押してください。\n不要な場合は「閉じる」ボタンを押して、詳細画面の「削除」ボタンよりレコードを破棄してください。',
        '録音制限',
        'リクエスト',
        null,
        null,
        null,
        {
          show: false,
          callBackFun: Function
        },
        true,
        '閉じる',
        true
      )

      if (!confirmLimit) {
        disableReq.value = false
        isPaused.value = true
        return
      }
    } else {
      if (document.querySelector('.q-dialog')) {
        return
      }

      let popup = {
        isConfirmed: false,
        id_template: '1',
        apiOptions: 'speech'
      }

      await mtUtils.smallPopup(RecordingLimitConfirmation, { popup })

      if (!popup.isConfirmed) {
        disableReq.value = false
        isPaused.value = true
        return
      }

      const conversation_id = conversationStore.conversationId
      createNotification(conversation_id, true)
      await stopRecording(
        popup.id_template,
        selectedServiceType.value,
        conversation_id,
        popup.id_template
      )
      closeModal()
      return
    }
  }

  const conversation_id = conversationStore.conversationId
  let selectedQuestionTemplateId = {
    id: ''
  }
  const confirmationResult = {
    confirmed: false
  }

  if (selectedServiceType.value === 2) {
    if (conversationStore.getSource !== 'create_memo_carte') {
      // await mtUtils.smallPopup(
      //   ConfirmKoekaruQuestionTemplateModal,
      //   {
      //     questionTemplates: questionTemplatesList.value,
      //     selectedQuestionTemplateId,
      //     confirmationResult
      //   },
      //   {
      //     persistent: true
      //   }
      // )
      selectedQuestionTemplateId.id = selectedQuestionTemplate.value
      confirmationResult.confirmed = true

    } else {
      if (!isFromLimit) {
        await mtUtils.confirm(
          'この内容でSOAPを生成しますか？',
          '確認',
          '生成',
          null,
          null,
          null,
          {
            show: false,
            callBackFun: Function
          },
          true
        ).then(async (confirmation) => {
          if (confirmation) {
            selectedQuestionTemplateId.id = '3'
            confirmationResult.confirmed = true
          }
        })
      } else {
        selectedQuestionTemplateId.id = '3'
        confirmationResult.confirmed = true
      }
    }
    if (!confirmationResult.confirmed) {
      disableReq.value = false
      resumeRecording()
      return
    }
  } else {
    await mtUtils.smallPopup(ConfirmKoekaruRequestModal, { confirmationResult })
    if (!confirmationResult.confirmed) {
      disableReq.value = false
      resumeRecording()
      return
    }
  }

  event_bus.emit('close-draggable-recording-modal')
  createNotification(conversation_id, true)
  await stopRecording(
    null,
    selectedServiceType.value,
    conversation_id,
    selectedQuestionTemplateId.id
  )
  closeModal()
}

event_bus.on('recording-start', () => {
  console.log("recording-start")
  toggleTranscribing()
})

event_bus.on('close-draggable-recording-modal', () => {
  closeModal()
})

const scrollToBottom = () => {
  if (scrollArea.value) {
    nextTick(() => {
      const scrollHeight = scrollArea.value.getScroll().verticalSize
      scrollArea.value.setScrollPosition('vertical', scrollHeight)
    })
  }
}

const observer = ref()

const realTimeScript = computed(() => {
  const splitter = tempFullTranscript.value.split('。')
  const joiner = splitter.join('<br />')

  return joiner.length > 0
    ? `${joiner} <br /> ${tempTranscript.value}`
    : `${tempTranscript.value}`
})

// Add watcher for realTimeScript
watch(realTimeScript, () => {
  scrollToBottom()
})

async function listAudioInputs() {
  try {
    // Always fetch the devices and update the microphone list
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioDevices  = devices.filter((device) => device.kind === 'audioinput')
    
    const formattedMics = audioDevices.map(mic => {
      let label = mic.label || `Microphone ${mic.deviceId.slice(0, 5)}...`
      
      if (isExternalMic(label)) {
        label = mic.deviceId === 'default' 
          ? '外部接続マイク (デフォルト)' 
          : '外部接続マイク'
      } else {
        if (mic.deviceId === 'default') {
          label = `${label} (デフォルト)`
        }
      }

      return {
        label: label,
        value: mic.deviceId
      }
    }).sort((a, b) => {
      if (a.value === 'default') return -1
      if (b.value === 'default') return 1
      return 0
    })
    michrophoneList.value = [...formattedMics]
    microphonesListDefault.value = [...formattedMics];

    // Check if a microphone is selected in local storage
    const selectedMicFromStorage = localStorage.getItem('selectedMicrophone');
    const matchingMic = michrophoneList.value.find(
      (mic) => mic.value === selectedMicFromStorage
    );

    if (matchingMic) {
      // Use the microphone from local storage if it matches the list
      await nextTick();
      recordConfig.value.microphone = matchingMic.value;
    } else {
      // Select the first microphone from the list if no match is found
      await nextTick();
      recordConfig.value.microphone = michrophoneList.value[0]?.value || null;
    }

    // Save the selected microphone to local storage
    if (recordConfig.value.microphone) {
      localStorage.setItem('selectedMicrophone', recordConfig.value.microphone);
    }
  } catch (error) {
    const errorMic = {
      label: 'マイクが見つかりません',
      value: ''
    }
    microphonesListDefault.value = [errorMic]
    michrophoneList.value = [errorMic]
  }
}

onMounted(async () => {
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', onDragEnd)
  document.addEventListener('touchmove', onTouchMove)
  document.addEventListener('touchend', onTouchEnd)

  navigator.mediaDevices.ondevicechange = async () => {
    console.log('Device change detected. Updating microphone list...');
    await listAudioInputs();
  };

  // if (conversationStore.getSource === 'create_memo_carte') {
  //   selectedServiceType.value = 2
  // }

  console.log('Mounting voice command listener')

  event_bus.on('voice-command-stop', async (data) => {
    if (isRecording.value && !isPaused.value) {
      await handleQuickSubmit()
    }
  })

  event_bus.on('recording-limit-reached', async () => {
    await startKoekaruRequest(true)
  })

  const el = scrollArea.value?.$el

  if (el) {
    const stopPropagation = (e) => e.stopPropagation();

    el.addEventListener('touchstart', stopPropagation, { passive: true });
    el.addEventListener('touchmove', stopPropagation, { passive: true });

    // Optional: for pointer devices
    el.addEventListener('pointerdown', stopPropagation);
  }
})

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', onDragEnd)
  document.removeEventListener('touchmove', onTouchMove)
  document.removeEventListener('touchend', onTouchEnd)
  navigator.mediaDevices.ondevicechange = null;
  event_bus.off('recording-start')
  event_bus.off('close-draggable-recording-modal')
  event_bus.off('voice-command-stop')
  event_bus.off('ws-disconnected')
  event_bus.off('ws-reconnecting')
  event_bus.off('ws-reconnected')
  event_bus.off('ws-reconnect-failed')
  if (observer.value) {
    observer.value.disconnect()
  }
  event_bus.off('recording-limit-reached')
})

const handleQuickSubmit = async () => {
  disableReq.value = true
  if (!isPaused.value) {
    await pauseRecording()
  }

  const conversation_id = conversationStore.conversationId
  let selectedQuestionTemplateId = {
    id: ''
  }

  if (conversationStore.getSource === 'create_memo_carte') {
    selectedQuestionTemplateId.id = '3'
    selectedServiceType.value = 2
  } else {
    selectedQuestionTemplateId.id = selectedQuestionTemplate.value || 
                                   localStorage.getItem('id_question') || 
                                   questionTemplatesList.value[0]?.value
  }

  const confirmationResult = {
    confirmed: true
  }

  event_bus.emit('close-draggable-recording-modal')
  createNotification(conversation_id, true)

  await stopRecording(
    null,
    selectedServiceType.value,
    conversation_id,
    selectedQuestionTemplateId.id
  )
  closeModal()
}
const selectedQuestionTemplate = ref(recordConfig.value.id_question)

const mapQuestionTemplates = questionTemplatesList.value.map((questionTemplate) => {
  return {
    label: questionTemplate.name_template,
    value: questionTemplate.question_id
  }
})
const showMicSelect = ref(false)

const hasExternalMics = computed(() => {
  const result = michrophoneList.value.some(mic => isExternalMic(mic.label))
  return result
})

const handleMicClick = () => {
  if (hasExternalMics.value) {
    showMicSelect.value = true
  }
}

const isCompactView = ref(false)
const previousPos = ref(null) 

const toggleView = () => {
  
  isCompactView.value = !isCompactView.value
  
  if (isCompactView.value) {
    previousPos.value = { ...pos.value }
    pos.value = {
      x: window.innerWidth - 185, 
      y: window.innerHeight - 200 
    }
  } else {
    pos.value = previousPos.value
  }
  
  nextTick(async () => {
    if (isRecording.value && !isPaused.value) {
      const currentDeviceId = recordConfig.microphone
      
      if (currentDeviceId) {
        const micComponent = michrophoneList.value.find(mic => mic.value === currentDeviceId)
        if (micComponent) {
          recordConfig.microphone = null
          await nextTick()
          recordConfig.microphone = currentDeviceId
        }
      }
    }
  })
}

const switchMic = (micVal:string)=> {
  console.log("selected mic", micVal);
  
  recordConfig.value.microphone = micVal
  localStorage.setItem('selectedMicrophone', recordConfig.value.microphone);
}

// Add WebSocket status event handlers
event_bus.on('ws-disconnected', (message) => {
  showWsOverlay.value = true
  wsStatusMessage.value = `現在、インターネット接続が不安定なため、「声カル」の動作が一時的に不安定になる場合があります。
通信状況によっては音声の送信ができないことがありますので、安定したネットワーク環境でのご利用をお願いいたします。`
})

event_bus.on('ws-reconnecting', () => {
  wsStatusMessage.value = `現在、インターネット接続が不安定なため、「声カル」の動作が一時的に不安定になる場合があります。
通信状況によっては音声の送信ができないことがありますので、安定したネットワーク環境でのご利用をお願いいたします。`
})

event_bus.on('ws-reconnected', () => {
  showWsOverlay.value = false
  wsStatusMessage.value = ''
})

event_bus.on('ws-reconnect-failed', () => {
  wsStatusMessage.value = 'Failed to reconnect'
  // Hide overlay after 3 seconds
  setTimeout(() => {
    showWsOverlay.value = false
    wsStatusMessage.value = ''
  }, 3000)
})
</script>

<template>
  <div class="adjustable-modal" ref="elModal" v-show="getFlgRecording" :style="{
    transform: `translate3d(${pos.x}px, ${pos.y}px, 0)`,
    zIndex: zIndex,
    width: isCompactView ? '185px' : '500px',
    height: isCompactView ? '200px' : '700px'
  }" tabindex="0" :class="getSummaryGenerated ? 'summary-generated' : ''">

    <!-- Replace q-inner-loading with CSS overlay -->
    <div v-if="showWsOverlay" class="ws-overlay">
      <div class="ws-status-card">
        <div class="text-center">
          <div class="text-white text-body2 q-mb-md">{{ wsStatusMessage }}</div>
          <q-spinner
            color="white"
            size="2em"
          />
        </div>
      </div>
    </div>

    <div class="adjustable-modal__header relative-position col flex column" 
         @mousedown="onMoveDragStart"
         @touchstart="onTouchDragStart">
      
      <!-- Compact View -->
      <template v-if="isCompactView">
        <div class="compact-view-container">
          <div class="header">
            <q-btn
              flat
              round
              dense
              icon="fullscreen"
              @click="toggleView"
              class="absolute-top-right q-ma-xs no-drag"
            />
            <div class="header__title body1 text-center q-mt-lg">{{ isPaused ? '一時停止中' : '録音中' }}</div>
          </div>
          <div>
            <div class="text-center recording-time">{{ recordingTime }}</div>
            <div class="flex justify-center q-mt-lg">
              <q-btn
                :icon="recordingButtonIcon"
                class="bg-danger q-px-xl text-white no-drag"
                @click.stop="toggleTranscribing"
              />
            </div>
            <div class="text-center q-mt-sm">
              <div class="cursor-pointer no-drag" @click.stop="deleteRecording">
                録音を中止
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- Normal View -->
      <template v-else>
        <div class="no-drag flex items-center">
          <q-tabs v-model="selectedTab" align="center" class="col" dense indicator-color="transparent">
            <q-tab v-for="tab in tabsData" :key="tab.id" :name="tab.label" :label="tab.label" />
          </q-tabs>
          <q-btn
            flat
            round
            dense
            icon="fullscreen_exit"
            @click="toggleView"
            class="q-mr-sm"
          />
        </div>
        <q-tab-panels v-model="selectedTab" animated class="col" style="background-color: #f1f5f9">
          <q-tab-panel name="声カル作成" class="q-px-none q-pb-none flex column">
            <div class="header">
              <div class="flex items-center">
                <div class="title col">
                  {{ isPaused ? '一時停止中...' : '録音中...' }}
                </div>
                <div class="time q-pr-md">{{ recordingTime }}</div>
              </div>
            </div>

            <q-scroll-area class="col q-pa-sm" style="font-size: 13px" ref="scrollArea">
              <div class="" v-html="realTimeScript"></div>
            </q-scroll-area>
          </q-tab-panel>
          <q-tab-panel name="設定" class="q-pa-none">
            <div class="col flex column">
              <div class="col">
                <div v-if="conversationStore.getSource !== 'create_memo_carte'" class="q-mt-xl q-mb-md"
                  style="max-width: 250px; margin-left: auto; margin-right: auto">
                  <MtInputForm type="radio" class="q-mt-none" v-model="selectedServiceType" :items="itemServiceType"
                    required />
                </div>

                <div class="q-px-md">
                  <MtFilterSelect v-model:options="michrophoneList" label="録音デバイス"
                    v-model:selecting="recordConfig.microphone" :options-default="microphonesListDefault"
                    @update:selecting="switchMic"
                    />
                </div>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>

        <div class="">
          <div class="btn-container-question q-py-none no-drag">
            <div class="relative-position" style="width:100%">
              <MicrophoneIndicator 
                :label="michrophoneList.find(mic => mic.value === recordConfig.microphone)?.label || '選択されていません'"
                :is-active="isRecording && !isPaused"
                :device-id="recordConfig.microphone"
                @click="hasExternalMics ? handleMicClick : null"
                :class="{ 'cursor-pointer': hasExternalMics }"
              />
              
              <q-menu
                v-if="hasExternalMics"
                :mode="showMicSelect"
                anchor="bottom middle"
                self="top middle"
                fit
              >
                <q-list class="full-width">
                  <q-item
                    v-for="mic in michrophoneList"
                    :key="mic.value"
                    clickable
                    v-close-popup
                    @click="switchMic(mic.value)"
                  >
                    <q-item-section>
                      {{ mic.label }}
                    </q-item-section>
                    <q-item-section side v-if="recordConfig.microphone === mic.value">
                      <q-icon name="check" color="primary"/>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </div>
          </div>
          <div class="btn-container-question q-py-none no-drag"
            v-if="conversationStore.getSource !== 'create_memo_carte'">
            <MtInputForm type="radio" class="q-mt-none" v-model="selectedQuestionTemplate" :items="mapQuestionTemplates"
              required />
          </div>
          <div class="btn-container q-py-none no-drag">
            <q-btn :label="isPaused ? '再開' : '一時停止'" dense class="bg-white q-mr-sm q-py-xs" outline
              @click.stop="toggleTranscribing" :disable="disableReq" />
            <q-btn flat dense class="text-white bg-primary text-center q-py-xs" label="生成"
              @click.stop="startKoekaruRequest(false)" :disable="disableReq" />
          </div>
          <div class="flex items-center justify-center q-mt-xs no-drag">
            <q-btn flat dense class="text-center q-px-lg" label="閉じる" @click.stop="deleteRecording"
              :disable="disableReq" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
$z-index-modal: 10;

* {
  box-sizing: border-box;
}

.adjustable-modal {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  background-color: #eeeeee;
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);
  width: 500px;
  height: 700px;
  border: 4px solid #009639;
  touch-action: none; /* Prevent all touch actions on the modal container */

  &.summary-generated {
    border-color: #34c759;
  }

  &:focus,
  &:focus-within {
    outline: 0;
  }

  // set q-tab
  .q-tab.q-tab__label {
    font-size: 12px;
  }

  .q-tabs--dense .q-tab {
    min-height: 30px;
  }

  .q-tab.relative-position.self-stretch.flex.flex-center.text-center.q-tab--active.q-focusable.q-hoverable.cursor-pointer {
    background-color: #f1f5f9;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .q-tab.relative-position.self-stretch.flex.flex-center.text-center.q-tab--inactive.q-focusable.q-hoverable.cursor-pointer {
    background-color: #eeeeee;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .btn-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    justify-content: center;
    background-color: #f1f5f9;
    padding-bottom: 8px;
    padding-left: 8px;
    padding-right: 8px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .btn-container-question {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f1f5f9;
    padding-bottom: 8px;
    padding-left: 8px;
    padding-right: 8px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.header {
  cursor: grab;
  touch-action: none; /* Prevent touch actions on the header for dragging */

  &:active {
    cursor: grabbing;
  }

  font-size: 14px;

  .title {
    text-align: center;
    font-weight: bold;
    color: var(--Status-Danger, #be0123);
  }
}

/* Add styles for scrollable content */
.q-scroll-area {
  touch-action: pan-y; /* Allow vertical scrolling only */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Add no-drag class for elements that shouldn't trigger dragging */
.no-drag {
  touch-action: auto;
  cursor: default;
}

.recording-time {
  color: var(--System-Gray-900, #212121);
  font-size: 24px;
  font-weight: bold;
}

.cursor-pointer {
  cursor: pointer;
}

.debug-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  font-size: 12px;
  padding: 4px 8px;
  opacity: 0.8;
  
  &:hover {
    opacity: 1;
  }
}

.ws-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 8px;
}

.ws-status-card {
  background: rgba(0, 0, 0, 0.8) !important;
  min-width: 250px;
  min-height: 200px;
  border-radius: 8px;
  padding: 32px;
  margin: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>

