<script setup lang="ts">
import { ref, onMounted } from 'vue'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import useConversationStore from '@/stores/Conversation'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import mtUtils from '@/utils/mtUtils'
import { useRecording } from './useRecording'
import useClinicStore from '@/stores/clinics'
import aahMessages from '@/utils/aahMessages'

const clinicStore = useClinicStore()


const props = withDefaults(
  defineProps<{
    id_employee: string
    popup: any,
    showQuestionTemplate: boolean
  }>(),
  {
    id_employee: '',
    showQuestionTemplate: true,
  }
)
const defaultEmployee = JSON.parse(
  localStorage.getItem('id_employee') as string
)

const emits = defineEmits(['close'])

const conversationStore = useConversationStore()

const { recordConfig, michrophoneList, microphonesListDefault, questionTemplatesList, questionTemplatesListDefault, selectedServiceType } = useRecording()

const itemServiceType = [
  {
    label: '文字起こしのみ',
    value: 1
  },
  {
    label: '要約付き',
    value: 2
  }
]

const currentClinic = ref()
const id_question = ref()
const selectedMicrophone = ref()

const closeModal = () => {
  emits('close')
}

const selectDefaultEmployee = () => {
  recordConfig.value.id_employee = defaultEmployee
}

const saveConfig = async () => {
  if (!recordConfig.value.microphone) {
    await mtUtils.alert('マイクが未選択です')
    return
  }
  if (!recordConfig.value.id_question) {
    await mtUtils.alert('要約フォーマットを指定してください')
    return
  }

  try {
    // Update clinic AI settings if changed
    if (recordConfig.value.flg_auto_memocarte_ai !== currentClinic.value?.flg_auto_memocarte_ai) {
      currentClinic.value.flg_auto_memocarte_ai = recordConfig.value.flg_auto_memocarte_ai
      await clinicStore.updateClinic(localStorage.getItem('id_clinic'), {
        flg_auto_memocarte_ai: currentClinic.value.flg_auto_memocarte_ai,
      })
      await clinicStore.fetchClinics()
    }

    // Save recording configuration
    recordConfig.value.id_question = id_question.value
    recordConfig.value.microphone = selectedMicrophone.value
    localStorage.setItem('id_question', id_question.value)
    localStorage.setItem('selectedMicrophone', recordConfig.value.microphone)

    await mtUtils.autoCloseAlert(aahMessages.success)
    closeModal()
  } catch (error) {
    await mtUtils.alert(aahMessages.failed)
  }
}


onMounted(() => {

  recordConfig.value.id_employee = props.id_employee
  recordConfig.value.datetime_memo_carte = new Date().toISOString().slice(0, 16)
  const currentQuestion = localStorage.getItem('id_question') || recordConfig.value.id_question || questionTemplatesList.value[0]?.value
  id_question.value = currentQuestion

  selectedMicrophone.value = recordConfig.value.microphone
  if (localStorage.getItem('id_clinic')) {
    currentClinic.value = clinicStore.getClinics.find(
      (clinic) => clinic.id_clinic == localStorage.getItem('id_clinic')
    )
    if (currentClinic.value) {
      recordConfig.value.flg_auto_memocarte_ai = currentClinic.value.flg_auto_memocarte_ai
    }
  }

})
</script>

<template>
  <q-form>
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        基本設定
      </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="content q-px-xl">
      <div class="q-my-md">
        <MtInputForm type="radio" class="q-mt-none" v-model="selectedServiceType" :items="itemServiceType" required />
      </div>
      <div class="q-my-md">
        <MtFilterSelect v-model:options="michrophoneList" label="録音デバイス" v-model:selecting="selectedMicrophone"
          :options-default="microphonesListDefault" />
      </div>
      <!-- <div class="q-my-md">
        <MtFormInputDate v-model:date="recordConfig.datetime_memo_carte" label="メモカルテ記録日時" />
      </div> -->
      <div class="q-my-md" v-if="showQuestionTemplate">
        <MtFilterSelect v-model:options="questionTemplatesList" label="カルテ型"
          v-model:selecting="id_question" :options-default="questionTemplatesListDefault" />
      </div>
      <div class="q-my-md">
        <MtFormCheckBox v-model:checked="recordConfig.flg_auto_memocarte_ai" label="AIカルテ生成後にカルテへ自動反映" class=""
          @update:checked="checkedFlg" />
      </div>

    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" class="q-ml-md" @click="saveConfig()">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style></style>
