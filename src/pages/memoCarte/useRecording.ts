import { computed, ref, nextTick } from 'vue'
import mtUtils from '@/utils/mtUtils'
import DeleteRecording from '@/pages/memoCarte/confirmation/DeleteRecording.vue'
import { debounce } from 'lodash'
import useConversationStore, { TaskStatus } from '@/stores/Conversation'
import useMemoCarteStore from '@/stores/memo-cartes'
import aahMessages from '@/utils/aahMessages'
import { event_bus } from '@/utils/eventBus'
import { clearIntervalAsync, SetIntervalAsyncTimer } from 'set-interval-async'
import koekaruApi, { secretKey, getInstance } from '@/boot/axiosKoekaru'
import router from '@/router'
import { formatDateWithTime, getDateTimeNow } from '@/utils/aahUtils'
import useCliCommonStore from '@/stores/cli-common'
import RecordingLimitConfirmation from '@/pages/memoCarte/confirmation/RecordingLimitConfirmation.vue'
import usePetBioStore from '@/stores/pet-bios'
import useClinicStore from '@/stores/clinics'
import { useKoekaruModalStore } from '@/stores/koekaru-modal'

let conversationStore: any, memoCarteStore: any, koekaruModalStore: any

declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}

let recognition: any
if (window.SpeechRecognition || window.webkitSpeechRecognition) {
  const Recognition = window.SpeechRecognition || window.webkitSpeechRecognition
  recognition = new Recognition()
  recognition.continuous = true
  recognition.lang = 'ja-JP'
  recognition.interimResults = true
} else {
  recognition = null
  console.warn('SpeechRecognition is not supported in this browser.')
}
const isRecording = ref(false)
const isPaused = ref(false)
const seconds = ref(0)
const fullAudioSource = ref('')
const flgMemoCarteSubmitted = ref(false)
const flgVerified = ref('0')
const flg_auto_memocarte_ai = ref(false)
let timer: any = null,
  stream: MediaStream
const sampleRate = 16000

// arrays to store audio chunks and recorders
let audioChunks: any = [],
  sessionAudioChunks: any = []
let mediaRecorder: any, sessionMediaRecorder: MediaRecorder

const tempTranscript = ref('')
const fullTranscript = ref('')
const tempFullTranscript = ref('')
const currentTranscript = ref('')
const showNotification = ref(false)
const selectedServiceType = ref(2)
const recordConfig = ref({
  id_employee: '',
  datetime_memo_carte: '',
  id_question: '',
  microphone: '',
  flg_auto_memocarte_ai: false
})
const michrophoneList = ref([])
const microphonesListDefault = ref([])
const questionTemplatesList = ref([])
const questionTemplatesListDefault = ref([])

type ChangeDataType = {
  old_word: string
  new_word: string
}

interface FormattedDataType {
  start_time: string
  end_time: string
  corrected_sentence: string
  old_sentence: string
  corrected_words: ChangeDataType[]
}

const feedbackData = ref<FormattedDataType[]>([])
const recordingLimit = ref(25 * 60)
//const recordingLimit = ref(20)

interface TranscriptData {
  text: string
  start_time: string
  end_time: string
}

const transcriptWithTimestamp = ref<TranscriptData[]>([])

const setTranscriptData = (transcriptResponse: any) => {
  if (transcriptResponse?.transcript) {
    transcriptWithTimestamp.value = transcriptResponse.transcript
  }
}

const updateFeedbackData = async (
  conversationId: string,
  oldSentence: string,
  correctedSentence: string,
  oldWord: string,
  newWord: string
) => {
  try {
    const feedbackItem: FormattedDataType = {
      start_time: '00:00',
      end_time: '00:00',
      corrected_sentence: correctedSentence,
      old_sentence: oldSentence,
      corrected_words: [
        {
          old_word: oldWord,
          new_word: newWord
        }
      ]
    }

    const payload = {
      feedback: [feedbackItem],
      id_conversation: conversationId
    }

    const response = await koekaruApi.post(`/submit-feedback`, payload)
    feedbackData.value.push(feedbackItem)
    return response.data
  } catch (error: any) {
    console.error('Error submitting feedback:', error)
    throw error
  }
}

const recordingTime = computed(() => {
  const date = new Date(seconds.value * 1000)
  const m = String(date.getUTCMinutes()).padStart(2, '0')
  const s = String(date.getUTCSeconds()).padStart(2, '0')
  return `${m}:${s}`
})

const recordingButtonIcon = computed(() => {
  if (isPaused.value) {
    return 'play_arrow'
  }
  return 'pause'
})

const toggleTranscribing = async () => {
  if (!isRecording.value) {
    isPaused.value = false
    startRecording()
  } else if (isPaused.value) {
    if (seconds.value >= recordingLimit.value) {
      await mtUtils.alert(
        '録音の制限に達しました。現在、1録音につき20分までの利用可能です。'
      )
      return
    }
    resumeRecording()
  } else {
    pauseRecording()
  }
}

const checkRecordingLimit = () => {
  if (seconds.value >= recordingLimit.value) {
    if (!isPaused.value) {
      pauseRecording()
    }
    event_bus.emit('recording-limit-reached')
    return true
  }
  return false
}

let voiceCommandStartTime: number | null = null
const VOICE_COMMAND_BUFFER_BEFORE = 1500
const VOICE_COMMAND_BUFFER_AFTER = 1000

const isVoiceCommand = ref(false)
const FEEDBACK_SOUND_URL =
  'https://test-bucket-jp-123123124124.s3.ap-northeast-1.amazonaws.com/japan-feedback-voice_onk0lq.mp3'

let audioFeedback: HTMLAudioElement | null = null

if (typeof window !== 'undefined') {
  audioFeedback = new Audio(FEEDBACK_SOUND_URL)
  audioFeedback.preload = 'auto'
}

const playAudioFeedback = async () => {
  try {
    if (audioFeedback) {
      audioFeedback.currentTime = 0
      await audioFeedback.play()
    }
  } catch (error) {
    console.error('Error playing audio feedback:', error)
  }
}

// Add WebSocket related variables
interface ExtendedWebSocket extends WebSocket {
  wasError?: boolean;
}

const wsConnection = ref<ExtendedWebSocket | null>(null)
const isWsConnected = ref(false)
const wsUrl = ref('')
const reconnectAttempts = ref(0)
const MAX_RECONNECT_ATTEMPTS = 3
const RECONNECT_INTERVAL = 5000 // 5 seconds

export const connectWebSocket = async (conversationId: string, petId?: string, employeeId?: string) => {
  try {
    if (!secretKey) await getInstance(useClinicStore().getClinic?.code_clinic)

    if (!conversationStore) conversationStore = useConversationStore()
    
    // Get WebSocket URL from Koekaru API with conversation parameters
    const params = new URLSearchParams({
      id_conversation: conversationId,
    })

    if (conversationStore.requestId) {
      params.append('id_vetty_request', conversationStore.requestId)
    }

    if (petId) {
      params.append('id_pet_info', petId)
      conversationStore.setCurrentMemoCarteData({
        ...conversationStore.getCurrentMemoCarteData,
        id_pet: petId
      })
    } else {
      params.append('id_pet_info', conversationStore.getCurrentMemoCarteData?.id_pet)
    }

    if (employeeId) {
      params.append('id_employee_info', employeeId)
    }

    const response = await koekaruApi.get(`/get-upload-url?${params}`)
    const uploadUrl = response.data.upload_url
    
    // Construct WebSocket URL with secret key and clinic code
    const separator = uploadUrl.includes('?') ? '&' : '?'
    const clinicCode = useClinicStore().getClinic?.code_clinic
    wsUrl.value = `${uploadUrl}${separator}secret-key=${secretKey}&code-clinic=${clinicCode}`
    
    wsConnection.value = new WebSocket(wsUrl.value) as ExtendedWebSocket
    wsConnection.value.wasError = false
    
    wsConnection.value.onopen = () => {
      console.log('WebSocket connection established for conversation:', conversationId)
      isWsConnected.value = true
      reconnectAttempts.value = 0 // Reset reconnect attempts on successful connection
      return true
    }
    
    wsConnection.value.onclose = async () => {
      console.log('WebSocket connection closed for conversation:', conversationId)
      isWsConnected.value = false
      
      // Check if this was a manual pause (user clicked pause button)
      const wasManualPause = isPaused.value && !wsConnection.value?.wasError
      
      if (!wasManualPause) {
        console.log("error")
        // Emit disconnection event
        event_bus.emit('ws-disconnected', 'WebSocket接続が切断されました。再接続を試みています...')
        
        // Attempt to reconnect if we haven't exceeded max attempts
        if (reconnectAttempts.value < MAX_RECONNECT_ATTEMPTS) {
          reconnectAttempts.value++
          console.log(`Attempting to reconnect (${reconnectAttempts.value}/${MAX_RECONNECT_ATTEMPTS})...`)
          
          // Emit reconnecting event
          event_bus.emit('ws-reconnecting', reconnectAttempts.value)
          
          // Pause recording if it's active
          if (isRecording.value && !isPaused.value) {
            await pauseRecording()
          }
          
          // Wait for the specified interval before attempting to reconnect
          setTimeout(async () => {
            const reconnected = await connectWebSocket(conversationId, petId, employeeId)
            if (reconnected) {
              // Emit reconnected event
              event_bus.emit('ws-reconnected')
              // Resume recording if it was paused due to disconnection
              await resumeRecording()
            } else {
              // If reconnection failed, keep recording paused
              console.log('Failed to reconnect WebSocket, keeping recording paused')
              event_bus.emit('ws-reconnect-failed')
            }
          }, RECONNECT_INTERVAL)
        } else {
          console.log('Max reconnection attempts reached')
          // Emit reconnect failed event
          event_bus.emit('ws-reconnect-failed')
        }
      } else {
        console.log('Recording was manually paused by user, skipping reconnection attempt')
      }
      
      return false
    }
    
    wsConnection.value.onerror = (error) => {
      console.error('WebSocket error for conversation:', conversationId, error)
      isWsConnected.value = false
      // Mark that this was an error disconnection
      if (wsConnection.value) {
        wsConnection.value.wasError = true
      }
      return false
    }
    
    return true
  } catch (error) {
    console.error('Failed to establish WebSocket connection for conversation:', conversationId, error)
    return false
  }
}

const initializeMediaRecorder = (stream: MediaStream) => {
  // Clean up existing MediaRecorder if it exists
  if (sessionMediaRecorder?.state !== 'inactive') {
    sessionMediaRecorder?.stop()
  }

  // Create new MediaRecorder with explicit mimeType
  sessionMediaRecorder = new MediaRecorder(stream, {
    mimeType: 'audio/webm;codecs=opus'
  })

  // Start with 500ms timeslice to get regular data chunks
  sessionMediaRecorder.start(500)

  sessionMediaRecorder.onerror = (event: any) => {
    console.error('MediaRecorder error:', event)
  }
  
  // Send audio data through WebSocket
  sessionMediaRecorder.ondataavailable = (event: any) => {
    console.log("On data available")
    if (wsConnection.value !== null && wsConnection.value?.readyState === WebSocket.OPEN && event.data.size > 0) {
      try {
        wsConnection.value.send(event.data)
        console.log(`🎤 bytes ${event.data.size}`)
      } catch (error) {
        console.error('Failed to send audio data:', error)
      }
    } else {
      console.log('WebSocket connection is not open or ready')
    }
  }

  return sessionMediaRecorder
}

const startRecording = async () => {
  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    seconds.value++
    checkRecordingLimit()
  }, 1000)

  if (wsConnection.value === null) {
    mtUtils.autoCloseAlert('Not connected to WebSocket')
    return
  }

  stream = await navigator.mediaDevices.getUserMedia({
    audio: {
      deviceId: conversationStore.getCurrentMic,
      sampleRate: sampleRate,
      sampleSize: 16,
      channelCount: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    },
    video: false
  })

  recognition.start()
  isRecording.value = true

  if (recognition) {
    // Initialize MediaRecorder with the stream
    initializeMediaRecorder(stream)

    recognition.onspeechstart = () => {
      if (isPaused.value) return
    }

    recognition.onresult = (event: any) => {
      let finalTranscript = '',
        intermTranscript = ''

      for (let i = event.resultIndex; i < event.results.length; ++i) {
        const transcript = event.results[i][0].transcript.trim().toLowerCase()

        const numberPattern =
          /(3|三|さん|san|three|３).*(2|二|に|ni|two|２).*(1|一|いち|ichi|one|１).*(?:終了|しゅうりょう)/i

        if (numberPattern.test(transcript)) {
          isVoiceCommand.value = true
          voiceCommandStartTime = Date.now()

          try {
            if (audioFeedback) {
              audioFeedback.play().catch(console.error)
            }

            mtUtils.autoCloseAlert('要約リクエストを送信しました！')
            event_bus.emit('voice-command-stop')
            return
          } catch (error) {
            console.error('Error in voice command handling:', error)
          }
        }

        if (event.results[i].isFinal) {
          var speech = event.results[i][0].transcript.trim()
          finalTranscript += speech
          
          // Send transcription through WebSocket only when final
          if (wsConnection.value !== null) {
            wsConnection.value.send(speech)
            console.log(`📝 sent text: ${speech.slice(0, 30)}…`)
          }
        } else {
          intermTranscript += event.results[i][0].transcript
        }
      }
      tempTranscript.value = intermTranscript
      if (intermTranscript !== '') fullTranscript.value = intermTranscript

      if (finalTranscript) {
        tempTranscript.value = ''
        if (!conversationStore.onePersonSummarry) {
          conversationStore.addTranscriptChat({
            speech: finalTranscript,
            speech_audio_url: ''
          })
        }
        tempFullTranscript.value += tempFullTranscript.value
          ? `。${fullTranscript.value}`
          : fullTranscript.value

        event_bus.emit(
          'speech_created',
          conversationStore.getTranscriptChats.length - 1
        )
      }
    }

    recognition.onend = () => {
      if (isRecording.value && !isPaused.value) {
        recognition.start()
      } else {
        setTimeout(() => recognition.stop(), 300)
      }
    }

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error)
    }
  }
}

const resumeRecording = async () => {
  // First try to reconnect WebSocket before resuming
  const wsConnected = await connectWebSocket(
    conversationStore.conversationId, 
    conversationStore.getCurrentMemoCarteData?.id_pet,
    conversationStore.getCurrentMemoCarteData?.id_employee
  )
  if (!wsConnected) {
    console.log('Failed to establish WebSocket connection, cannot resume recording')
    mtUtils.autoCloseAlert('WebSocket接続に失敗したため、録音を再開できません。')
    return
  }

  // Get fresh stream
  try {
    stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        deviceId: conversationStore.getCurrentMic,
        sampleRate: sampleRate,
        sampleSize: 16,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      },
      video: false
    })

    // Reinitialize MediaRecorder with fresh stream
    initializeMediaRecorder(stream)
    
    recognition.start()
    isPaused.value = false
    timer = setInterval(() => {
      seconds.value++
      checkRecordingLimit()
    }, 1000)
  } catch (error) {
    console.error('Error resuming recording:', error)
    mtUtils.autoCloseAlert('Failed to resume recording')
  }
}

const failedUploadQueue = ref<FormData[]>([])
const failedUploadBlobs = ref<Blob[]>([])
const isRetryingUploads = ref(false)

const retryFailedUploads = async () => {
  if (isRetryingUploads.value || failedUploadQueue.value.length === 0) return
  
  isRetryingUploads.value = true
  try {
    while (failedUploadQueue.value.length > 0) {
      const formData = failedUploadQueue.value[0]
      try {
        await koekaruApi.post(`/uploadfile`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        failedUploadQueue.value.shift() // Remove successful upload
        failedUploadBlobs.value.shift() // Remove corresponding blob
      } catch (error) {
        console.error('Error retrying upload:', error)
        // Keep the failed upload in queue and break the retry loop
        break
      }
    }
  } finally {
    isRetryingUploads.value = false
  }
}

const pauseRecording = async () => {
  recognition.stop()
  sessionMediaRecorder?.stop()
  isPaused.value = true
  
  // Close WebSocket connection
  if (wsConnection.value) {
    wsConnection.value.close()
    wsConnection.value = null
    isWsConnected.value = false
  }

  clearInterval(timer)
}

let interval: SetIntervalAsyncTimer<any[]>

let stopPromise: Promise<FormData>

const uploadAudio = async (formData: FormData) => {
  await conversationStore.uploadFullAudio(
    conversationStore.getCurrentConversation.id_conversation,
    formData
  )
  sessionAudioChunks.length = 0
}

const uploadAudioKoekaru = async (formData: FormData,) => {
  try {
    if (!secretKey) await getInstance(useClinicStore().getClinic?.code_clinic)

    const id_pet_info = conversationStore.getCurrentMemoCarteData?.id_pet || 
                       conversationStore.getCreateMemoCarteData?.id_pet

    const id_employee_info = conversationStore.getCurrentMemoCarteData?.id_employee || 
                            conversationStore.getCreateMemoCarteData?.id_employee

    if (id_pet_info) {
      formData.append('id_pet_info', id_pet_info)
    }

    if (id_employee_info) {
      formData.append('id_employee_info', id_employee_info)
    }

    await koekaruApi.post(`/uploadfile`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    // On success, clear the audio chunks
    sessionAudioChunks.length = 0
  } catch (error) {
    console.error('Error uploading audio:', error)
    // On failure, store both the formData and the blob
    failedUploadQueue.value.push(formData)
    const audioBlob = new Blob(sessionAudioChunks, { type: 'audio/mpeg-3' })
    failedUploadBlobs.value.push(audioBlob)
  }
}

const stopRecording = async (
  questionTemplateId: any = null,
  order_type: number = 2,
  conversation_id: string,
  question_id: string
) => {
  recognition.stop()
  if (mediaRecorder?.state != 'inactive') mediaRecorder?.stop()
  if (sessionMediaRecorder?.state != 'inactive') sessionMediaRecorder?.stop()

  // Close WebSocket connection and clear stored URL
  if (wsConnection.value) {
    wsConnection.value.close()
    wsConnection.value = null
    isWsConnected.value = false
  }

  isRecording.value = false
  stream?.getTracks().forEach((track) => track.stop())

  // No need to upload file since we're streaming directly
  // Just call generateSummary
  await generateSummary(
    conversation_id,
    question_id.toString(),
    order_type,
    conversationStore.requestId,
    conversationStore.onePersonSummarry
  )

  // Filter audio chunks if voice command was used
  if (voiceCommandStartTime) {
    audioChunks = audioChunks.filter((chunk) => {
      const timeDiff = chunk.timestamp - voiceCommandStartTime!
      return (
        timeDiff < -VOICE_COMMAND_BUFFER_BEFORE ||
        timeDiff > VOICE_COMMAND_BUFFER_AFTER
      )
    })
  }

  // Reset voice command state
  voiceCommandStartTime = null
  isVoiceCommand.value = false
}

const stopRecordingKoekaru = async (data: {
  orderType: number
  question_id: string
  conversation_id: string
}) => {
  recognition.stop()
  if (mediaRecorder?.state != 'inactive') mediaRecorder?.stop()
  if (sessionMediaRecorder?.state != 'inactive') sessionMediaRecorder?.stop()

  isRecording.value = false
  stream?.getTracks().forEach((track) => track.stop())

  // conversationStore.setSummaryGenerating(true)

  // const conversation_id = data.conversation_id ?? nanoid()

  const formData = await stopRecorderAndRecordingKoekaru()
  formData.append('id_conversation', conversationStore.conversationId)
  formData.append('id_vetty_request', conversationStore.requestId)
  if (!currentTranscript.value) {
    currentTranscript.value = tempFullTranscript.value
  } else {
    currentTranscript.value = tempFullTranscript.value.replace(
      currentTranscript.value,
      ''
    )
  }
  formData.append('realtime_transcription', currentTranscript.value)
  await uploadAudioKoekaru(formData)

  await generateSummaryKoekaru(
    conversationStore.conversationId,
    data.orderType,
    data.question_id
  )
}

const generateSummary = async (
  id_conversation: string,
  question_id: string,
  order_type: number,
  requestId: string,
  isOnePersonSummary: boolean
) => {
  conversationStore.setFlgRecording(false)
  const payload: {
    id_conversation: string
    question_id: string
    order_type: number
  } = {
    id_conversation: `${id_conversation}`,
    question_id: question_id,
    order_type: order_type
  }

  try {
    await koekaruApi.post('/combine-audio', payload)
  } catch (error) {
    updateNotification(3, id_conversation)
    // conversationStore.setSummaryGenerating(false)
    useConversationStore().setTranscriptChats([])
    useConversationStore().setSummary([])
    seconds.value = 0
    mtUtils.autoCloseAlert(aahMessages.failed)
    return
  }

  let status: string = ''

  while (status !== 'COMPLETED' && status !== 'ERROR') {
    const res = await koekaruApi.get(
      `/task-status?id_conversation=${id_conversation}`
    )
    status = res.data.status
    // Wait for 3 seconds
    await new Promise((resolve) => setTimeout(resolve, 3000))
  }
  if (status === 'ERROR') {
    updateNotification(3, id_conversation)
    // conversationStore.setSummaryGenerating(false)
    useConversationStore().setTranscriptChats([])
    useConversationStore().setSummary([])
    seconds.value = 0
    mtUtils.autoCloseAlert(aahMessages.failed)
    // closeDraggableModal(500)
    return
  } else {
    updateNotification(1, id_conversation)
  }
  const res = await koekaruApi.get(
    `/output-data?id_conversation=${id_conversation}`
  )

  const summaryResponse = res.data.data
  let summaryContent = ''

  conversationStore.setCurrentConversation(summaryResponse)
  if (summaryResponse.summary?.answers) {

    if (isOnePersonSummary && flg_auto_memocarte_ai.value) {
      // 【要確認 AI 自動生成】
      summaryContent += `<br/>`
    }

    const sortedAnswers = summaryResponse.summary.answers.sort(
      (a, b) => a.display_order - b.display_order
    )

    sortedAnswers.forEach((answer) => {
      const sectionKey = Object.keys(answer).find(
        (key) => key !== 'display_order'
      )

      if (sectionKey && sectionKey !== 'pet_owner_name') {
        // Add section title
        const MARKER = '[[SECTION]]' // This is for iOS v18 fix. NEVER use HTML tag to split the string (before it was <b>) as it can be rendered as HTML by iOS safari/chrome
        summaryContent += `${MARKER}<b>## ${sectionKey}</b><br/>`
        const contentArray = answer[sectionKey]
        const content = contentArray
          .filter((item) => typeof item === 'string')
          .join('<br/>')
        summaryContent += content
        summaryContent += '<br/><br/>'
      }
    })
  }

  // if (summaryResponse.transcript) {
  // conversationStore.setTranscriptChats(summaryResponse.transcript)
  // localStorage.setItem(
  //   'ai_transcript_chats',
  //   JSON.stringify(summaryResponse.transcript)
  // )
  // }

  const setSummaryTranscriptData = {
    id_conversation: id_conversation,
    summary: summaryContent,
    transcript: summaryResponse.transcript
  }

  event_bus.emit(
    'set-generated-summary-and-transcript',
    setSummaryTranscriptData
  )
  if (isOnePersonSummary && flg_auto_memocarte_ai.value) {
    submitMemoCarte(requestId, summaryContent, id_conversation)
  } else if (isOnePersonSummary && order_type === 2) {
    mtUtils.autoCloseAlert(aahMessages.success)
  } else {
    submitMemoCarte(requestId, summaryContent, id_conversation)
  }

  if (!conversationStore.flgRecording) {
    clearInterval(timer)
  }
}

const generateSummaryKoekaru = async (
  id_conversation: string,
  order_type: number = 1,
  question_id: string
) => {
  try {
    const payload: {
      id_conversation: string
      order_type: number
      question_id?: string
    } = {
      id_conversation: `${id_conversation}`,
      order_type
    }

    if (order_type === 2) {
      payload['question_id'] = question_id
    }
    await koekaruApi.post('/combine-audio', payload)

    // After this keep calling the status api, every 3 second until the status is "COMPLETED" or "ERROR"
    let status: string = ''

    while (status !== 'COMPLETED' && status !== 'ERROR') {
      const res = await koekaruApi.get(
        `/task-status?id_conversation=${id_conversation}`
      )
      status = res.data.status
      // Wait for 3 seconds
      await new Promise((resolve) => setTimeout(resolve, 3000))
    }

    const res = await koekaruApi.get(
      `/output-data?id_conversation=${id_conversation}`
    )

    const result = res.data.data
    conversationStore.setKoekaruResult(result)
  } catch (error) {
    console.error(error)
  }
}

const fetchTask = async (summaryResponse: any) => {
  const taskResponse = await conversationStore.fetchTask(
    summaryResponse.data.task_id
  )

  if (
    taskResponse.data &&
    taskResponse.data.type_prep_status === TaskStatus.COMPLETED
  ) {
    // update conversation and set summary
    await fetchConversation(taskResponse)
    // stop interval
    await clearIntervalAsync(interval)
  } else if (
    taskResponse.data &&
    taskResponse.data.type_prep_status == TaskStatus.ERROR
  ) {
    // conversationStore.setSummaryGenerating(false)
    conversationStore.setSummaryError(true) // show draggable modal in error state
    await clearIntervalAsync(interval)
  }
}

const fetchConversation = async (taskResponse: any) => {
  // const conversationResponse = await conversationStore.fetchConverstion(
  //   taskResponse.data.uuid_conversation
  // )

  conversationStore.setCurrentConversation(conversationResponse.data)
  conversationStore.setSummary(conversationResponse.data.summary)
  localStorage.setItem(
    'ai_summary',
    JSON.stringify(conversationResponse.data.summary)
  )
  conversationStore.setTranscriptChats(conversationResponse.data?.speeches)
  localStorage.setItem(
    'ai_transcript_chats',
    JSON.stringify(conversationResponse.data?.speeches)
  )
  // conversationStore.setSummaryGenerating(false)
  conversationStore.setSummaryGenerated(true)
  localStorage.setItem('ai_summary_generated', JSON.stringify(true))
  localStorage.setItem('ai_flg_recording', JSON.stringify(true))
  if (conversationStore.getAutoReflectMemoCarte) {
    submitMemoCarte()
  }
}

const updateRecordingTime = (additionalPayload: object = {}) => {
  let payload = {
    recording_time: recordingTime.value,
    ...additionalPayload
  }
  conversationStore.updateRecordingTime(
    conversationStore.getCurrentConversation.id_conversation,
    payload
  )
}

const debouncedSave = debounce((formData) => {
  try {
    const speaker = 'Vet'
    formData.append(
      'id_conversation',
      conversationStore.getCurrentConversation.id_conversation
    )
    formData.append('speaker', speaker)
    conversationStore.createSpeech(formData).then((response: any) => {
      conversationStore.addTranscriptChat(response.data)
      event_bus.emit(
        'speech_created',
        conversationStore.getTranscriptChats.length - 1
      )
    })
  } catch (e) {
    return console.error(e)
  }
}, 0)

const startFullSessionRecording = (stream: MediaStream) => {
  sessionMediaRecorder = new MediaRecorder(stream)
  sessionMediaRecorder.start(500)

  sessionMediaRecorder.ondataavailable = (event) => {
    sessionAudioChunks.push(event.data)
  }
}

const stopRecorderAndRecording = () => {
  stopPromise = new Promise((resolve, reject) => {
    sessionMediaRecorder.onstop = async () => {
      if (!conversationStore?.getCurrentConversation?.id_conversation)
        return false // for delete case
      const fullAudioBlob = new Blob(sessionAudioChunks, {
        type: 'audio/mpeg-3'
      })
      fullAudioSource.value = URL.createObjectURL(fullAudioBlob)

      const audioFormData = new FormData()
      audioFormData.append('audio_file', fullAudioBlob)
      sessionAudioChunks.length = 0

      resolve(audioFormData)
    }

    sessionMediaRecorder.onerror = (event) => {
      reject('Error: ' + event)
    }
  })

  return stopPromise
}
const downloadFullAudio = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.style.display = 'none'
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  URL.revokeObjectURL(url)
  document.body.removeChild(a)
}

const stopRecorderAndRecordingKoekaru = () => {
  stopPromise = new Promise((resolve, reject) => {
    sessionMediaRecorder.onstop = async () => {
      const fullAudioBlob = new Blob(sessionAudioChunks, {
        type: 'audio/mpeg-3'
      })
      fullAudioSource.value = URL.createObjectURL(fullAudioBlob)

      const audioFormData = new FormData()
      audioFormData.append('file', fullAudioBlob)
      sessionAudioChunks.length = 0

      resolve(audioFormData)
    }
    sessionMediaRecorder.onpause = async () => {
      const fullAudioBlob = new Blob(sessionAudioChunks, {
        type: 'audio/mpeg-3'
      })

      fullAudioSource.value = URL.createObjectURL(fullAudioBlob)

      const audioFormData = new FormData()
      audioFormData.append('file', fullAudioBlob)
      sessionAudioChunks.length = 0

      resolve(audioFormData)
    }

    sessionMediaRecorder.onerror = (event) => {
      reject('Error: ' + event)
    }
  })

  return stopPromise
}

const updatedSummary = (summary: any) => {
  const newSummary = [] as {
    ai_summary: string
    question_display_title: string
  }[]

  if (summary && summary.length && summary.length > 0) {
    const sortedAnswers = summary.sort(
      (a, b) => a.display_order - b.display_order
    )

    sortedAnswers.forEach((answer) => {
      const sectionKey = Object.keys(answer).find(
        (key) => key !== 'display_order'
      )

      if (sectionKey && sectionKey !== 'pet_owner_name') {
        newSummary.push({
          ai_summary: answer[sectionKey],
          question_display_title: sectionKey
        })
      }
    })

    return newSummary
  }
  return []
}

const oldUpdatedSummary = (summary: any) => {
  if (summary && summary.length && summary.length > 0) {
    summary = summary.filter(
      (item: any) =>
        item.ai_summary?.trim()?.replace(/\n/g, '')?.toLowerCase() != 'n/a' &&
        item.ai_summary?.trim() != ''
    )
    if (summary && summary.length && summary.length > 0) {
      summary.forEach((item: any, idx: any) => {
        item.ai_summary = item.ai_summary.trim()
        if (item.ai_summary.endsWith('\n\n')) {
          item.ai_summary = item.ai_summary.substring(
            0,
            item.ai_summary.length - 2
          )
        } else if (item.ai_summary.endsWith('\n')) {
          item.ai_summary = item.ai_summary.substring(
            0,
            item.ai_summary.length - 1
          )
        }
        if (item.ai_summary.startsWith('\n')) {
          item.ai_summary = item.ai_summary.substring(
            2,
            item.ai_summary.length - 1
          )
        }
      })
    }
    return summary
  }
  return []
}

const deleteConversation = async () => {
  let popup = {
    isConfirmed: false
  }
  await mtUtils.smallPopup(DeleteRecording, { popup })
  if (popup.isConfirmed) {
    // const formData = {
    //   id_conversation: conversationStore.conversationId
    // }
    const resDelete = await koekaruApi.post('/delete-conversation', {
      id_conversation: conversationStore.conversationId
    })

    resetRecordingData()
    return true
  } else {
    return false
  }
}

const resetRecordingData = () => {
  seconds.value = 0
  isPaused.value = isRecording.value = false
  if (timer) clearInterval(timer)
  recognition?.stop()
  sessionMediaRecorder?.stop()
  conversationStore.setCurrentConversation(null)
  conversationStore.setFlgRecording(false)
  conversationStore.setSummaryGenerated(false)
  conversationStore.setTranscriptChats([])
  conversationStore.setSummary([])
  try {
    stream.getTracks().forEach((track) => track.stop())
  } catch (error) {}
}

const submitMemoCarte = async (
  requestId: string = '',
  content: string = '',
  conversationId: string = ''
) => {
  try {
    if (!conversationStore) conversationStore = useConversationStore()
    if (!memoCarteStore) memoCarteStore = useMemoCarteStore()
    let data = useConversationStore().getRecentMemoCarteList.find(
      (item) => item.conversation_id === conversationId
    )

    let dataIndex = useConversationStore().getRecentMemoCarteList.findIndex(
      (item) => item.conversation_id === conversationId
    )
    if (!data) {
      // console.error('🔴 No memo carte data found for requestId:', requestId)
      mtUtils.autoCloseAlert('No memo carte data found for requestId')
      return
    }
    
    if (data) {
      delete data.conversation_id
    }
    
    if (data && data.id_request) {
      const cleanedContent = content.replace(/(\[\[SECTION\]\])/g, '').trim()
      data.memo_other = [data.memo_other, cleanedContent]
        .filter(Boolean)
        .join(data.memo_other ? '<br/>' : ' ')
      data.type_source = 1
      data.type_input = 2
      data.flg_verified = flgVerified.value

      // Check if we need to refresh
      let refresh =
        router.currentRoute.value.params.id == requestId ? true : false

      await useMemoCarteStore()
        .submitMemoCarte(data, refresh)
        .then(async (response) => {
          ;[
            'ai_summary',
            'ai_transcript_chats',
            'ai_summary_generated',
            'ai_flg_recording',
            'memo_cart_content'
          ].forEach((key) => {
            localStorage.removeItem(key)
          })
          useConversationStore().getRecentMemoCarteList.splice(dataIndex, 1)
          if (refresh) {
            let payload = { id_pet: data.id_pet, id_customer: data.id_customer }
            await Promise.all([useMemoCarteStore().fetchMemoCarteV1(payload)])
            event_bus.emit('reloadLeft')
          }

          if (!conversationStore.flgRecording) {
            useConversationStore().setTranscriptChats([])
            useConversationStore().setSummary([])
            seconds.value = 0
          }

          mtUtils.autoCloseAlert(aahMessages.success)
        })
    } else {
      const splitContentByHash = content.split('[[SECTION]]').map((section) => `<b>${section.trim()}`).filter((section) => section)
      
      if (splitContentByHash.length === 5) {
        
        const [memoSbjPart1, memoSbjPart2, memoObj, memoAss, memoOther] = splitContentByHash;
        data.memo_carte.memo_sbj = [data.memo_carte.memo_sbj, memoSbjPart1, memoSbjPart2]
          .filter(Boolean)
          .join(data.memo_carte.memo_sbj ? '<br/>' : ' ')
        data.memo_carte.memo_obj = [data.memo_carte.memo_obj, memoObj]
          .filter(Boolean)
          .join(data.memo_carte.memo_obj ? '<br/>' : ' ')
        data.memo_carte.memo_ass = [data.memo_carte.memo_ass, memoAss]
          .filter(Boolean)
          .join(data.memo_carte.memo_ass ? '<br/>' : ' ')
        data.memo_carte.memo_other = [data.memo_carte.memo_other, memoOther]
          .filter(Boolean)
          .join(data.memo_carte.memo_other ? '<br/>' : ' ')
      }
      data.type_input = 2
      await useMemoCarteStore()
        .submitMemoCarteV1(data)
        .then(async (response) => {
          ;[
            'ai_summary',
            'ai_transcript_chats',
            'ai_summary_generated',
            'ai_flg_recording',
            'memo_cart_content'
          ].forEach((key) => {
            localStorage.removeItem(key)
          })
          useConversationStore().getRecentMemoCarteList.splice(dataIndex, 1)
          // Check if we need to refresh
          let refresh =
            router.currentRoute.value.params.id == requestId ? true : false

          if (refresh) {
            let payload = {
              id_pet: data.memo_carte.id_pet,
              id_customer: data.memo_carte.id_customer,
              page_size: 200
            }
            await Promise.all([useMemoCarteStore().fetchMemoCarteV1(payload)])
            event_bus.emit('reloadLeft')
          }

          if (!conversationStore.flgRecording) {
            useConversationStore().setTranscriptChats([])
            useConversationStore().setSummary([])
            seconds.value = 0
          }
          
          mtUtils.autoCloseAlert(aahMessages.success)
        })
    }
  } catch (error) {
    console.error('🔴 submitMemoCarte error:', error)
    mtUtils.autoCloseAlert(aahMessages.failed)
    throw error
  }
}

const closeDraggableModal = (timeout: number = 1000) => {
  //Hide after 10 seconds
  setTimeout(() => {
    // to close draggable recording modal instance
    // event_bus.emit('close-draggable-recording-modal')
    // event_bus.emit('close-notification-modal')
    // Hide draggable recording modal
    conversationStore.setFlgRecording(false)
    conversationStore.setSummaryGenerated(false)
  }, 10 * timeout)
}

event_bus.on('recording-start', () => {
  conversationStore = useConversationStore()
  memoCarteStore = useMemoCarteStore()
  koekaruModalStore = useKoekaruModalStore()
})

event_bus.on('close-notification', () => {
  if (!koekaruModalStore) koekaruModalStore = useKoekaruModalStore()
  koekaruModalStore.closeNotificationModal()
})

const createNotification = async (
  conversationId: string,
  isOnePersonSummary: boolean,
  statusMessage: number = 0,
  seconds: number = 0,
  source: string = 'update_memo_carte'
) => {
  if (!koekaruModalStore) koekaruModalStore = useKoekaruModalStore()
  
  if (koekaruModalStore.getNotificationModalShowStatus) {
    saveCurrentRecord({
      id_conversation: conversationId,
      one_person_summarry: isOnePersonSummary,
      status_message: statusMessage,
      seconds: seconds
    })
    return
  }
  koekaruModalStore.openNotificationModal()

  // Wait for the modal component to mount before saving the record
  await nextTick()
  setTimeout(() => {
    saveCurrentRecord({
      id_conversation: conversationId,
      one_person_summarry: isOnePersonSummary,
      status_message: statusMessage,
      seconds: seconds
    })
  }, 100)
}

const updateNotification = (statusMessage: number, id_conversation: string) => {
  event_bus.emit('update-notification-status', {
    statusMessage: statusMessage,
    id_conversation
  })
}

const saveCurrentRecord = (data) => {
  if (!koekaruModalStore) koekaruModalStore = useKoekaruModalStore()
  
  if (!koekaruModalStore.getNotificationModalShowStatus) {
    createNotification(
      data.id_conversation,
      data.one_person_summarry,
      data.status_message,
      data.seconds
    )
    return
  }
  let newRecord = {
    idConversation: data.id_conversation,
    isOnePersonSummaryRecord: data.one_person_summarry,
    statusMessage: data.status_message,
    seconds: data.seconds
  }

  event_bus.emit('add-notification-list', newRecord)
}

const parseSummary = (summary: string) => {
  const sections = {
    memo_sbj: '',
    memo_obj: '',
    memo_ass: '',
    memo_other: ''
  }

  const sectionTexts = summary.split('<br/>')

  let currentSection = ''
  sectionTexts.forEach((line) => {
    if (line.includes('## 主観')) {
      currentSection = 'memo_sbj'
    } else if (line.includes('## 客観')) {
      currentSection = 'memo_obj'
    } else if (line.includes('## 評価')) {
      currentSection = 'memo_ass'
    } else if (line.includes('## 計画他')) {
      currentSection = 'memo_other'
    } else if (currentSection && line.trim()) {
      sections[currentSection] += line + '<br/>'
    }
  })

  return sections
}

const handleLimitRecording = async () => {
  if (document.querySelector('.q-dialog')) {
    return false
  }

  if (conversationStore.getSource === 'create_memo_carte') {
    isPaused.value = true
    return false
  }

  let popup = {
    isConfirmed: false,
    id_template: '1',
    apiOptions: 'speech'
  }

  await mtUtils.smallPopup(RecordingLimitConfirmation, { popup })

  if (popup.isConfirmed) {
    const conversation_id = conversationStore.conversationId
    createNotification(conversation_id, false)
    await stopRecording(
      popup.id_template,
      2,
      conversation_id,
      popup.id_template
    )
    event_bus.emit('close-draggable-recording-modal')
    return true
  }

  isPaused.value = true
  return false
}

export const isExternalMic = (label: string): boolean => {
  const lowerLabel = label.toLowerCase()  
  if (label.includes('外部接続マイク')) {
    return true
  }
  
  const externalKeywords = [
    // Japanese keywords
    '外付け',
    'エクステーナル',
    'ブルートゥース',
    'usb',
    // English keywords
    'external',
    'bluetooth',
    'wireless',
    'usb',
    // Device specific keywords
    'pro x',
    'lightspeed',
    'web camera',
    'webcam',
    // Hardware identifiers
    '046d',  // Logitech vendor ID
    '05a3'   // Generic webcam vendor ID
  ]
  
  const result = externalKeywords.some(keyword => lowerLabel.includes(keyword.toLowerCase()))
  return result
}

export function useRecording() {
  return {
    recordConfig,
    michrophoneList,
    microphonesListDefault,
    questionTemplatesList,
    questionTemplatesListDefault,
    isPaused,
    isRecording,
    flgMemoCarteSubmitted,
    flgVerified,
    toggleTranscribing,
    startRecording,
    resumeRecording,
    pauseRecording,
    seconds,
    timer,
    recordingTime,
    recordingButtonIcon,
    stopRecording,
    stopRecordingKoekaru,
    tempTranscript,
    fullTranscript,
    fullAudioSource,
    resetRecordingData,
    updatedSummary,
    deleteConversation,
    closeDraggableModal,
    submitMemoCarte,
    createNotification,
    oldUpdatedSummary,
    tempFullTranscript,
    saveCurrentRecord,
    selectedServiceType,
    updateFeedbackData,
    feedbackData,
    transcriptWithTimestamp,
    setTranscriptData,
    flg_auto_memocarte_ai,
    recordingLimit,
    checkRecordingLimit,
    handleLimitRecording,
    isVoiceCommand,
    voiceCommandStartTime,
    isExternalMic,
    failedUploadQueue,
    failedUploadBlobs,
    retryFailedUploads,
    wsConnection,
    isWsConnected,
    connectWebSocket,
    wsUrl,
    simulateWebSocketDisconnect: () => {
      if (wsConnection.value && wsConnection.value.readyState === WebSocket.OPEN) {
        console.log('Simulating WebSocket disconnection...')
        wsConnection.value.close(1006, 'Simulated disconnect')
      }
    }
  }
}
