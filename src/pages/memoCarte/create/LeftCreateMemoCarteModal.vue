<script setup lang="ts">
import MtInputForm from '@/components/form/MtInputForm.vue'
import useMemoCarteStore from '@/stores/memo-cartes'
import useCliCommonStore from '@/stores/cli-common'
import { dateFormat, scrollAreaHeight, truncateAndSearch } from '@/utils/aahUtils'
import { computed, onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'

const emits = defineEmits(['setMemoCarteContent'])
const memoCarteStore = useMemoCarteStore()
const cliCommonStore = useCliCommonStore()

const { getFilteredMemoCartesV1 } = storeToRefs(memoCarteStore)
const memoCarteSearch = ref('')
const isSearch = ref(false)
const memoCarteInitList = ref([])
const leftSidebarMemoCarteRef = ref(null)

const showLoadMore = ref(false)
const currentPage = ref(1)
const pageSize = ref(15) 
const props = defineProps({
  id_pet: String,
  id_customer:String,
  selectedMemo: String
});
const itemsToShow = ref(20)
const displayedMemoCarteList = ref([])
import {
  CliCommon,
  MemoCarteGroupType,
} from '@/types/types'
const {
  getCliCommonOptionList,
} = storeToRefs(cliCommonStore)

const searchMemoCarte = () => {
  isSearch.value = true

  displayedMemoCarteList.value = memoCarteInitList.value.filter((v) =>
    v.memo_sbj?.toLowerCase().includes(memoCarteSearch.value.toLowerCase()) || v.memo_other?.toLowerCase().includes(memoCarteSearch.value.toLowerCase())
  )
}

const getMemoCarteBgColor = (memoCarte: any) => {
  if (memoCarte) {
    const cli = getCliCommonOptionList.value.find(
      (v: CliCommon) => v.id_cli_common == memoCarte?.id_cli_common
    )
    return cli ? cli.text1 : 'memo-carte'
  }
  return 'memo-carte'
}
const getLabel = (memoCarte: any) => {
  if (memoCarte) {
    const cli = getCliCommonOptionList.value.find(
      (v: CliCommon) => v.id_cli_common == memoCarte?.id_cli_common
    )
    return cli ? cli.label : ''
  }
  return ''
}


const formatDate = (dateString: string) => {
  const dt = new Date(dateString)
  const year = dt.getFullYear()
  const month = String(dt.getMonth() + 1).padStart(2, '0')
  const day = String(dt.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}

function stripHtmlTags(input: any, maxLength: number) {
  if (!input) return "";
  const doc = new DOMParser().parseFromString(input, 'text/html')
  const text = doc.body.textContent || ""
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
}

const handleClick = (memo: any) => {
  const dt_insert = new Date(memo.datetime_memo_carte);
  const dt= memo.datetime_memo_carte
  const formattedDate = formatDate(dt_insert.toISOString()); 
  const filterData = getFilteredMemoCartesV1?.value[formattedDate]?.others[memo.datetime_memo_carte];
  const clinicalFile =  getFilteredMemoCartesV1?.value[formattedDate]?.clinical_file_list;
  const filterFiles = clinicalFile?.filter(
    (v) => v.datetime_receive == dt
  )
  emits('setMemoCarteContent', {
    id_memo_carte: memo.id_memo_carte,
    filterFiles,
    filterData,
    memo,
  });
}; 

const getMaxWidth = computed(() => {
  if (leftSidebarMemoCarteRef.value) {
    return leftSidebarMemoCarteRef.value.clientWidth
  }
  return 0
})

const fetchMemoCartesUntil = async () => {
  showLoadMore.value = false
  const aggregator = [] as any[]
  const params = {
    id_pet: props.id_pet,
    search_word: memoCarteSearch.value,
    page: currentPage.value,
    page_size: pageSize.value
  }
  try {
    await memoCarteStore.fetchMemoCartesForLeftCreateMemoCarte(params)
    if (memoCarteStore.getCreateMemoCarteLeftSidebarList.length === 0) {
      return
    }
    memoCarteStore.getCreateMemoCarteLeftSidebarList?.forEach(async (memo_group: MemoCarteGroupType) => {
      if (aggregator.find((v) => v.id_memo_carte === memo_group.memo_carte.id_memo_carte)) {
        return
      }
      // const customerName = await getCustomer(memo)
      aggregator.push({
        ...memo_group.memo_carte,
        id: memo_group.id,
        datetime_memo_carte: memo_group.datetime_group_carte,
        additional_info: memo_group,
        // customer_name: customerName,
      })
    })
    displayedMemoCarteList.value.push(...aggregator.slice(0, itemsToShow.value))
    memoCarteInitList.value = [...displayedMemoCarteList.value]

    if (aggregator.length > 0) {
      currentPage.value++
      showLoadMore.value = true
    } else {
      return
    }
  } catch (error) {
    console.error("Error fetching memo cartes:", error)
    return
  }
}

onMounted(async () => {
  currentPage.value = 1
  await fetchMemoCartesUntil()
  handleClick(displayedMemoCarteList.value[0])
});

</script>

<template>
  <div style="z-index: 10;">
    <q-form @submit.prevent="searchMemoCarte" class="flex no-wrap q-mb-sm q-mt-sm">
      <MtInputForm
        type="text"
        class="full-width q-mr-sm"
        v-model="memoCarteSearch"
        @updatedValue="searchMemoCarte()"
      />
      <q-btn type="submit">
        <q-icon name="search" size="10" />
      </q-btn>
    </q-form>
    <q-scroll-area style="width: 100%;" :style="scrollAreaHeight(100, 303)">
      <div ref="leftSidebarMemoCarteRef" class="full-width">
        <q-list dense>
          <q-item
            v-for="(memo, index) in displayedMemoCarteList"
            :key="index"
            @click="handleClick(memo)"
            :class="['bg-' + getMemoCarteBgColor(memo), { 'selected-row': props.selectedMemo && props.selectedMemo == memo.id_memo_carte }]"
            :style="{ backgroundColor: props.selectedMemo && props.selectedMemo == memo.id_memo_carte ? 'lightgray' : getMemoCarteBgColor(memo) }"
            class="full-width"
            clickable
            v-ripple
          >
            <q-item-section>
              <div class="q-pa-none q-ma-none body1">
                <div class="full-width" style="font-size:12px">
                  <span class="text-grey-700 q-mr-sm">{{ dateFormat(memo.datetime_memo_carte, 'YY/MM/DD') }}</span>
                  <span class="text-white bg-[#424242]" style="display: inline-block; margin-right: 8px; background-color:#424242; padding: 1px 5px; border-radius:4px; font-size:8px" >
                    {{ getLabel(memo) }}
                  </span>
                  <span class="text-grey-500">{{ memo.customer_name }}</span>
                  <span 
                    v-if="memo.pet_illness_history_list?.length > 0" 
                    class="illness text-grey-800" 
                  >
                    {{ stripHtmlTags(memo.pet_illness_history_list.map(item => item.name_disease).join(' , '), 5) }}
                  </span>
                  <div class="ellipsis" :style="{ maxWidth: getMaxWidth - 33 + 'px' }" v-html="truncateAndSearch(memo.memo_sbj || memo.memo_other, -1, memoCarteSearch)"></div>
                </div>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
        <q-btn
          @click="fetchMemoCartesUntil"
          v-if="showLoadMore && displayedMemoCarteList.length >= pageSize"
          class="q-mt-sm flex justify-center items-center"
          style="margin: 0 auto; background-color: transparent; box-shadow: none; color: #333;"
          flat
        >
          <q-icon name="arrow_drop_down" size="24px" />
          <span style="margin-left: 5px; font-size: 14px; color: #333;">+15件</span>
        </q-btn>
      </div>
    </q-scroll-area>
  </div>
</template>
<style scoped>
.selected-row {
  font-weight: bold;
  background-color: lightgray;
}

.row-container span {
  font-size: 13px;
  margin-right: 8px;
  white-space: nowrap;
}

.illness {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
