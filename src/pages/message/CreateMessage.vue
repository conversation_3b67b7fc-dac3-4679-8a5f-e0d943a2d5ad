<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { aahUtilsGetEmployeeName, decoder, formatHoursMinutes, timeDifferences } from '@/utils/aahUtils'
import ViewMessageFile from './ViewMessageFile.vue'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { FileType, MessageAttributeListType, MessageImageFileType, MessageThreadType, MessageType } from '@/types/types'
import useEmployeeStore from '@/stores/employees'
import useMessageStore from '@/stores/message-clinic'

type PropsDataType = {
  type_department: number
  typeMessage: number
  messageTextarea: string
  id_file: null
  id_employee: string
  name_employee: string
  id_employee_insert: string
  filedata: []
}

const today = new Date()
const selectedMessage = ref('')
const employeeStore = useEmployeeStore()
const messageStore = useMessageStore()
const { getMessages } = storeToRefs(messageStore)
const messageBox = ref()
const windowInnerHeight = ref(window.innerHeight)
const showScrollToBottom = ref(false)

const props = withDefaults(
  defineProps<{
    allTypeThreads: MessageThreadType[]
    data: PropsDataType
    messageAttributeList: MessageAttributeListType[]
    headerElementHeight: number
    messageFontSize: number
    chatMessageHeight: number
    employeeId: string
    selectedThread: MessageThreadType
  }>(),
  {
    allTypeThreads: () => {
      return [] as MessageThreadType[]
    },
    data: () => {
      return {} as PropsDataType
    },
    messageAttributeList: () => {
      return [] as MessageAttributeListType[]
    },
    headerElementHeight: 0,
    messageFontSize: 0,
    chatMessageHeight: 0,
    employeeId: ''
  }
)

const emits = defineEmits<{
  (e: 'copyMessageLink', value: MessageType): void
  (e: 'deleteMessage', value: MessageType): void
  (
    e: 'openImageViewModal',
    path: FileType[] | string,
    index: number,
    singleImage: boolean
  ): void
  (e: 'scrollToBottom', forceImmediate?: boolean): void
}>()

const copyMessageLink = (data: MessageType) => {
  emits('copyMessageLink', data)
}

const deleteMessage = (data: MessageType) => {
  emits('deleteMessage', data)
}

const scrollToBottom = (forceImmediate = false) => {
  if (forceImmediate) {
    // For immediate scrolling, wait for images and content to load
    const waitForContent = () => {
      const messageBoxElement = messageBox.value
      if (!messageBoxElement) return

      // Wait for any images to load
      const images = messageBoxElement.querySelectorAll('img') as NodeListOf<HTMLImageElement>
      const imagePromises = Array.from(images).map((img) => {
        if (img.complete) return Promise.resolve()
        return new Promise(resolve => {
          img.onload = resolve
          img.onerror = resolve
          // Timeout after 1 second to avoid hanging
          setTimeout(resolve, 1000)
        })
      })

      Promise.all(imagePromises).then(() => {
        emits('scrollToBottom', forceImmediate)
      })
    }

    // Wait a bit for DOM to render, then wait for content
    setTimeout(waitForContent, 100)
  } else {
    emits('scrollToBottom', forceImmediate)
  }
}

const openImageViewModal = (
  path: FileType[] | string,
  index: number,
  singleImage: boolean
) => {
  emits('openImageViewModal', path, index, singleImage)
}

const selectMessage = (messageId: string) => {
  // if a user clicks the same message twice, then deselect the message
  if (selectedMessage.value === messageId) {
    selectedMessage.value = ''
    return selectedMessage.value
  }

  selectedMessage.value = messageId
  return selectedMessage.value
}

const isUnderTenMins = (message: MessageType) => {
  const msgTime = message.datetime_insert
  const diff = timeDifferences(today, msgTime, 'minutes')
  const isToday = dayjs(message?.datetime_insert).isToday()

  if (isToday && diff <= 10) {
    return true
  } else {
    return false
  }
}

const handleEmpName = (empId: string) => {
  return aahUtilsGetEmployeeName(employeeStore.getAllEmployees, empId)
}

// Throttle function to improve scroll performance
const throttle = (func: Function, delay: number) => {
  let timeoutId: number | null = null
  let lastExecTime = 0
  return function (this: any, ...args: any[]) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) clearTimeout(timeoutId)
      timeoutId = window.setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

const handleShowScrollToBottomCore = (data: any) => {
  const { clientHeight, scrollHeight, scrollTop } = data.target

  // Hide button if there are no messages or content doesn't overflow
  if (!getMessages.value || getMessages.value.length === 0 || scrollHeight <= clientHeight) {
    showScrollToBottom.value = false
    return showScrollToBottom.value
  }

  // Calculate if user is near the bottom with tolerance for different devices
  // Use different tolerance based on device type for better UX
  let tolerance = 5 // Default tolerance
  if (isMobile.value) tolerance = 10 // Higher tolerance for mobile
  if (isTablet.value) tolerance = 8 // Medium tolerance for tablets

  const totalHeight = clientHeight + scrollTop
  const isNearBottom = (scrollHeight - totalHeight) <= tolerance

  // Show button only when user is NOT at the bottom and there's content to scroll
  showScrollToBottom.value = !isNearBottom
  return showScrollToBottom.value
}

// Throttled version of the scroll handler for better performance
const handleShowScrollToBottom = throttle(handleShowScrollToBottomCore, 100)

const downloadPdfFile = (item: MessageImageFileType) => {
  const a = document.createElement('a')
  a.href = item.file_url
  a.download = a.href
  a.target = '_blank'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

const isIOSSafari = ref(false);
const isMobile = ref(false);
const isTablet = ref(false);

// Enhanced device detection for better cross-platform support
const detectDeviceAndBrowser = () => {
  const userAgent = navigator.userAgent;

  // Detect iOS devices (iPhone, iPad, iPod)
  const isIOSDevice = /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;

  // Detect Safari browser (excluding Chrome on iOS which also contains Safari in UA)
  const isSafariBrowser = userAgent.includes('Safari') && !userAgent.includes('Chrome') && !userAgent.includes('CriOS');

  // Detect iPad specifically (including newer iPads that may not have "iPad" in UA)
  const isIPad = userAgent.includes('iPad') ||
    (userAgent.includes('Macintosh') && 'ontouchend' in document);

  // Detect mobile devices
  isMobile.value = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  // Detect tablets (iPad, Android tablets)
  isTablet.value = isIPad || /Android.*Tablet|Android.*Tab/i.test(userAgent);

  // Set iOS Safari flag
  isIOSSafari.value = (isIOSDevice && isSafariBrowser) || (isIPad && isIOSDevice);
};

onMounted(() => {
  detectDeviceAndBrowser();

  // Initial check for scroll button visibility
  setTimeout(() => {
    if (messageBox.value) {
      const { clientHeight, scrollHeight } = messageBox.value
      if (scrollHeight <= clientHeight || !getMessages.value || getMessages.value.length === 0) {
        showScrollToBottom.value = false
      }
    }
  }, 100)
});

// Check if user is at bottom before new messages arrive
const checkScrollPosition = () => {
  const element = messageBox.value
  if (!element) return false

  const { clientHeight, scrollHeight, scrollTop } = element
  const tolerance = isMobile.value ? 10 : 5
  const totalHeight = clientHeight + scrollTop
  return (scrollHeight - totalHeight) <= tolerance
}

// Track if this is the first time messages are loaded
const isFirstLoad = ref(true)

watch(getMessages, async (newMessages, oldMessages) => {
  // Wait for DOM to update
  await nextTick()

  // If this is the first load or page reload (no old messages), always scroll to bottom
  if (isFirstLoad.value || !oldMessages || oldMessages.length === 0) {
    isFirstLoad.value = false
    // Wait a bit more to ensure all content is rendered
    setTimeout(() => {
      scrollToBottom(true)
    }, 200)
  } else {
    // For subsequent message updates, only auto-scroll if user was already at the bottom
    // This prevents interrupting user's reading when new messages arrive
    if (checkScrollPosition()) {
      scrollToBottom()
    }
  }
}, { immediate: true });

// Watch for selectedThread changes (new conversation loaded)
watch(() => props.selectedThread, (newThread, oldThread) => {
  if (newThread && newThread !== oldThread) {
    // Reset first load flag when switching threads
    isFirstLoad.value = true
  }
});
</script>

<template>
  <div
    ref="messageBox"
    id="messageBox"
    class="chat-blk"
    :style="{
      maxHeight: `calc(100vh - ${
        props.data.typeMessage === 1
          ? isIOSSafari ? props.chatMessageHeight + 80 : props.chatMessageHeight + 10
          : props.headerElementHeight + 45
      }px)`
    }"
    @scroll="handleShowScrollToBottom"
  >
    <div
      v-if="selectedThread?.memo_goal"
      class="goal_memo q-mt-xl q-pa-sm"
      :style="`font-size: ${messageFontSize.toFixed(1)}em`"
    >
      <div class="goal_memo_text">
        スレッド目的：<br />
        <div v-html="decoder(selectedThread?.memo_goal)" />
      </div>
    </div>
    <div
      v-if="getMessages && props.data?.typeMessage === 1"
      v-for="(item, index) in getMessages"
      :key="index"
    >
      <div
        v-if="
          props.messageAttributeList[index]?.isDayStartSeparator !== null ||
          props.messageAttributeList[index]?.isStartToday ||
          props.messageAttributeList[index]?.isStartYesterday
        "
        class="flex flex-center no-wrap full-width file-divider q-my-lg"
      >
        <span class="line"></span>
        <span
          v-if="props.messageAttributeList[index]?.isDayStartSeparator !== null"
          class="text dateLetterSpacing q-pa-sm"
        >
          {{ props.messageAttributeList[index]?.isDayStartSeparator }}
        </span>
        <span
          v-if="props.messageAttributeList[index]?.isStartToday"
          class="text dateLetterSpacing q-pa-sm"
        >
          今日
        </span>
        <span
          v-if="props.messageAttributeList[index]?.isStartYesterday"
          class="text dateLetterSpacing q-pa-sm"
        >
          昨日
        </span>
        <span class="line"></span>
      </div>
      <div
        v-if="item?.message?.includes('さんによる確認が完了しました。 このスレッドは終了します。')"
        :style="`font-size: ${messageFontSize.toFixed(1)}em`"
        class="goalAchivedAlert q-mt-xl q-pa-sm"
      >
        <span class="goalAchivedTxt">{{ item?.message }}</span>
      </div>
      <div class="flex items-center no-wrap">
        <div v-if="item?.id_message === selectedMessage">
          <q-btn
            :ripple="true"
            @click="deleteMessage(item)"
            v-if="
              isUnderTenMins(item) && item?.id_employee !== props.employeeId
            "
            padding="2px"
            class="q-ml-xs deleteBtn"
            unelevated
          >
            <q-icon name="cancel" size="22px" />
          </q-btn>
          <q-btn
            :ripple="true"
            @click="copyMessageLink(item)"
            v-if="item?.id_employee !== props.employeeId"
            padding="2px"
            class="q-ml-xs copyBtn"
            unelevated
          >
            <q-icon name="link" size="22px" />
          </q-btn>
        </div>
        <q-chat-message
          :id="item.id_message"
          v-if="!item?.message?.includes('さんによる確認が完了しました。 このスレッドは終了します。')"
          :class="
            (item && item.id_file && item.id_file.content_type && (item?.id_file?.content_type?.includes('image') || item?.id_file?.content_type?.includes('video')))
              ? 'sendedImageDiv'
              : props.messageAttributeList[index]?.isASingleEmoji
              ? 'emojiMsg'
              : item?.id_employee === props.employeeId &&
                item?.message?.length > 250
              ? 'longSend'
              : item?.id_employee === props.employeeId &&
                item?.message?.length <= 50
              ? 'shortSend'
              : item?.id_employee === props.employeeId &&
                item?.message?.length > 50
              ? 'mediumSend'
              : item?.id_employee !== props.employeeId &&
                item?.message?.length > 250
              ? 'longRecieved'
              : item?.id_employee !== props.employeeId &&
                item?.message?.length <= 50
              ? 'shortRecieved'
              : 'mediumRecieved'
          "
          :sent="item?.id_employee === props.employeeId"
          :name="
            props.messageAttributeList[index]?.showTimestamp &&
            item?.datetime_insert &&
            item?.id_employee === props.employeeId
              ? handleEmpName(item?.id_employee) +
                ', ' +
                formatHoursMinutes(item?.datetime_insert)
              : props.messageAttributeList[index]?.showTimestamp &&
                item?.datetime_insert < 1 &&
                item?.id_employee === props.employeeId
              ? handleEmpName(item?.id_employee)
              : props.messageAttributeList[index]?.showTimestamp &&
                item?.datetime_insert &&
                item?.id_employee !== props.employeeId
              ? handleEmpName(item?.id_employee) +
                ', ' +
                formatHoursMinutes(item?.datetime_insert)
              : props.messageAttributeList[index]?.showTimestamp &&
                item?.datetime_insert < 1 &&
                item?.id_employee !== props.employeeId
              ? handleEmpName(item?.id_employee)
              : item.id_employee !== props.employeeId && (index === 0 || getMessages[index - 1]?.id_employee !== item.id_employee)
              ? handleEmpName(item?.id_employee) + (item?.datetime_insert ? ', ' + formatHoursMinutes(item?.datetime_insert) : '')
              : ''
          "
          :style="`font-size:${messageFontSize.toFixed(1)}em; width:100%`"
        >
          <div
            v-if="
              props.messageAttributeList[index]?.isASingleEmoji || item?.message
            "
            :class="
              props.messageAttributeList[index]?.isASingleEmoji
                ? 'single-emoji'
                : ''
            "
          >
            <div
              class="cursor-pointer"
              @click="selectMessage(item?.id_message)"
              v-if="item?.message"
              v-html="decoder(item?.message)"
            />
          </div>
          <div
            @click="selectMessage(item?.id_message)"
            class="flex"
            v-if="item?.id_file?.content_type"
          >
            <div
              class="q-ml-xs cursor-pointer"
              v-if="item?.id_file?.content_type?.includes('image')"
              @click="openImageViewModal(item?.id_file?.file_url, 0, true)"
            >
              <q-img class="imageSize" :src="item?.id_file?.thumbnail_url" alt="" />
            </div>
            <div v-if="item?.id_file?.content_type?.includes('video')" class="q-ml-xs cursor-pointer video-size">
              <video style="" controls class="video-content">
                <source :src="item?.id_file?.file_url" type="video/mp4" />
              </video>
            </div>
            <div v-if="item?.id_file?.content_type?.includes('pdf')" class="q-ml-xs cursor-pointer video-size">
              <span @click="downloadPdfFile(item.id_file)">
                <q-icon name="picture_as_pdf" size="xl" />
                {{ item.id_file.file_name ? item.id_file.file_name : 'File.pdf' }}
              </span>
            </div>
          </div>
        </q-chat-message>
        <div v-if="item?.id_message === selectedMessage">
          <q-btn
            v-if="
              isUnderTenMins(item) && item?.id_employee === props.employeeId
            "
            :ripple="true"
            padding="2px"
            class="q-ml-xs deleteBtn"
            unelevated
            @click="deleteMessage(item)"
          >
            <q-icon name="cancel" size="22px" />
          </q-btn>
          <q-btn
            v-if="item?.id_employee === props.employeeId"
            :ripple="true"
            padding="2px"
            class="q-ml-xs copyBtn"
            unelevated
            @click="copyMessageLink(item)"
          >
            <q-icon name="link" size="22px" />
          </q-btn>
        </div>
      </div>
    </div>
    <ViewMessageFile
      :data="data"
      :messageAttributeList="props.messageAttributeList"
      :employeeId="props.employeeId"
      :allTypeThreads="props.allTypeThreads"
      @open-image-view-modal="openImageViewModal"
    />
    <div
      class="column full-width flex-center logoComponent"
      v-if="getMessages.length < 1 && props.allTypeThreads?.length"
    >
      <!-- <img src="@/assets/img/login/aah_logo.svg" alt="logo" class="logoImage" /> -->
      {{ 'メッセージを投稿しましょう！' }}
    </div>
  </div>
  <div
    class="scroll-to-bottom-btn"
    :class="{
      show: showScrollToBottom,
      'mobile': isMobile,
      'tablet': isTablet,
      'ios-safari': isIOSSafari
    }"
    v-if="getMessages && getMessages.length > 0"
  >
    <q-btn color="primary" @click="() => scrollToBottom()">
      <q-icon size="22px" name="vertical_align_bottom" />
    </q-btn>
  </div>
</template>

<style lang="scss" scoped>
.q-message {
  margin-bottom: 0px;
}

.chat-blk {
  width: 98%;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: 12px;
  margin-bottom: 10px;
  height: 100vh;
}
.chat-blk::-webkit-scrollbar {
  margin-left: 15px;
  width: 14px;
}
.chat-blk::-webkit-scrollbar-thumb {
  border: 4px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: #aaaaaa;
}
.file-divider {
  .line {
    flex-grow: 1;
    border: 1px solid #dddddd;
  }
  .text {
    margin: 0px 5px !important;
    font-size: 12px;
    color: #868686;
  }
}
.sendedImageDiv {
  :deep(.q-message-text) {
    padding: 0px;
    font-size: 12px;
    font-weight: 300;
    max-width: 600px;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 0px 20px;
      background-color: transparent !important;
    }
  }
  .imageSize {
    width: 360px;
    height: 240px;
    object-fit: cover;
    border-radius: 5px;
  }
  .video-size {
    width: 360px;
    height: 250px;
    border-radius: 5px;
    .video-content {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    @media screen and (max-width: 375px) {
      width: 300px;
    }
  }
  .lasImag {
    position: absolute;
    top: 13px;
    bottom: 0;
    width: 80px;
    height: 80px;
    opacity: 0.7;
    border-radius: 4px;
    font-size: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: black;
    color: white;
  }
}
.emojiMsg {
  :deep(.q-message-text) {
    max-width: 500px;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 0px 20px;
      background-color: transparent !important;
    }
  }
}
.shortSend {
  :deep(.q-message-text) {
    margin-top: 0px;
    padding: 14px;
    line-height: 1.6;
    font-weight: 300;
    max-width: 250px;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 0px 20px;
      background-color: #bff1ff;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.mediumSend {
  :deep(.q-message-text) {
    margin-top: 0px;
    padding: 14px;
    line-height: 1.6;
    font-weight: 300;
    max-width: 400px;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 0px 20px;
      background-color: #bff1ff;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.longSend {
  :deep(.q-message-text) {
    margin-top: 0px;
    padding: 14px;
    line-height: 1.6;
    font-weight: 300;
    max-width: 700px;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 0px 20px;
      background-color: #bff1ff;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.longRecieved {
  font-size: 12px;
  font-weight: 300;
  max-width: 700px;
  color: #888;
  :deep(.q-message-text) {
    line-height: 1.6;
    margin-top: 0px;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 20px 0px;
      background-color: #c9c9c9 !important;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.mediumRecieved {
  font-size: 12px;
  font-weight: 300;
  max-width: 400px;
  color: #888;
  :deep(.q-message-text) {
    margin-top: 0px;
    line-height: 1.3;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 20px 0px;
      background-color: #c9c9c9 !important;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.shortRecieved {
  font-size: 12px;
  font-weight: 300;
  max-width: 250px;
  color: #888;
  :deep(.q-message-text) {
    margin-top: 0px;
    line-height: 1.3;
    cursor: pointer;
    &:last-child::before {
      display: none !important;
    }
    &:last-child {
      border-radius: 20px 20px 20px 0px;
      background-color: #c9c9c9 !important;
    }
  }
  :deep(.q-message-text--send) {
    color: black;
  }
}
.single-emoji {
  font-size: 70px !important;
  padding: 0px !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  transform: translateX(2px);
}
.goal_memo {
  width: 70%;
  background-color: #efefd2 !important;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto 15px auto;
  overflow: hidden;
}
.goalAchivedAlert {
  margin-top: 20px!important;
  width: 70%;
  background-color: #dbdbdb !important;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px auto 15px auto;
  overflow: hidden;
}
.goal_memo_text {
  line-height: 1.3;
  text-align: center;
  color: #576100 !important;
}
.goalAchivedTxt {
  line-height: 1.3;
  text-align: center;
  color: #6b6b6b !important;
}
.dateLetterSpacing {
  letter-spacing: 1px;
}
.logoComponent {
  margin-top: 5%;
}
.logoImage {
  width: 224px;
  height: 39px;
}
.deleteBtn {
  border-radius: 50%;
}
.copyBtn {
  border-radius: 50%;
}
.scroll-to-bottom-btn {
  position: fixed;
  z-index: 1000;
  display: none;
  transition: all 0.3s ease-in-out;

  &.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .scroll-btn {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Default positioning for desktop
  bottom: 120px;
  right: 20px;

  // Mobile devices (phones)
  &.mobile {
    bottom: 100px;
    right: 16px;

    .scroll-btn {
      width: 48px;
      height: 48px;
    }
  }

  // Tablet devices
  &.tablet {
    bottom: 110px;
    right: 24px;

    .scroll-btn {
      width: 52px;
      height: 52px;
    }
  }

  // iOS Safari specific adjustments
  &.ios-safari {
    bottom: 140px; // Account for Safari's bottom bar

    &.mobile {
      bottom: 120px;
    }

    &.tablet {
      bottom: 130px;
    }
  }

  bottom: 240px;
  right: 12px;

  // High DPI displays
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .scroll-btn {
      border: 0.5px solid rgba(255, 255, 255, 0.1);
    }
  }

  // Accessibility improvements
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    .scroll-btn {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }

  // Dark mode support (if implemented)
  @media (prefers-color-scheme: dark) {
    .scroll-btn {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
      }
    }
  }
}
</style>
