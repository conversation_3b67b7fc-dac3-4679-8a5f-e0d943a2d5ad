<script setup lang="ts">
import { computed, onMounted, ref, watch, inject } from 'vue'
import dayjs from '@/boot/dayjs'
import { storeToRefs } from 'pinia'
import useBookingItemStore from '@/stores/booking-items'
import useWorkScheduleStore from '@/stores/work-schedules'
import useClinicStore from '@/stores/clinics'
import mtUtils from '@/utils/mtUtils'
import { typeBusinessDay } from '@/utils/enum'
import { flatten } from 'lodash'
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import BulkUpdateShiftModal from './BulkUpdateShiftModal.vue'
import UpdateBulkShiftModal from '@/pages/employeeAvailability/components/UpdateBulkShiftModal.vue'
import UpdateEmployeeModal from '@/pages/master/employee/UpdateEmployeeModal.vue'

import {
  EmployeeType
} from '@/types/types'
import selectOptions from '@/utils/selectOptions'

// State management
const selectedMonth = ref(dayjs().startOf('month'))
const selectedMonthLabel = ref('')
const isLoading = ref(false)
const monthlyData = ref<any[]>([])
const clinicName = ref('')
const bulkUpdateData = ref<any[]>([])
const employees = ref<any[]>([])
const displaySlotsTime = ref(false)

// Store instances
const bookingItemStore = useBookingItemStore()
const workScheduleStore = useWorkScheduleStore()
const clinicStore = useClinicStore()

// Get the clinic ID and date range from props or localStorage
const props = defineProps<{
  clinicId?: number | null
  startDate?: string
  endDate?: string
  editMode?: boolean
  booking_item_id?: number
}>()

// Use provided editMode and displayMode from props or inject
const editMode = computed(() => {
  return props.editMode
})

// Get the clinic ID from props or localStorage
const clinicId = computed(() => {
  // First check if props.clinicId is available
  if (props.clinicId) {
    return props.clinicId
  }
  
  // Finally fallback to localStorage
  const storedId = localStorage.getItem('id_clinic')
  return storedId ? JSON.parse(storedId) : null
})

// Watch for clinic ID changes to refetch data
watch(clinicId, async (newClinicId) => {
  if (newClinicId) {
    await fetchClinicName()
    await fetchMonthlySchedule()
  }
})

// Set up the start and end date
const startDateValue = computed(() => {
  if (props.startDate) {
    return props.startDate
  }
  
  return selectedMonth.value.format('YYYY-MM-DD')
})

const endDateValue = computed(() => {
  if (props.endDate) {
    return props.endDate
  }
  
  return selectedMonth.value.endOf('month').format('YYYY-MM-DD')
})

// Watch for date changes
watch([startDateValue, endDateValue], ([newStart, newEnd]) => {
  if (newStart && newEnd) {
    selectedMonth.value = dayjs(newStart).startOf('month')
    selectedMonthLabel.value = selectedMonth.value.format('YYYY年MM月')
    fetchMonthlySchedule()
  }
})

// Interface definitions
interface EmployeeSchedule {
  id_employee_workschedule: number | null
  time_workschedule_start?: string
  time_workschedule_end?: string
  flg_whole_dayoff: boolean
  checked: boolean
}

interface EmployeeSchedules {
  [key: string]: {
    [key: number]: EmployeeSchedule
  }
}

interface TimeSlot {
  slot_number: number
  business_time: {
    start: string
    end: string
  }
  checkin_time?: {
    start: string
    end: string
  }
  ticket_issue_time?: {
    start: string
    end: string
  }
  ticket_limit?: number | null
}

interface DayData {
  display_date: string
  date: string
  day_of_week: number
  weekDay: string
  type_weekday: number // UI value (11-17)
  today: boolean
  business_hour_slot?: {
    id_business_hour_slot?: number
    type_business_day: number
    name_business_hour: string
    display_order?: number
    time_slots?: TimeSlot[]
  }
  employeeSchedules: EmployeeSchedules
  is_off_day: boolean
  employee_schedules?: Array<{
    id_employee: number
    name_display: string
    type_occupation: number
    flg_calendar: boolean
    id_employee_workschedule?: number
    schedules: Array<{
      time_workschedule_start: string
      time_workschedule_end: string
      flg_whole_dayoff: boolean
    }>
  }>
  slot_name?: string
  slot_type?: number
}

// Sort doctors by display order
const sortedDoctors = computed(() =>
  employees.value.filter((emp) => emp.flg_calendar).sort((a, b) => (a.display_order || 999) - (b.display_order || 999))
)

// Helper function to check if a day is Saturday (type_weekday = 16)
const isSaturday = (day: DayData) => {
  return day.type_weekday === 16
}

// Helper function to check if a day is Sunday (type_weekday = 17)
const isSunday = (day: DayData) => {
  return day.type_weekday === 17
}

// Fetch monthly schedule data
const fetchMonthlySchedule = async () => {
  if (!clinicId.value) return
  
  isLoading.value = true

  try {
    // Use date range from parent if available, otherwise use selectedMonth
    const fetchStartDate = startDateValue.value 
      ? dayjs(startDateValue.value).format('YYYY-MM-DD')
      : selectedMonth.value.format('YYYY-MM-DD')
    
    const fetchEndDate = endDateValue.value 
      ? dayjs(endDateValue.value).format('YYYY-MM-DD')
      : selectedMonth.value.endOf('month').format('YYYY-MM-DD')
    
    if(!dayjs(fetchStartDate).isValid() || !dayjs(fetchEndDate).isValid()) {
      return false
    }

    // Fetch scheduling data for the date range
    const response = await workScheduleStore.fetchSchedulingData({
      clinic_id: clinicId.value,
      period_type: 'monthly',
      start_date: fetchStartDate,
      end_date: fetchEndDate,
      booking_item_id: props.booking_item_id
    })

    // Set employees from API response
    if (response?.employees) {
      employees.value = response.employees
    }

    buildMonthlyData()
  } catch (error) {
    console.error('Error fetching monthly schedule:', error)
  } finally {
    isLoading.value = false
  }
}

// Build calendar days based only on API response data
const buildMonthlyData = () => {
  const monthlyDays = workScheduleStore.getMonthlySchedulingData()
  if (!Array.isArray(monthlyDays) || !monthlyDays.length) {
    return
  }

  // Create array of days to display based only on API response
  const allDays: DayData[] = []

  // Process only the days returned by the API
  monthlyDays.forEach((apiDay: any) => {
    if (!apiDay.date) return // Skip if no date in API response
    
    const dayObj = dayjs(apiDay.date)
    if (!dayObj.isValid()) return // Skip if invalid date
    
    // Use data directly from API response
    const businessHourSlot = apiDay.business_hour_slot || null
    const slotName = apiDay.slot_name || (businessHourSlot ? businessHourSlot.name_business_hour : null)
    const slotType = apiDay.slot_type || (businessHourSlot ? businessHourSlot.type_business_day : null)

    // Map employee availability data
    const employeeSchedules: EmployeeSchedules = {}
    if (apiDay.employee_schedules) {
      apiDay.employee_schedules.forEach((employeeSchedule: any) => {
        if (!employeeSchedules[employeeSchedule.id_employee]) {
          employeeSchedules[employeeSchedule.id_employee] = {}
        }

        // Map each schedule to a slot number
        employeeSchedule.schedules.forEach((schedule: any) => {
          // Figure out which time slot this schedule corresponds to
          const slotNumber = getSlotNumberForTime(
            schedule.time_workschedule_start,
            businessHourSlot?.time_slots
          )

          employeeSchedules[employeeSchedule.id_employee] = {
            checked: false,
            id_employee_workschedule: employeeSchedule.id_employee_workschedule || null,
            time_workschedule_start: schedule.time_workschedule_start,
            time_workschedule_end: schedule.time_workschedule_end,
            flg_whole_dayoff: schedule.flg_whole_dayoff,
            date_booking_special: apiDay.date,
            min_rest: schedule.min_rest
          }
        })
      })
    }

    // Create slots for all doctors and all potential time slots
    sortedDoctors.value.forEach((doctor) => {
      if (!employeeSchedules[doctor.id_employee]) {
        employeeSchedules[doctor.id_employee] = {
          checked: false,
          id_employee_workschedule: null,
          flg_whole_dayoff: false,
          date_booking_special: apiDay.date,
          min_rest: null
        }
      }
    })

    // Create day data object using API response data
    const displayDate = dayjs(apiDay.date).format('MM/DD (dd)')
    const weekDay = dayjs(apiDay.date).format('dd')
    allDays.push({
      display_date: displayDate,
      weekDay,
      date: apiDay.date,
      day_of_week: dayObj.day(), // 0-6 for Sunday-Saturday
      type_weekday: apiDay.type_weekday || (dayObj.day() === 0 ? 17 : dayObj.day() + 10), // 11-17 for UI display
      today: dayjs().isSame(dayObj, 'day'),
      business_hour_slot: businessHourSlot,
      employeeSchedules,
      is_off_day: businessHourSlot?.type_business_day === 90,
      employee_schedules: apiDay.employee_schedules || [],
      slot_name: slotName,
      slot_type: slotType,
    } as DayData)
  })

  monthlyData.value = allDays
}

// Helper function to match a time to a slot number
const getSlotNumberForTime = (time: string, timeSlots?: TimeSlot[]): number | null => {
  if (!timeSlots) return null

  for (let i = 0; i < timeSlots.length; i++) {
    if (timeSlots[i].business_time.start === time) {
      return i + 1
    }
  }
  return null
}

// Helper function to get business day type name
const typeBusinessDayName = (value: number) => typeBusinessDay.find((v) => v.value === value)

// Get total number of time slots for a day
const getTotalSlots = (timeSlots?: TimeSlot[]) => {
  return timeSlots?.length || 0
}

// Check if all slots for an employee on a specific day are marked as off
const isAllSlotChecked = (data: DayData, idEmployee: number | string) => {
  const employeeSchedule = data.employeeSchedules[idEmployee]
  if (!employeeSchedule) return false

  return employeeSchedule.checked
}

// Set all slots for an employee on a specific day to checked or unchecked
const setAllSlotChecked = (checked: boolean, data: DayData, idEmployee: number | string) => {
  const employeeSchedule = data.employeeSchedules[idEmployee]
  if (!employeeSchedule) return

  employeeSchedule.checked = checked
}

// Update all slots for an employee on a specific day
const updateAllSlotChecked = (day: DayData, employeeId: number | string, value: boolean) => {
  setAllSlotChecked(value, day, employeeId)
}

// Fetch clinic name
const fetchClinicName = async () => {
  if (clinicId.value) {
    try {
      const clinic = await clinicStore.fetchClinicById(clinicId.value)
      if (clinic) {
        clinicName.value = clinic.name_clinic_display
      }
    } catch (error) {
      console.error('Error fetching clinic name:', error)
      clinicName.value = ''
    }
  } else {
    clinicName.value = ''
  }
}

// Format time string to display format (removing :00 seconds)
const formatTimeDisplay = (time: string) => {
  return time ? time.replace(/:00$/, '') : ''
}

// Interface for schedule
interface EmployeeScheduleItem {
  id_employee_workschedule?: number;
  time_workschedule_start: string;
  time_workschedule_end: string;
  time_workschedule_start2?: string;
  time_workschedule_end2?: string;
  time_workschedule_start3?: string;
  time_workschedule_end3?: string;
  flg_whole_dayoff: boolean;
  type_weekday?: number;
}

// Add a new helper function to get the highest priority schedule
const getHighestPrioritySchedule = (employeeSchedules: any[], employeeId: number): EmployeeScheduleItem | null => {
  if (!employeeSchedules || !employeeSchedules.length) return null;
  
  // Filter schedules for the given employee
  const empSchedules = employeeSchedules.filter(emp => emp.id_employee === employeeId);
  if (!empSchedules.length || !empSchedules[0].schedules || !empSchedules[0].schedules.length) return null;
  
  const schedules = empSchedules[0].schedules;
  
  // First priority: Special schedules (type_weekday=99)
  const specialSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday === 99);
  if (specialSchedules.length) {
    return {
      ...specialSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule
    };
  }
  
  // Second priority: Regular schedules (type_weekday=11-18)
  const regularSchedules = schedules.filter((s: EmployeeScheduleItem) => s.type_weekday && s.type_weekday >= 11 && s.type_weekday <= 18);
  if (regularSchedules.length) {
    return {
      ...regularSchedules[0],
      id_employee_workschedule: empSchedules[0].id_employee_workschedule
    };
  }
  
  // If no matching schedules, return the first one
  return {
    ...schedules[0],
    id_employee_workschedule: empSchedules[0].id_employee_workschedule
  };
};

const getBorderClasses = (dateIndex, totalDates, borderTop = false) => {
  let str = 'border-bottom'
  if(dateIndex !== 1) str += ' border-left'
  if(dateIndex === totalDates) str += ' border-right'
  if(borderTop) str += ' border-top'
  return str
}

const getBulkData = () => {
  const bulkData = []
    
  const currentMonthData = monthlyData.value.slice()
    
  currentMonthData.forEach((day) => {

    Object.entries(day.employeeSchedules).forEach(([employeeId, data]) => {
      const hasCheckedEmployee = data.checked
      
      if (hasCheckedEmployee) {
        bulkData.push({ id_employee: parseInt(employeeId), ...data })
      }
    })
  })
  return bulkData
}

const showPresentAbsentLabel = (day: DayData, idEmployee: number) => {
  if(isEmployeeOff(day, idEmployee)) return true
  const schedule = getHighestPrioritySchedule(day.employee_schedules, idEmployee)
  if(!schedule) return false
  const { time_workschedule_start, time_workschedule_end } = schedule
  return time_workschedule_start !== '00:00:00' || time_workschedule_end !== '00:00:00'
}

const isEmployeeOff = (day: DayData, idEmployee: number) => {
  if (day.is_off_day || day.employeeSchedules[idEmployee].flg_whole_dayoff) return true
  return false
}

const updateEmployeeSlot = async (day: DayData, idEmployee: number) => {
  const employeeSlotData = day.employeeSchedules[idEmployee]
  if(!employeeSlotData) return false
  const bulkData = []
  bulkData.push({ ...employeeSlotData, id_employee: parseInt(idEmployee) })
  await mtUtils.smallPopup(UpdateBulkShiftModal, {
    bulkData
  })
  fetchMonthlySchedule()
}

const getMinRest = (day: DayData, idEmployee: number): string | false => {
  const employeeSlotData = day.employeeSchedules[idEmployee]
  const minRest = employeeSlotData?.min_rest

  return minRest != null ? minRest.toString() : false
}

const openEmployeeModal = async (idEmployee: Number) => {
  const employee = await mtUtils.callApi(selectOptions.reqMethod.GET, `/mst/employees/${idEmployee}`)
  if(employee) {
    mtUtils.popup(UpdateEmployeeModal, {
      data: employee,
      searchData: () => fetchMonthlySchedule()
    })
  }
}

// Initialize on component mount
onMounted(async () => {
  selectedMonthLabel.value = selectedMonth.value.format('YYYY年MM月')
  
  // Only fetch data if clinicId is available
  if (clinicId.value) {
    await mtUtils.promiseAllWithLoader([fetchClinicName(), fetchMonthlySchedule()])
  }
})

// Expose bulkUpdate method to parent
defineExpose({
  fetchMonthlySchedule,
  getBulkData
})
</script>

<template>
  <div>
    <div class="calendar-container q-px-md q-my-lg" v-if="!isLoading && monthlyData.length">
      <div 
        class="flex justify-end"
        @click="displaySlotsTime = !displaySlotsTime"
      >
        <small class="text-blue cursor-pointer q-mx-sm">{{ displaySlotsTime ? '非表示' : '時間枠の表示' }}</small>
      </div>
      <div class="calendar-header row items-start no-wrap">
        <div class="doctor-column text-center">
          <div 
            class="flex justify-center items-center q-ba-400 staff-heading"
            :class="displaySlotsTime ? 'times-displayed' : ''"
          >
            スタッフ
          </div>
          <div
            class="text-center border-left border-right border-bottom doc-name full-width cursor-pointer"
            v-for="(doctor, i) in sortedDoctors"
            :key="i"
            :style="{'height': editMode ? 'clamp(5vw, 7.5vw, 8vw)' : 'clamp(5vw, 5.5vw, 6vw)'}"
            @click="openEmployeeModal(doctor.id_employee)"
          >
            <div> 
              <q-chip 
                clickable 
                color="grey-300" 
                text-color="primary"
                class="vet-label"
              >
                獣医師
              </q-chip>
            </div>
            <div class="name">{{ doctor?.name_display }}</div>
          </div>
        </div>
        <div
          class="row items-center no-wrap month-row"
        >
          <div
            v-for="(day, dateIndex) in monthlyData"
            :key="dateIndex"
            class="col-1 text-center"
          >
            <div
              class="flex justify-center items-center date-col"
              :class="[getBorderClasses(dateIndex + 1, monthlyData.length, true), 
                day.is_off_day ? 'off-day' : '',
                isSaturday(day) ? 'sat' : ''
              ]">
                <span class="text-grey-900">{{dateIndex + 1}}</span>
                <span class="text-grey-600 q-ml-sm">{{ day.weekDay }}</span>
            </div>

            <div
              class="slot-col"
              :class="
                [getBorderClasses(dateIndex + 1, monthlyData.length), 
                day.is_off_day ? 'off-day' : '',
                displaySlotsTime ? 'times-displayed' : ''
              ]"
            >
              <div class="heading">
                {{ day.business_hour_slot?.name_business_hour }}
              </div>
              <template v-if="day.business_hour_slot?.time_slots && displaySlotsTime">
                <div class="flex items-center justify-center text-center slots">
                  <div 
                    v-for="(timeSlot, slotIdx) in day.business_hour_slot.time_slots" 
                    :key="slotIdx"
                    class="flex items-center gap-2"
                  >
                    枠{{ slotIdx + 1 }}
                    {{ formatTimeDisplay(timeSlot.business_time.start) }} ~
                    {{ formatTimeDisplay(timeSlot.business_time.end) }}
                  </div>
                </div>
              </template>
            </div>
            <template
              v-for="doctor in sortedDoctors"
              :key="doctor.id_employee"
            >
              <div 
                class="work-schedule-col flex column items-center text-center cursor-pointer"
                :class="[
                  getBorderClasses(dateIndex + 1, monthlyData.length), 
                  day.is_off_day ? 'off-day' : '',
                  (day.is_off_day || isEmployeeOff(day, doctor.id_employee)) ? 'day-off' : 'day-on'
                ]"
                @click="() => {
                  if(editMode) updateAllSlotChecked(day, doctor.id_employee, !!!isAllSlotChecked(day, doctor.id_employee))
                  else updateEmployeeSlot(day, doctor.id_employee)
                }"
                :style="{
                  'height': editMode ? 'clamp(5vw, 7.5vw, 8vw)' : 'clamp(5vw, 5.5vw, 6vw)', 
                  'padding-top': editMode ? 'clamp(.6vw, .8vw, 1vw)' : 'clamp(.75vw, .8vw, 1vw)'
                }"
              >
                <div class="flex items-center" v-if="editMode">
                  <MtFormCheckBox
                    type="checkbox"
                    label=""
                    :checked="isAllSlotChecked(day, doctor.id_employee)"
                    class="caption1 q-pa-none update-checkbox"
                    style="padding: 0; border: none"
                    @update:checked="(newVal) => updateAllSlotChecked(day, doctor.id_employee, newVal)"
                  />
                </div>
                <div
                  v-if="showPresentAbsentLabel(day, doctor.id_employee)"
                  square
                  class="availability-status"
                  style="margin-top: 0"
                  :class="isEmployeeOff(day, doctor.id_employee) ? 'absent' : 'present'"
                >
                  {{ isEmployeeOff(day, doctor.id_employee) ? '休み' : '出勤' }}
                </div>
                <template v-if="!day.is_off_day && !isEmployeeOff(day, doctor.id_employee)">
                  <div v-if="getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)" class="availability-time">
                    {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_start || '') }} ~
                    {{ formatTimeDisplay(getHighestPrioritySchedule(day.employee_schedules, doctor.id_employee)?.time_workschedule_end || '') }}
                  </div>
                </template>
                <small
                  class="break-time text-grey-600"
                  v-if="getMinRest(day, doctor.id_employee) && !isEmployeeOff(day, doctor.id_employee)"
                >
                  ({{ getMinRest(day, doctor.id_employee) }})
                </small>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.calendar-container {
  .calendar-header {
    .doctor-column {
      width: 10vw;
      .staff-heading {
        font-size: clamp(1vw, 1vw, 1.5vw);
        min-height: clamp(6vw, 6vw, 11vw);
        &.times-displayed {
          min-height: clamp(9vw, 9.75vw, 10vw);
        }
      }
      .doc-name {
        padding-top: clamp(.5vw, .5vw, 1vw);
        .name {
          font-size: clamp(1vw, 1vw, 1.5vw);
        }
      }
      .vet-label {
        margin-top: 0;
        padding: 0 0.9em;
        height: clamp(1vw, 1.5vw, 1.5vw);;
        font-size: clamp(1vw, 1vw, 1.5vw);
      }
    }
    .month-row {
      max-width: 86vw;
      width: 100%;
      overflow-x: scroll;
      .date-col {
        &.off-day {
          background-color: #FFF1F1 !important;
        }
        font-size: clamp(1vw, 1.5vw, 3vw);
        height: clamp(2vw, 2.75vw, 3vw);
        &.sat {
          & > span {
            color: $blue !important;
          }
        }
      }
      .slot-col {
        height: clamp(3vw, 3.25vw, 6vw);
        padding-top: clamp(.5vw, .75vw, 1vw);
        &.times-displayed {
          height: clamp(7vw, 7vw, 8vw);
        }
        &.off-day {
          background-color: #FFF1F1 !important;
        }
        .heading {
          font-size: clamp(1vw, 1vw, 1.5vw);
        }
        .slots {
          font-size: clamp(.75vw, .75vw, 1.25vw);
        }
      }
      .work-schedule-col {
        flex-wrap: nowrap;
        &.day-off {
          background-color: #ffeaeaff !important;
        }
        &.day-on {
          background-color: #d9fffcff !important;
        }
        .availability-status {
          font-size: clamp(.75vw, .8vw, 1vw);
          &.present {
            color: #096bbe;
          }
          &.absent {
            color: #990000;
          }
        }
        .availability-time {
          margin: 2px 0;
          font-size: clamp(.75vw, .8vw, 1vw);
        }
        .break-time {
          font-size: clamp(.6vw, .65vw, 1vw);
        }
          .update-checkbox {
            margin-bottom: 8px;
            :deep(.q-checkbox__inner) {
              min-width: clamp(1vw, 1.2vw, 1.5vw);
              width: clamp(1vw, 1.2vw, 1.5vw);
              height: clamp(1vw, 1.2vw, 1.5vw);
              .q-checkbox__bg {
                width: 100%;
                height: 100%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }
            @media screen and (max-width: 1024px) {
              margin-bottom: 4px;
            }
          }
      }
    }
  }
}

.border-left { border-left: 1px solid #bdbdbd; }
.border-right { border-right: 1px solid #bdbdbd; }
.border-top { border-top: 1px solid #bdbdbd; }
.border-bottom { border-bottom: 1px solid #bdbdbd; }
</style>
