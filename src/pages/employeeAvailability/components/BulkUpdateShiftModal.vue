<script setup lang="ts">
import { computed, onMounted, ref, defineProps, defineEmits, watch } from 'vue'
import mtUtils from '@/utils/mtUtils'
// @ts-ignore - Import with ignoring TypeScript errors
import OptionModal from '@/components/OptionModal.vue'
// @ts-ignore - Import with ignoring TypeScript errors
import MtModalHeader from '@/components/MtModalHeader.vue'
import { Platform } from 'quasar'
import useBookingItemStore from '@/stores/booking-items'
import useEmployeeStore from '@/stores/employees'
import useWorkScheduleStore from '@/stores/work-schedules'
import { storeToRefs } from 'pinia'
// @ts-ignore - Import with ignoring TypeScript errors due to script setup component
import MInputTime from '@/components/form/MInputTime.vue'

const props = defineProps({
  bulkUpdateData: {
    type: Array,
    required: true
  },
  clinicId: {
    type: Number,
    required: true
  },
  onSuccess: {
    type: Function,
    default: () => {}
  },
  displayMode: {
    type: String,
    default: 'slot',
    validator: (value: string) => ['slot', 'day'].includes(value)
  },
  workScheduleId: {
    type: Number,
    default: null
  }
})

const emits = defineEmits(['close'])

const isLoading = ref(false)
const employees = ref<any[]>([])
const selectedEmployees = ref<number[]>([])
const timeStart = ref<string>('')
const timeEnd = ref<string>('')
const minRest = ref<number>(0)
const scheduleIdsToDelete = ref<number[]>([])

// Add bulkUpdateData ref
const bulkUpdateData = ref<any[]>([])

// Initialize stores
const bookingItemStore = useBookingItemStore()
const employeeStore = useEmployeeStore()
const workScheduleStore = useWorkScheduleStore()

// Computed property to check if on iPad
const isIpad = computed(() => {
  return Platform.is.ipad
})

// Close the modal
const closeModal = () => {
  emits('close')
}

// Process unique employees from bulk update data
const processEmployees = () => {
  if (props.bulkUpdateData && props.bulkUpdateData.length > 0) {
    // Extract unique employee IDs from bulk update data
    const uniqueEmployeeIds = [...new Set(props.bulkUpdateData.map((item: any) => item.id_employee))]
    selectedEmployees.value = uniqueEmployeeIds

    // Extract schedule IDs if they exist in the bulk update data
    const scheduleIds = props.bulkUpdateData
      .filter((item: any) => item.id_employee_workschedule)
      .map((item: any) => item.id_employee_workschedule)
      .filter(Boolean) // Remove any undefined or null values
    
    scheduleIdsToDelete.value = scheduleIds
    
    // Debug: Log extracted IDs
    console.log('Unique employee IDs:', uniqueEmployeeIds);
    console.log('Schedule IDs to delete:', scheduleIds);
  }
}

// Select/deselect employee handler
const selectingEmployee = (val: any) => {
  selectedEmployees.value = val
}

// Load employees data
const loadEmployees = async () => {
  try {
    await employeeStore.fetchEmployees({ id_clinic: props.clinicId })
    employees.value = employeeStore.getEmployees
      .filter((emp) => emp.flg_calendar)
      .map((emp) => ({
        label: emp.name_display,
        value: emp.id_employee
      }))

    processEmployees()
  } catch (error) {
    console.error('Failed to load employees:', error)
  }
}

// Load work schedule details if ID is provided
const loadWorkScheduleDetails = async () => {
  if (props.workScheduleId) {
    try {
      const details = await workScheduleStore.fetchWorkScheduleById(props.workScheduleId)
      if (details) {
        // Update form with work schedule details
        selectedEmployees.value = [details.id_employee]
        timeStart.value = details.time_workschedule_start
        timeEnd.value = details.time_workschedule_end
        minRest.value = details.min_rest

        // Add the workScheduleId to scheduleIdsToDelete to enable delete functionality
        scheduleIdsToDelete.value = [details.id_employee_workschedule]

        // Store the original schedule details for submission
        bulkUpdateData.value = [{
          id_employee_workschedule: details.id_employee_workschedule,
          id_employee: details.id_employee,
          type_weekday: details.type_weekday,
          date_booking_special: details.date_booking_special,
          time_workschedule_start: details.time_workschedule_start,
          time_workschedule_end: details.time_workschedule_end,
          flg_whole_dayoff: details.flg_whole_dayoff,
          min_rest: details.min_rest
        }]
      }
    } catch (error) {
      console.error('Failed to load work schedule details:', error)
    }
  }
}

// Submit the form and save changes
const submitForm = async () => {
  isLoading.value = true

  try {
    if (selectedEmployees.value.length > 0) {
      // Prepare employee work schedules
      const employeeList = selectedEmployees.value.map((employeeId) => {
        let employeeWorkscheduleList = []

        if (props.workScheduleId && bulkUpdateData.value.length > 0) {
          // If editing existing schedule, use the original data with updated values
          const schedule = bulkUpdateData.value[0] as {
            id_employee_workschedule: number;
            type_weekday: number;
            date_booking_special: string | null;
          }
          employeeWorkscheduleList = [{
            id_employee_workschedule: schedule.id_employee_workschedule,
            type_weekday: schedule.type_weekday,
            date_booking_special: schedule.date_booking_special,
            time_workschedule_start: timeStart.value,
            time_workschedule_end: timeEnd.value,
            flg_whole_dayoff: props.displayMode === 'day',
            min_rest: minRest.value
          }]
        } else {
          // For bulk update mode, create or update schedules based on the data
          // Filter data for current employee
          const employeeData = props.bulkUpdateData.filter((item: any) => item.id_employee === employeeId)
          
          employeeWorkscheduleList = employeeData.map((data: any) => ({
            id_employee_workschedule: data.id_employee_workschedule,
            type_weekday: data.type_weekday,
            time_workschedule_start: timeStart.value || data.time_workschedule_start,
            time_workschedule_end: timeEnd.value || data.time_workschedule_end,
            flg_whole_dayoff: props.displayMode === 'day',
            date_booking_special: data.date_booking_special || null,
            min_rest: minRest.value
          }))
        }

        return {
          id_employee: employeeId,
          employee_workschedule_list: employeeWorkscheduleList
        }
      })

      await workScheduleStore.createOrUpdateWorkSchedules({
        id_clinic: props.clinicId,
        employee_list: employeeList
      })

      // Show different message based on the operation type
      const message = props.displayMode === 'day' ? '休みを適用しました！' : 'シフトを更新しました！'
      mtUtils.autoCloseAlert(message)
      props.onSuccess()
      closeModal()
    } else {
      mtUtils.autoCloseAlert('スタッフを選択してください。')
    }
  } catch (error) {
    console.error('Failed to update schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの更新に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Delete work schedules
const deleteWorkSchedules = async () => {
  if (scheduleIdsToDelete.value.length === 0) {
    mtUtils.autoCloseAlert('削除するシフトがありません。')
    return
  }

  try {
    isLoading.value = true

    const confirmed = await mtUtils.confirm2({
      message: '選択したシフトを削除しますか？',
      title: '確認',
      okLabel: '削除',
      cancelBtn: true,
      cancelBtnLabel: 'キャンセル'
    })

    if (!confirmed) {
      isLoading.value = false
      return
    }

    // Delete work schedules
    await workScheduleStore.deleteWorkSchedules(scheduleIdsToDelete.value)

    // Call the success callback
    props.onSuccess()

    // Close the modal
    closeModal()
  } catch (error) {
    console.error('Failed to delete schedules:', error)
    mtUtils.autoCloseAlert('スケジュールの削除に失敗しました。')
  } finally {
    isLoading.value = false
  }
}

// Open options menu with more actions
const openMenu = async () => {
  const menuOptions = [
    {
      title: 'URLコピー',
      name: 'url_copy',
      isChanged: false,
      attr: { class: null, clickable: true }
    },
    {
      title: '削除',
      name: 'delete',
      isChanged: false,
      attr: { class: null, clickable: scheduleIdsToDelete.value.length > 0 }
    },
    {
      title: '保存せずに閉じる',
      name: 'close',
      isChanged: false,
      attr: { class: null, clickable: true }
    }
  ]

  await mtUtils.littlePopup(OptionModal, {
    options: menuOptions
  })

  const selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'url_copy') {
      const url = window.location.href
      await navigator.clipboard.writeText(url)
      mtUtils.autoCloseAlert('URLをコピーしました。')
    } else if (selectedOption.name == 'delete') {
      deleteWorkSchedules()
    } else if (selectedOption.name == 'close') {
      closeModal()
    }
  }
}

// Load initial data
onMounted(async () => {
  await loadEmployees()
  await loadWorkScheduleDetails()
})

const errorMessage = ref('')
const hourOptions = [5, 6, 7, 8, 9]

// Function to add hours to start time
const addHoursToStartTime = (hours: number) => {
  try {
    if (!timeStart.value) {
      errorMessage.value = 'Please set start time before choosing hours'
      return
    }

    // Parse the time string (assuming format HH:mm)
    const [startHour, startMinute] = timeStart.value.split(':').map(Number)
    if (isNaN(startHour) || isNaN(startMinute)) {
      errorMessage.value = 'Invalid time format in start time'
      return
    }

    // Calculate new hour
    let newHour = startHour + hours
    
    // Handle overflow (24-hour format)
    if (newHour >= 24) {
      newHour = newHour - 24
    }

    // Format the new time (zero-padding hour)
    timeEnd.value = `${String(newHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}`
    errorMessage.value = '' // Clear any previous error
  } catch (error) {
    console.error('Error calculating end time:', error)
    errorMessage.value = `An error occurred while calculating the time: ${error}`
  }
}

// Rest minute options
const restMinuteOptions = [15, 30, 45, 60]
</script>

<template>
  <q-form @submit.prevent="submitForm">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold"> シフト更新 </q-toolbar-title>

      <q-btn round flat @click="openMenu" class="q-mx-sm">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
    </MtModalHeader>

    <q-card-section class="q-mt-md q-px-xl content" :class="{ 'q-mt-md': isIpad }">
      <div class="row q-col-gutter-md">
        <!-- Employee selection -->
        <div class="col-12 q-mt-md">
          <div class="row">
            <div class="col-12">
              <q-select
                v-model="selectedEmployees"
                :options="employees"
                :readonly="true"
                clearable
                dense
                label="myVetty指名表示スタッフ"
                multiple
                use-chips
                emit-value
                map-options
                @update:model-value="selectingEmployee"
              />
            </div>
          </div>
        </div>

        <!-- Time settings -->
        <div class="col-12 q-mt-md">
          <div class="row q-col-gutter-md">
            <div class="col-4">
              <MInputTime v-model="timeStart" label="開始時間" :outlined="false" />
            </div>
            <div class="col-4">
              <MInputTime v-model="timeEnd" label="終了時間" :outlined="false" />
              <!-- Add hour selection buttons -->
              <div class="text-grey-500 text-caption">
                <span 
                  v-for="hours in hourOptions" 
                  :key="hours"
                  class="cursor-pointer hover-primary"
                  @click="addHoursToStartTime(hours)"
                >
                  +{{ hours }}h{{ hours < hourOptions[hourOptions.length - 1] ? ', ' : '' }}
                </span>
              </div>
              <!-- Error message display -->
              <div v-if="errorMessage" class="text-negative text-caption q-mt-sm">
                {{ errorMessage }}
              </div>
            </div>
            <div class="col-4">
              <q-input
                v-model="minRest"
                type="number"
                label="休憩時間 (分)"
                dense
                min="0"
                max="60"
              />
              <div class="text-grey-500 text-caption q-mt-sm">
                推奨時間: 
                <span 
                  v-for="(time, index) in restMinuteOptions" 
                  :key="time"
                  class="cursor-pointer hover-primary"
                  @click="minRest = time"
                >
                  {{ time }}{{ index < restMinuteOptions.length - 1 ? ', ' : '' }}
                </span>
                分
              </div>
            </div>
          </div>
        </div>

        <!-- Display order input -->
        <!-- <div class="col-12 q-mt-md">
          <div class="row">
            <div class="col-12">
              <q-input style="width: 20%" v-model="displayOrder" type="number" label="表示順序" dense />
              <span class="text-grey-500 text-caption regular"
                >0～999までの整数を入力してください。数値が小さいほど一覧で上位に表示します。</span
              >
            </div>
          </div>
        </div> -->
      </div>
    </q-card-section>

    <!-- footer -->
    <q-card-section class="bg-white q-bt">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn unelevated color="primary" class="q-ml-md" type="submit" :loading="isLoading">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.underline-input {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0;
}

.hover-primary {
  &:hover {
    color: var(--q-primary);
  }
}
</style>
