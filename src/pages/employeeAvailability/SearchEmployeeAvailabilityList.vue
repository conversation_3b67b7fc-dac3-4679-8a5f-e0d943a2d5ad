<script lang="ts">
  enum SHIFT {
    MONTHLY = 'MONTHLY',
    WEEKLY = 'WEEKLY',
  }
</script>
<script setup lang="ts">
import { ref, defineAsyncComponent, provide, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import useClinicStore from '@/stores/clinics'
import useWorkScheduleStore from '@/stores/work-schedules'
import dayjs from '@/boot/dayjs'
// @ts-ignore
import MtHeader from '@/components/layouts/MtHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
// @ts-ignore
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import mtUtils from '@/utils/mtUtils'
// Import the components directly for type information
import GetPdfWorkSchedule from './components/GetPdfWorkSchedule.vue'
import { debounce } from 'lodash'

// Import components asynchronously to avoid import errors
const ViewMonthlyShift = defineAsyncComponent(() => import('./components/ViewMonthlyShift.vue'))
const ViewWeeklyShift = defineAsyncComponent(() => import('./components/ViewWeeklyShift.vue'))
const BulkUpdateShiftModal = defineAsyncComponent(() => import('./components/BulkUpdateShiftModal.vue'))
const UpdateBulkShiftModal = defineAsyncComponent(() => import('./components/UpdateBulkShiftModal.vue'))

const route = useRoute()
const router = useRouter()

const shiftMode = ref<Shift>(SHIFT.MONTHLY)
const clinicId = ref<number | null>(null) // Default clinic ID
const clinicStore = useClinicStore()
const workScheduleStore = useWorkScheduleStore()
const clinics = ref<any[]>([])
const selectedClinic = ref<any>(null)
const clinicDetail = ref<any>(null)
const showFullHeader = ref(window.innerWidth >= 1200)
const showScrollTopBtn = ref(false)

// PDF download form data
const startDate = ref('')
const endDate = ref('')
const isGeneratingPdf = ref(false)

// Edit mode state
const editMode = ref(false)
const displayMode = ref<'slot' | 'day'>('slot')
const bulkUpdateData = ref<any[]>([])

// Toggle functions to switch views
const showWeekly = () => {
  shiftMode.value = SHIFT.WEEKLY
  updateUrlParams()
}

const showMonthly = () => {
  shiftMode.value = SHIFT.MONTHLY
  updateUrlParams()
}

// Update URL parameters for sharing
const updateUrlParams = () => {
  const query: any = {}
  
  if (shiftMode.value === SHIFT.MONTHLY) {
    query.view = 'month'
    if (startDate.value && endDate.value) {
      const startFormatted = dayjs(startDate.value).format('YYYYMMDD')
      const endFormatted = dayjs(endDate.value).format('YYYYMMDD')
      query.date_range = `${startFormatted}_${endFormatted}`
    }
  } else {
    query.view = 'master_week'
  }
  
  router.replace({ query })
}

// Parse URL parameters on load
const parseUrlParams = () => {
  const { view, date_range } = route.query
  
  if (view === 'month') {
    shiftMode.value = SHIFT.MONTHLY
    
    if (date_range && typeof date_range === 'string') {
      const [startDateStr, endDateStr] = date_range.split('_')
      if (startDateStr && endDateStr) {
        const parsedStartDate = dayjs(startDateStr, 'YYYYMMDD')
        const parsedEndDate = dayjs(endDateStr, 'YYYYMMDD')
        
        if (parsedStartDate.isValid() && parsedEndDate.isValid()) {
          startDate.value = parsedStartDate.format('YYYY-MM-DD')
          endDate.value = parsedEndDate.format('YYYY-MM-DD')
        }
      }
    }
  } else if (view === 'master_week') {
    shiftMode.value = SHIFT.WEEKLY
  }
}

// Function to set default date range based on clinic's date_start_cycle
const setDefaultDateRange = () => {
  if (!clinicDetail.value) return
  
  const today = dayjs()
  const cycleDay = clinicDetail.value.date_start_cycle || 1
  
  let cycleStartDate = today.date() >= cycleDay 
    ? today.date(cycleDay)
    : today.subtract(1, 'month').date(cycleDay)
    
  startDate.value = cycleStartDate.format('YYYY-MM-DD')
  endDate.value = cycleStartDate.add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD')
}

// Load clinics and set default
const fetchClinics = async () => {
  try {
    // await clinicStore.fetchClinics()
    clinics.value = clinicStore.getClinics
    
    // Get clinic ID from localStorage or use first clinic
    const storedClinicId = localStorage.getItem('id_clinic')
    if (storedClinicId) {
      clinicId.value = JSON.parse(storedClinicId)
      selectedClinic.value = clinics.value.find(clinic => clinic.id_clinic === clinicId.value) || null
      
      // Fetch clinic detail and set default date range
      if (clinicId.value) {
        const detail = await clinicStore.fetchClinicById(clinicId.value)
        clinicDetail.value = detail
        setDefaultDateRange()
      }
    } else if (clinics.value.length > 0) {
      selectedClinic.value = clinics.value[0]
      clinicId.value = selectedClinic.value.id_clinic
      
      // Fetch clinic detail and set default date range for first clinic
      const detail = await clinicStore.fetchClinicById(selectedClinic.value.id_clinic)
      clinicDetail.value = detail
      setDefaultDateRange()
    }
  } catch (error) {
    console.error('Error fetching clinics:', error)
  }
}

// Handle clinic change
const onClinicChange = async (clinic: any) => {
  if (clinic) {
    clinicId.value = clinic
    localStorage.setItem('id_clinic', JSON.stringify(clinic))
    
    // Fetch new clinic detail and update date range
    const detail = await clinicStore.fetchClinicById(clinic)
    clinicDetail.value = detail
    setDefaultDateRange()
  }
}

// Download PDF function
const generatePdf = async () => {
  if (!clinicId.value) return
  
  isGeneratingPdf.value = true
  
  try {
    // Fetch the PDF data
    const pdfData = await workScheduleStore.fetchWorkSchedulePdfData({
      id_clinic: clinicId.value,
      start_date: startDate.value,
      end_date: endDate.value
    })
    
    if (pdfData) {
      // Generate title for the PDF based on date range
      const monthTitle = dayjs(startDate.value).format('YYYY年MM月')
      
      // Render the PDF using mtUtils popup with the GetPdfWorkSchedule component
      await mtUtils.pdfRender(GetPdfWorkSchedule, {
        data: pdfData,
        monthTitle,
        flgDownloadPdf: true
      })
    }
  } catch (error) {
    console.error('Error generating PDF:', error)
  } finally {
    isGeneratingPdf.value = false
  }
}

// Function wrappers for event handlers
const downloadWorkSchedulePdf = () => {
  generatePdf()
}

// Computed property for month label
const selectedMonthLabel = computed(() => {
  const start = dayjs(startDate.value)
  const end = dayjs(endDate.value)
  const today = dayjs()

  let monthLabel = ''
  if (start.isSame(today, 'month')) {
    monthLabel = '今月'
  } else {
    monthLabel = start.format('MMM')
  }

  // Show the date range as well
  const rangeLabel = `${start.format('YYYY/MM/DD')} - ${end.format('YYYY/MM/DD')}`

  return `${monthLabel} (${rangeLabel})`
})

// Navigation functions for month selection
const prevMonth = () => {
  const start = dayjs(startDate.value)
  const end = dayjs(endDate.value)
  
  startDate.value = start.subtract(1, 'month').format('YYYY-MM-DD')
  endDate.value = end.subtract(1, 'month').format('YYYY-MM-DD')
}

const nextMonth = () => {
  const start = dayjs(startDate.value)
  const end = dayjs(endDate.value)
  
  startDate.value = start.add(1, 'month').format('YYYY-MM-DD')
  endDate.value = end.add(1, 'month').format('YYYY-MM-DD')
}

// Computed property for PDF button validation
const isPdfFormValid = computed(() => {
  return clinicId.value && startDate.value && endDate.value && 
         dayjs(startDate.value).isValid() && 
         dayjs(endDate.value).isValid() &&
         dayjs(endDate.value).isAfter(dayjs(startDate.value))
})

// Computed property for component key
const componentKey = computed(() => {
  return clinicId.value ? clinicId.value.toString() : 'no-clinic'
})

// Toggle edit mode functions
const toggleEditMode = (mode?: 'day' | 'slot') => {
  editMode.value = !editMode.value
}

// Cancel edit mode
const cancelEdit = () => {
  editMode.value = false
  // Reset data in child components by calling the refreshData event
  refreshData()
}

// References to child components
const weeklyShiftRef = ref(null)
const monthlyShiftRef = ref(null)

// Bulk update function
const bulkUpdate = async () => {
  try {
    // For day mode, delegate to the child component
    if (shiftMode.value === SHIFT.WEEKLY && weeklyShiftRef.value) {
      await weeklyShiftRef.value.bulkUpdate();
    } else if (shiftMode.value === SHIFT.MONTHLY && monthlyShiftRef.value) {
      const bulkData = monthlyShiftRef.value.getBulkData()
      if (!bulkData.length) {
        mtUtils.alert('更新する予定がありません')
        return
      }
      await mtUtils.smallPopup(UpdateBulkShiftModal, {
        bulkData
      })
      refreshData()
    } else {
      console.error('Component reference not available')
      mtUtils.alert('操作を完了できませんでした。ページを再読み込みしてください。')
    }
    
    // Set edit mode to false after completion
    editMode.value = false
    return;

    // For slot mode, show the popup directly
    
    // Get data from the appropriate child component
    let bulkUpdateData = []
    if (shiftMode.value === SHIFT.WEEKLY && weeklyShiftRef.value) {
      // Call the weekly component's method to prepare data
      bulkUpdateData = await weeklyShiftRef.value.prepareBulkUpdateData();
    } else if (shiftMode.value === SHIFT.MONTHLY && monthlyShiftRef.value) {
      // Call the monthly component's method to prepare data
      bulkUpdateData = await monthlyShiftRef.value.prepareBulkUpdateData();
    } else {
      console.error('Component reference not available')
      mtUtils.alert('操作を完了できませんでした。ページを再読み込みしてください。')
      return;
    }
    
    // Check if we have data to show
    if (!bulkUpdateData.length) {
      mtUtils.alert('更新する予定がありません')
      return
    }
    
    // Show the modal using mtUtils
    await mtUtils.smallPopup(BulkUpdateShiftModal, {
      bulkUpdateData: bulkUpdateData,
      clinicId: clinicId.value,
      displayMode: displayMode.value,
      onSuccess: async () => {
        // Refresh data
        refreshData()
        // Set edit mode to false after successful operation
        editMode.value = false
      }
    })
  } catch (error) {
    console.error('Error in bulkUpdate:', error)
    mtUtils.alert('エラーが発生しました。もう一度お試しください。')
  }
}

// Function to refresh data in child components
const refreshData = () => {
  if (shiftMode.value === SHIFT.WEEKLY) {
    weeklyShiftRef.value?.fetchWeeklySchedule()
  } else {
    monthlyShiftRef.value?.fetchMonthlySchedule()
  }
}

// Watch for clinicId changes to notify child components
watch(clinicId, (newValue) => {
  if (newValue) {
    // Trigger refetch in child components via provide/inject
    provide('clinicId', newValue)
  }
})

// Provide edit mode and display mode to child components
provide('clinicId', clinicId)
provide('startDate', startDate)
provide('endDate', endDate)
provide('editMode', editMode)
provide('displayMode', displayMode)

// Watch for date changes to update URL
watch([startDate, endDate], () => {
  if (shiftMode.value === SHIFT.MONTHLY) {
    updateUrlParams()
  }
})

const updateHeaderLayout =  debounce(() => {
  showFullHeader.value = window.innerWidth >= 1200
}, 300)

const handleScroll =  debounce(() => {
  showScrollTopBtn.value = window.scrollY > 40
})

const scrollToBodyTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

onMounted(() => {
  parseUrlParams()
  fetchClinics()

  window.addEventListener('resize', updateHeaderLayout)
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateHeaderLayout)
  window.addEventListener('scroll', handleScroll)
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900 mobile-margin flex items-center justify-between">
          <!-- {{ showWeeklyShift ? 'シフトマスタ' : '月間シフト' }} -->
          <!-- Only show the opposite view option -->
          <div class="flex items-center gap-4">
            シフト設定
            <div class="flex justify-between items-center gap-4">
              <q-select
                v-model="selectedClinic"
                :options="clinics"
                option-label="name_clinic_display"
                option-value="id_clinic"
                emit-value
                map-options
                dense
                outlined
                class="clinic-selector"
                :style="{'min-width': showFullHeader ? '180px' : '130px'}"
                @update:model-value="onClinicChange"
              />
              <q-btn
                @click="prevMonth()"
                padding="2px"
                flat
                unelevated
                icon="chevron_left"
                style="border: 1px solid #9e9e9e"
                :size="showFullHeader ? 'md' : 'xs'"
              />
              <div class="row no-wrap items-center">
                <MtInputForm
                  v-model="selectedMonthLabel"
                  type="text"
                  class="col-grow"
                  :style="{ 'width': showFullHeader ? '200px' : '120px' }"
                  :inputStyle="{'font-size': showFullHeader ? '14px' : '10px'}"
                  borderless
                  readonly
                />
              </div>
              <q-btn
                @click="nextMonth()"
                padding="2px"
                flat
                unelevated
                icon="chevron_right"
                style="border: 1px solid #9e9e9e"
                :size="showFullHeader ? 'md' : 'xs' "
            />
          </div>
          <div>
            <q-btn-toggle
              class="toggle-btn"
              :size="showFullHeader ? 'md' : 'xs'"
              unelevated
              v-model="shiftMode"
              :options="[
                {label: '月間変動', value: SHIFT.MONTHLY},
                {label: '週間固定', value: SHIFT.WEEKLY},
              ]"
            />
          </div>
        </div>

          <!-- Edit mode buttons and controls -->
          <div class="row items-center">
            <div v-if="!editMode" class="flex items-center">
              <q-btn
                label="一括更新"
                @click="toggleEditMode('slot')"
                padding="4px 20px"
                flat
                unelevated
                class="bg-grey-100 q-mr-sm"
                style="border: 1px solid #9e9e9e"
                :size="showFullHeader ? 'md' : 'xs'"
              />
            </div>
            <div v-else class="flex items-center">
              <div class="caption2" v-if="showFullHeader">
                更新する日にチェックを入れてください
              </div>
              <q-btn 
                unelevated 
                color="primary" 
                class="q-ml-md"
                type="button" 
                @click="bulkUpdate"
                :size="showFullHeader ? 'md' : 'xs'"
              >
                <span>決定</span>
              </q-btn>
              <q-btn 
                outline 
                class="bg-grey-100 text-grey-800 q-ml-sm" 
                @click="cancelEdit"
                :size="showFullHeader ? 'md' : 'xs'"
              >
                <span>キャンセル</span>
              </q-btn>
            </div>
          </div>
        </q-toolbar-title>
        <div class="row" v-if="!editMode">
          <div class="col-12">
            <div class="flex items-center">
              
              <!-- PDF Export Form -->
              <div 
                class="pdf-export-form flex items-center"
                :class="showFullHeader ? 'q-ml-md' : 'q-ml-sm'"
              >
                <div>
                  <q-btn
                    color="primary"
                    :loading="isGeneratingPdf"
                    :disable="!isPdfFormValid"
                    @click="downloadWorkSchedulePdf"
                    :size="showFullHeader ? 'md' : 'xs'"
                  >
                    <q-icon name="file_download" size="md" v-if="showFullHeader" />
                    {{ showFullHeader ? 'PDFダウンロード' : 'PDF' }}
                  </q-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>

    <div class="q-mt-md">
      <div v-if="shiftMode === SHIFT.MONTHLY">
        <ViewMonthlyShift
          :clinic-id="clinicId"
          :start-date="startDate"
          :end-date="endDate"
          :edit-mode="editMode"
          :key="'monthly-' + componentKey"
          ref="monthlyShiftRef"
        />
      </div>
      <div v-else>
        <ViewWeeklyShift
          :clinic-id="clinicId"
          :start-date="startDate"
          :end-date="endDate"
          :edit-mode="editMode"
          :display-mode="displayMode"
          :key="'weekly-' + componentKey"
          ref="weeklyShiftRef"
        />
      </div>
    </div>
    <q-icon 
      name="arrow_circle_up" 
      v-if="showScrollTopBtn" 
      @click="scrollToBodyTop" 
      class="scroll-top-btn" 
      size="30px" 
    />
  </q-page>
</template>

<style lang="scss" scoped>
.toggle-btn {
  border: 1px solid #D0D7DE;
  border-radius: 6px;
  :deep(.q-btn) {
    background: rgb(245 245 245) !important;
    &[aria-pressed="true"] {
      background: #fff !important;
      color: #0057FF !important;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }
}
.clinic-selector {
  :deep(.q-field__control) {
    height: 32px;
  }
}

.date-input {
  :deep(.q-field__control) {
    height: 32px;
  }
}

.pdf-export-form {
  
  .row.no-wrap.items-center.q-mx-xs {
    border: 1px solid #9e9e9e;
    border-radius: 4px;
    padding: 0px 8px;
  }
  
  :deep(.q-field__native) {
    padding-right: 0;
  }
  
  :deep(.q-field__control) {
    min-height: unset;
  }
}

.scroll-top-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  background: $primary;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: opacity 0.3s;
}
</style>
