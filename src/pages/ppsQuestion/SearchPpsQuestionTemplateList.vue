<script setup lang="ts">
import { defineAsyncComponent, onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'

// Import components properly
import * as MtHeaderComponent from '@/components/layouts/MtHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import * as MtFormInputDateComponent from '@/components/form/MtFormInputDate.vue'
import * as MtTable2Component from '@/components/MtTable2.vue'

// Extract components from imports
const MtHeader =
  'default' in MtHeaderComponent ? MtHeaderComponent.default : MtHeaderComponent
const MtFormInputDate =
  'default' in MtFormInputDateComponent
    ? MtFormInputDateComponent.default
    : MtFormInputDateComponent
const MtTable2 =
  'default' in MtTable2Component ? MtTable2Component.default : MtTable2Component

import { Loading } from 'quasar'
import { debounce } from 'lodash'
import dayjs from 'dayjs'
import mtUtils from '@/utils/mtUtils'
// Import the PPS question template store
import usePPSQuestionTemplateStore from '@/stores/pps-question-template'
import MtSpinnerLoading from '@/components/MtSpinnerLoading.vue'

const UpdatePpsQuestionTemplateModal = defineAsyncComponent({
  loader: () => import('./UpdatePpsQuestionTemplateModal.vue'),
  loadingComponent: MtSpinnerLoading,
  delay: 200,
  timeout: 10000
})

const CreateUpdatePpsQuestionModal = defineAsyncComponent({
  loader: () => import('./CreateUpdatePpsQuestionModal.vue'),
  loadingComponent: MtSpinnerLoading,
  delay: 200,
  timeout: 10000
})

/* ---------------------- Variables ---------------------- */
const ppsStore = usePPSQuestionTemplateStore()
const { pps_question_templates, total_templates_count } = storeToRefs(ppsStore)

// @ts-ignore - Using any[] to avoid type conflicts with MtTable2
const columns: any[] = [
  {
    name: 'name_button',
    label: 'PJ名',
    field: 'name_button',
    align: 'left',
    style: 'width: 25%'
  },
  {
    name: 'id_disease_relate',
    label: '診療科',
    field: (row: any) => {
      // Check if disease_relate object exists first
      if (row.disease_relate) {
        return row.disease_relate.name_disease || ''
      }
      // Fallback to id_disease_relate if it's an object (old structure)
      else if (
        row.id_disease_relate &&
        typeof row.id_disease_relate === 'object'
      ) {
        return row.id_disease_relate.name_disease || ''
      }
      // If neither available, return empty string
      return ''
    },
    align: 'left',
    style: 'width: 25%'
  },
  {
    name: 'questions_count',
    label: '質問数',
    field: 'questions_count',
    align: 'left',
    style: 'width: 10%'
  },
  {
    name: 'datetime_insert',
    label: '登録日',
    field: 'datetime_insert',
    align: 'left',
    style: 'width: 20%'
  },
  {
    name: 'modal_template_detail',
    label: ' ',
    field: 'modal_template_detail',
    align: 'left',
    style: 'width: 20%'
  }
]

const searchData = ref({
  template_name: '',
  id_disease: '',
  question_count: '',
  date_start: dayjs().subtract(30, 'day').format('YYYY/MM/DD'),
  date_end: dayjs().format('YYYY/MM/DD')
})

const pagination = ref({
  page: 1,
  page_size: 10
})

let loading = ref(false)

/* ---------------------- Functions ---------------------- */

const openCreateUpdatePpsQuestionModal = async (row?: any) => {
  try {
    // If row is provided, open for editing
    if (row) {
      // Set the selected template first
      ppsStore.setSelectedTemplate(row)

      // Open the modal with the template ID
      await mtUtils.fullHeightPopup(CreateUpdatePpsQuestionModal, {
        id_pps_qs_template: row.id_pps_qs_template,
        templateName: row.name_button,
        callBackRefresh: search
      })
    } else {
      // Open for creating a new template
      await mtUtils.fullHeightPopup(CreateUpdatePpsQuestionModal, {
        callBackRefresh: search
      })
    }
  } catch (error) {
    console.error('Error opening question template editor:', error)
  }
}

// modal template detail
const openModalTemplateDetail = async (row: any, event?: Event) => {
  // Stop propagation if event exists to prevent row click from also triggering
  if (event) {
    event.stopPropagation()
  }

  try {
    // Set the selected template first
    ppsStore.setSelectedTemplate(row)

    // Open the modal with template ID
    await mtUtils.smallPopup(UpdatePpsQuestionTemplateModal, {
      id_pps_qs_template: row.id_pps_qs_template,
      callBackRefresh: search
    })
  } catch (error) {
    console.error('Error opening template detail:', error)
  }
}

// Comments out the openQuestionManager functionality for now as it doesn't exist
// const openQuestionManager = async (row: any) => {
//   await mtUtils.mediumPopup(QuestionManager, {
//     template_id: row.id_pps_qs_template
//   })
// }

const search = async () => {
  loading.value = true
  Loading.show({
    backgroundColor: 'transparent',
    spinnerColor: 'black',
    message: 'データ取得中...',
    messageColor: 'black'
  })

  pagination.value.page = 1

  // Set filters from search data
  ppsStore.setFilters({
    page: pagination.value.page,
    page_size: pagination.value.page_size,
    search: searchData.value.template_name,
    id_disease: searchData.value.id_disease
      ? parseInt(searchData.value.id_disease)
      : null,
    date_start: searchData.value.date_start,
    date_end: searchData.value.date_end
  })

  // Fetch data
  await ppsStore.fetchPPSQuestionTemplates()

  Loading.hide()
  loading.value = false
}

const loadMore = debounce(async (index: number, done: () => void) => {
  if (loading.value) {
    if (done) done()
    return
  }

  if (ppsStore.getNextPage) {
    loading.value = true
    await ppsStore.loadMoreTemplates()
    loading.value = false
  }

  if (done) done()
}, 200)

const onRowClick = async (row: any) => {
  // Select the template when row is clicked
  ppsStore.setSelectedTemplate(row)

  // Open the question editor modal for this template
  await openCreateUpdatePpsQuestionModal(row)
}

const moveNext = (e: any) => {
  const inputs = Array.from(
    e.target.form.querySelectorAll('input[type="text"]')
  )
  const index = inputs.indexOf(e.target)
  if (index === 0) {
    ;(inputs[index + 1] as HTMLElement).focus()
  } else {
    ;(inputs[1] as HTMLElement).blur()
    search()
  }
}

// Highlight search text function
const searchWithHighlight = (text: string, searchTerm: string) => {
  if (!searchTerm) return text
  const terms = searchTerm
    .split(',')
    .map((term) => term.trim())
    .filter((term) => term)
  if (!terms.length) return text

  let result = text
  terms.forEach((term) => {
    const regex = new RegExp(
      `(${term.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')})`,
      'gi'
    )
    result = result.replace(regex, '<mark>$1</mark>')
  })
  return result
}

// Load data on component mount
onMounted(async () => {
  await search()
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="text-grey-900 title2 bold">
          PPS質問テンプレート
        </q-toolbar-title>
        <div class="row mobile-hide">
          <div class="col-12">
            <div class="flex items-center q-gutter-x-sm">
              <MtFormInputDate
                :date="searchData.date_start"
                :tabindex="1"
                autofocus
                label="開始日：Start"
                outlined
                type="date"
                @keydown.enter="moveNext"
                @update:date="
                  (val) => {
                    searchData.date_start = val
                  }
                "
                @update:modelValue="
                  () => {
                    searchData.date_end = searchData.date_start
                  }
                "
              />
              <MtFormInputDate
                :date="searchData.date_end"
                :tabindex="2"
                class="q-mx-sm"
                label="終了日：End"
                outlined
                type="date"
                @keydown.enter="moveNext"
                @update:date="
                  (val) => {
                    searchData.date_end = val
                  }
                "
              />

              <MtInputForm
                type="text"
                label="テンプレート名"
                :tabindex="3"
                outlined
                v-model="searchData.template_name"
                class="q-mr-sm search-field"
              />

              <q-btn
                color="grey-800"
                text-color="white"
                tabindex="4"
                unelevated
                @click="search()"
              >
                <q-icon size="20px" name="search" />検索
              </q-btn>
            </div>
          </div>
        </div>
        <div class="row desktop-hide">
          <div class="col-12">
            <div class="flex items-center">
              <MtInputForm
                type="text"
                label="テンプレート名"
                outlined
                v-model="searchData.template_name"
                class="q-mr-sm search-field"
                @keydown.enter="moveNext"
              />
              <q-btn
                color="grey-800"
                text-color="white"
                unelevated
                class="q-mx-sm"
                @click="search()"
              >
                <q-icon size="20px" name="search" />
              </q-btn>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>

    <!-- Display total count -->
    <div
      class="flex justify-between q-px-md q-pt-xs text-grey-700"
      style="margin-top: 10px"
    >
      <span> 検索結果: {{ total_templates_count }}件</span>

      <!-- add button -->
      <span class="q-ml-sm">
        <q-btn
          color="grey-800"
          text-color="white"
          unelevated
          @click="openCreateUpdatePpsQuestionModal"
        >
          <q-icon size="20px" name="add" /> 問診
        </q-btn>
      </span>
    </div>

    <!-- Table with template data -->
    <MtTable2
      class="q-pt-sm"
      :columns="columns"
      :rows="pps_question_templates"
      :rowsBg="true"
      flat
      @virtual-scroll="loadMore"
      :style="{ height: 'calc(100dvh - 120px)' }"
    >
      <template v-slot:row="{ row }">
        <td
          v-for="(col, index) in columns"
          :key="index"
          @click="
            col.field !== 'modal_template_detail' ? onRowClick(row) : null
          "
          class="cursor-pointer"
        >
          <template v-if="col.field === 'datetime_insert'">
            {{ row['datetime_insert'] }}
          </template>
          <template v-else-if="typeof col.field === 'function'">
            {{ col.field(row) }}
          </template>
          <template v-else-if="col.field === 'memo_explanation'">
            <span
              v-html="
                searchWithHighlight(row[col.field], searchData.template_name)
              "
            ></span>
          </template>
          <template v-else-if="col.field === 'modal_template_detail'">
            <div style="float: right">
              <q-icon
                size="20px"
                name="more_horiz"
                color="grey-700"
                class="cursor-pointer"
                @click="openModalTemplateDetail(row, $event)"
              />
            </div>
          </template>
          <template v-else>
            {{ row[col.field] }}
          </template>
        </td>
      </template>

      <!-- No results template -->
      <template v-slot:no-data>
        <div class="text-center q-pa-md">検索結果がありません</div>
      </template>
    </MtTable2>

    <div ref="loadMoreTrigger" style="height: 1px"></div>
  </q-page>
</template>

<style lang="scss" scoped>
.tableRow {
  width: 100%;
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-word;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 8px;
  img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
}

// Style for highlighted text
:deep(mark) {
  background-color: #ffe082;
  padding: 0 2px;
  border-radius: 2px;
}
</style>
