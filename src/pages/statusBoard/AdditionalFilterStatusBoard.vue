<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormInputText from '@/components/form/MtFormInputText.vue'
import MtFormMultipleSelection from '@/components/form/MtFormMultipleSelection.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'

import useCommonStore from '@/stores/common'
import useCustomerStore from '@/stores/customers'
import { type RequestStatusAdditionalSearchType, useRequestStatus } from '@/stores/request-statuses'
import { storeToRefs } from 'pinia'

import { 
  formatDateWithTime
} from '@/utils/aahUtils'
import mtUtils from '@/utils/mtUtils'
import selectOptions from '@/utils/selectOptions'

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const commonStore = useCommonStore()
const customerStore = useCustomerStore()
const requestStatusStore = useRequestStatus()
const { getCustomer } = storeToRefs(customerStore)
const { getCommonTypeAnimalOptionList } = storeToRefs(commonStore)

const customerOptions = ref([]),
  customerOptionsDefault = reactive([])
const customersFilterKey = ref(0)

const props = defineProps<{
  searchData: RequestStatusAdditionalSearchType;
}>()

const defaultFilter = {
   time_selection: '',
   id_cm_animal: [],
   memo_status: '',
   id_customer: null,
   type_hour: null
}
const searchFilter = reactive({ ...defaultFilter })

const clearFilters = () => {
  Object.assign(searchFilter, defaultFilter)
}

const searchData = () => {
  let count = 0

  const finalPayload = {}

  Object.entries(searchFilter).forEach(([key, value]) => {
    finalPayload[key] = value
    if(key == 'id_cm_animal'){
      finalPayload[key] = value || []
      if(value && value.length > 0) count++
    }
    else if(value) count++
  })
  requestStatusStore.setAdditionalSearch(finalPayload, count)
  closeModal()
}

const updateCustomerOptions = async (val) => {
  customerOptions.value.length = customerOptionsDefault.length = 0
  searchFilter.id_customer = null
  if(val){
    let reqStatuses = await mtUtils.callApi(selectOptions.reqMethod.GET, 'req_statuses-v1', 
      { time_selection: formatDateWithTime(val, 'YYYY-MM-DD') })
    const idCustomerSet = new Set();

    reqStatuses.forEach(statusGroup => {
      Object.values(statusGroup).forEach(statusArray => {
        statusArray.forEach(status => {
          status.req_status_list.forEach(reqStatus => {
            if (reqStatus.customer) {
              idCustomerSet.add(reqStatus.customer.id_customer);
            }
          })
        })
      })
    })
    
    if(idCustomerSet.size > 0) {
      customerOptions.value = customerStore.getCustomerListOptions.filter((customer) => idCustomerSet.has(customer.value))
      customerOptionsDefault.push(...customerOptions.value)
    }
  }
  ++customersFilterKey.value
}

onMounted(() => {

  if(props.searchData.time_selection) {
    searchFilter.time_selection = props.searchData.time_selection
    updateCustomerOptions(props.searchData.time_selection)
  }

  if(props.searchData.id_cm_animal && props.searchData.id_cm_animal.length > 0) {
    searchFilter.id_cm_animal = props.searchData.id_cm_animal
  }

  if(props.searchData.memo_status) {
    searchFilter.memo_status = props.searchData.memo_status
  }

  if(props.searchData.id_customer) {
    searchFilter.id_customer = props.searchData.id_customer
  }

  if(props.searchData.type_hour) {
    searchFilter.type_hour = props.searchData.type_hour
  }
})
</script>
<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title class="text-grey-900 title3 bold">
      詳細検索
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section class="row q-col-gutter-sm">
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormInputDate
        v-model:date="searchFilter.time_selection"
        @update:date="updateCustomerOptions"
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormMultipleSelection
        :options="getCommonTypeAnimalOptionList"
        v-model="searchFilter.id_cm_animal"
        label="動物種"
        option-label="label"
        option-value="value"
        outlined
        required
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFilterSelect
        :options="customerOptions"
        :options-default="customerOptionsDefault"
        v-model:selecting="searchFilter.id_customer"
        label="診察券番号"
        :key="customersFilterKey"
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormInputText
        v-model="searchFilter.memo_status"
        label="ステータスメモ"
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormRadiobtn
        v-model="searchFilter.type_hour"
        label="滞在2時間以上"
        :val="1"
      />
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12">
      <MtFormRadiobtn
        v-model="searchFilter.type_hour"
        label="滞在3時間以上"
        :val="2"
      />
    </div>
    <div class="flex justify-end full-width text-blue cursor-pointer" @click="clearFilters">
      全クリア
    </div>
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn outline @click="closeModal()" class="bg-grey-100 text-grey-800">
        <span>閉じる</span>
      </q-btn>
      <q-btn
        tabindex="10"
        color="primary"
        class="q-ml-md"
        @click="searchData()"
      >
        <span>適用</span>
      </q-btn>
    </div>
  </q-card-section>
</template>