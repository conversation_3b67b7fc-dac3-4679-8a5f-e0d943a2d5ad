<script setup lang="ts">
import { onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'

import { Common } from '@/types/types'
import { groupBy } from 'lodash'
import { roundFloat } from '@/utils/aahUtils'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'

import { event_bus } from '@/utils/eventBus'

import { storeToRefs } from 'pinia'
import useCommonStore from '@/stores/common'
import { Overwrites, useVettyMasterStore, vettyMasterImageBaseUrl } from '@/stores/vetty-master'

const commonStore = useCommonStore()
const vettyMasterStore = useVettyMasterStore()
const {
  getCommonTypeMedicineFormatOptionList
} = storeToRefs(commonStore)

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const props = defineProps<{
  medicinesPool: any[]
}>()

const selectedMedicinesPool = ref(JSON.parse(JSON.stringify(props.medicinesPool)))
const commonOptionList = ref<Common>([])

const getCmMedFormat = (idCmMedFormat: number) => {
  return getCommonTypeMedicineFormatOptionList.value.find((v: Common) => v.id_common === idCmMedFormat)?.name_common
}

const getUnitName = (idUnit) => commonStore.getCommonUnitOptionList.find((v: Common) => v.id_common == idUnit)?.name_common

const importMedicine = () => {
  const itemServiceData = selectedMedicinesPool.value.map((itemService) => itemService)

  const newItemServiceData = itemServiceData.filter((itemService) => itemService.type_overwrite === Overwrites.NEW)
  const overwriteItemServiceData = itemServiceData.filter((itemService) => itemService.type_overwrite === Overwrites.FULL_OVERWRITE)

  vettyMasterStore.importMedicines({ 'item_service_list': newItemServiceData, 'partial_item_service_list': overwriteItemServiceData })
  .then(() => {
    mtUtils.autoCloseAlert(aahMessages.success)
    event_bus.emit('medicine-imported')
  })
}

onMounted(async () => {
  await commonStore.fetchPreparationCommonList({ code_common: [1, 4, 11, 12, 5] }, true)
  commonOptionList.value = commonStore.getCommonTypeAnimalOptionList
    .map((o: any) => ({
      value: o.id_common,
      label: o.name_common,
      status: 1,
      id_common: o.id_common,
      value1: o.code_func2
    }))
})

</script>
<template>
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title
        class="row no-wrap items-center text-grey-900 title2 bold"
      >
        確認画面
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section class="content">
    <template v-for="(poolMedicine, index) in selectedMedicinesPool" :key="poolMedicine.id_item_service">
       <div class="flex justify-between">
         <div>
           <q-chip
              :color="poolMedicine.type_overwrite == Overwrites.NEW ? 'teal' : 'red'"
              text-color="white"
            >
             {{ poolMedicine.type_overwrite == Overwrites.NEW ? 'Create new' : 'OW fully' }}
           </q-chip>
           <span class="text-weight-bold" style="font-size: 30px"> {{ poolMedicine.name_item_service }} </span>
         </div>
         <div> 
           <img class="is-image" v-if="poolMedicine.image_path1 && poolMedicine.image_path1 != 'null'" :src="`${vettyMasterImageBaseUrl}${poolMedicine.image_path1}`" />
         </div>
      </div>
      <div class="row q-col-gutter-md q-mt-md">
        <div class="col-3">
           <div>早見表: <q-icon v-if="poolMedicine.medicine.flg_dosage_fixed" name="check" class="text-positive text-weight-bold" size="20px" /> </div>
           <div>per キロ (可変): <q-icon v-if="poolMedicine.medicine.flg_dosage_variable" name="check" class="text-positive text-weight-bold" size="20px" /></div>
           <div>per ヘッド (可変): <q-icon v-if="poolMedicine.medicine.flg_dosage_per_head" name="check" class="text-positive text-weight-bold" size="20px" /></div>
           <div>数量指定: <q-icon v-if="poolMedicine.medicine.flg_dosage_quantity" name="check" class="text-positive text-weight-bold" size="20px" /></div>
        </div>
        <div class="col-3">
          <div>有効成分を指定しない: <q-icon v-if="poolMedicine.medicine.flg_no_efficacy_ingredient" name="check" class="text-positive text-weight-bold" size="20px" /></div>
          <div>デフォルト注射売上量を1にする: <q-icon v-if="poolMedicine.medicine.flg_default_one_sales_quantity" name="check" class="text-positive text-weight-bold" size="20px" /></div>
          <div>医薬品形状: {{ getCmMedFormat(poolMedicine.medicine.id_cm_med_format) }}</div>
        </div>
        <div class="col-3"></div>
        <div class="col-3"></div>
      </div>

       <div class="row q-col-gutter-xs q-my-md" v-if="poolMedicine.medicine.flg_dosage_fixed">
          <div class="col-12">
            <table class="table-custom-fixed q-mb-lg" v-for="[key, value] in Object.entries(groupBy(poolMedicine.dosage_fixed_list, 'id_common'))" :key="key">
              <thead>
                <tr>
                  <td class="flex justify-center ">
                    {{ `( ${commonOptionList.find((v: Common) => v.id_common == key)?.label ?? '全種'} )` }}
                  </td>
                  <template
                    v-if="poolMedicine.item_service_unit_list && poolMedicine.item_service_unit_list.length > 0">
                    <template v-for="(item, index) in poolMedicine.item_service_unit_list.sort((iSUObjA: any, iSUObjB: any) => 
                      parseInt(iSUObjA.display_order) - parseInt(iSUObjB.display_order))" :key="index">
                      <td>
                        <q-btn unelevated color="primary" :ripple="false" style="min-width: 150px;"
                          class="q-mr-sm q-px-lg full-width full-height" no-caps type="button">
                          <span>{{ item.name_service_item_unit }}</span>
                        </q-btn>
                      </td>
                    </template>
                  </template>
                </tr>
              </thead>
              <tbody>
                <template
                  v-if="value && value.length > 0">
                  <tr v-for="(fixedDosage, index2) in value" :key="index2">
                    <td>
                      <q-btn unelevated color="primary" :ripple="false" style="min-width: 150px;"
                        class="q-mr-sm q-px-lg full-width full-height" no-caps type="button">
                        <span>{{ roundFloat(fixedDosage.val_weight_min / 1000) }}<span class="body2">kg</span> ~ {{ roundFloat(fixedDosage.val_weight_max / 1000) }}
                        <span class="body2">kg</span>{{ '  未満 ' + ` ( ${commonOptionList.find((v) => v.value == fixedDosage.id_common)?.label ?? '全種'} )` }}</span>
                      </q-btn>
                    </td>
                    <template
                      v-if="poolMedicine.item_service_unit_list && poolMedicine.item_service_unit_list.length > 0">
                      <template v-for="(item2, index3) in poolMedicine.item_service_unit_list.sort((iSUObjA: any, iSUObjB: any) =>
                        parseInt(iSUObjA.display_order) - parseInt(iSUObjB.display_order))" :key="index3">
                        <td class="q-ba text-center fixed-detail-hover">
                          <div class="cursor-pointer"
                            v-if="fixedDosage.dosage_fixed_detail_list && fixedDosage.dosage_fixed_detail_list.length > 0 && fixedDosage.dosage_fixed_detail_list.find(v => v.id_item_service_unit == item2.id_item_service_unit && v.id_dosage_fixed == fixedDosage.id_dosage_fixed)">
                            {{ fixedDosage.dosage_fixed_detail_list.find(v => v.id_item_service_unit ==
                              item2.id_item_service_unit && v.id_dosage_fixed == fixedDosage.id_dosage_fixed)?.quantity
                            }} <span class="body2">{{ getUnitName(fixedDosage.dosage_fixed_detail_list.find(v => v.id_item_service_unit ==
                              item2.id_item_service_unit && v.id_dosage_fixed == fixedDosage.id_dosage_fixed)?.id_common) }}</span>
                          </div>
                          <div v-else
                            class="fixed-detail-hover-btn cursor-pointer bg-grey-050 text-black full-width full-height">
                            -
                          </div>
                        </td>
                      </template>
                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>

      <div class="q-mt-md">
        <template v-for="itemServiceUnit in poolMedicine.item_service_unit_list" :key="itemServiceUnit.id_item_service_unit">
          <div class="isu-box row q-pa-sm">
            <div class="col-md-9">
              <div class="flex justify-between">
                {{itemServiceUnit.name_service_item_unit}}
                <div>Price: {{itemServiceUnit.unit_price}}円/錠</div>
              </div>
            </div>
            <div class="col-md-3">
              <img v-if="itemServiceUnit.image_path1" :src="`${vettyMasterImageBaseUrl}${itemServiceUnit.image_path1}`" class="isu-image" />
            </div>
          </div>
        </template>
      </div>
      <q-separator class="q-my-md" v-if="(index !== selectedMedicinesPool.length - 1)" />
    </template> 
  </q-card-section>
    <q-card-section class="q-bt bg-white">
    <div class="flex justify-between">
      <div>{{ selectedMedicinesPool.length }}件が追加対象です</div>
      <div>
        <q-btn
          color="primary"
          @click="importMedicine"
        >
          追加する
        </q-btn>
      </div>
    </div>
  </q-card-section>
</template>
<style lang="scss" scoped>
.isu-box {
  background: rgb(255, 242, 204);
  border: 1px solid $grey;
  border-radius: 20px;
  &:not(:last-child) {
    margin-bottom: 20px;
  }
}
.is-image, .isu-image {
  max-width: 150px;
  max-height: 150px;
  object-fit: cover;
}
</style>