<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtFormRadiobtn from '@/components/form/MtFormRadiobtn.vue'
import MtFormInputNumber2 from '@/components/form/MtFormInputNumber2.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import ConfirmTargetMedicineItem from '@/pages/master/medicine/ConfirmTargetMedicineItem.vue'
import MtCategorySelectionComponent from '@/components/modals/MtCategorySelectionComponent.vue'
import MtSearchItemService from '@/components/MtSearchItemService.vue'

import mtUtils from '@/utils/mtUtils'
import aahValidations from '@/utils/aahValidations'
import { event_bus } from '@/utils/eventBus'
import selectOptions from '@/utils/selectOptions'

import useItemStore from '@/stores/items'
import useModalStore from '@/stores/modal'
import { Overwrites } from '@/stores/vetty-master'
import { storeToRefs } from 'pinia'

const itemStore = useItemStore()
const modalStore = useModalStore()
const { getAllItems } = storeToRefs(itemStore)

const props = defineProps<{
  medicinesPool: any[]
}>()

const emits = defineEmits(['close'])
const closeModal = () => emits('close')

const selectedMedicinesPool = ref(JSON.parse(JSON.stringify(props.medicinesPool)))

const clinicExistingMedicines = ref([])
const clinicExistingMedicinesDefault = reactive([])

const scrollContainer = ref(), showScrollTop = ref(false)

const isuLinkKey = ref(0)

const selectedPoolMedicines = computed(() => {
  return selectedMedicinesPool.value.filter((poolMedicine) => !!poolMedicine.checked).length
})

const removeMedicineFromPool = (index) => {
  selectedMedicinesPool.value.splice(index, 1)
}

const openConfirmTargetMedicineModal = () => {
  mtUtils.popup(ConfirmTargetMedicineItem, {
    medicinesPool: selectedMedicinesPool.value
  })
}

const category1Selected = (val, poolMedicine) => {
  poolMedicine.id_category1 = val
  if(!val) poolMedicine.name_category1 = null
}

const category2Selected = (val, poolMedicine) => {
  poolMedicine.id_category2 = val
  if(!val) {
    poolMedicine.name_category2 = null
    poolMedicine.code_category2 = null
  }
}

const scrollToMedicine = (medicineId: number) => {
  const container = scrollContainer.value.$el
  const el = document.getElementById(`medicine-${medicineId}`)
  if (container && el) {
    const containerTop = container.getBoundingClientRect().top
    const elementTop = el.getBoundingClientRect().top
    const offset = elementTop - containerTop - 50
    container.scrollTo({ top: container.scrollTop + offset, behavior: 'smooth' })
  }
}

const getRowBgColor = (poolMedicine) => {
  if(!poolMedicine.checked) return '#fff'
  return poolMedicine.type_overwrite == Overwrites.NEW ? '#baefc9' : '#aac2ff'
}

const handleBodyScroll = () => {
  showScrollTop.value = scrollContainer.value.$el.scrollTop > 50
}

const scrollToBodyTop = () => {
  if (scrollContainer.value) {
    scrollContainer.value.$el.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

const confirmMedicineDelete = async (index) => {
  const confirmation = await mtUtils.confirm(
    '本当に薬を削除してもよろしいですか？',
    '確認',
    '薬の削除'
  )
  if (confirmation) {
    removeMedicineFromPool(index)
  }
}

const selectNameCategory1 = (nameCategory1, poolMedicine) => {
  poolMedicine.name_category1 = nameCategory1
}

const selectNameCategory2 = (nameCategory2, poolMedicine) => {
  poolMedicine.name_category2 = nameCategory2
}

const selectCodeCategory = (codeCategory, poolMedicine) => {
  poolMedicine.code_category2 = codeCategory
}

const showNameItemService = (nameItemService: string) => {
  if(!nameItemService) return ''
  if(nameItemService.length <= 3) return nameItemService
  return nameItemService.slice(0, 3) + '...'
}

const fetchMedicine = async(idItemService, poolMedicine) => {
  poolMedicine.item_service_unit_list.forEach((itemServiceUnit) => {
    itemServiceUnit.id_item_service_unit_link = null
  })

  poolMedicine.isuOptions.length = 0  
  poolMedicine.isuOptionsDefault.length = 0

  if(idItemService) {
    const response = await mtUtils.callApi(selectOptions.reqMethod.GET, `/mst/item_services/${idItemService}`, {})
    if(response) {
      const isuOptions = response?.item_service_unit_list.sort((iSUObjA: any, iSUObjB: any) =>
        parseInt(iSUObjA.display_order) - parseInt(iSUObjB.display_order)).map((itemServiceUnit) => {
          return {
            ...itemServiceUnit,
            label: itemServiceUnit.name_service_item_unit,
            value: itemServiceUnit.id_item_service_unit
          }
        })
      poolMedicine.isuOptions = isuOptions || []
      poolMedicine.isuOptionsDefault = [...isuOptions] || []
    }
  }

  ++isuLinkKey.value
}

const getMedicinceByMstCode = async (poolMedicine) => {
  const codeMstIs = poolMedicine.code_item_service
  const response = await mtUtils.callApi(selectOptions.reqMethod.GET, `mst/item_service_by_code_mst/${codeMstIs}`)
  if(response) {

    poolMedicine.isuOptions.length = 0
    poolMedicine.isuOptionsDefault.length = 0
    
    poolMedicine.id_item_service_link = response.id_item_service
    poolMedicine.flg_disable_medicine_select = true
    poolMedicine.item_service_unit_list.forEach((itemServiceUnit) => {
      const isuExist = response.item_service_unit_list.find((cliniciItemServiceUnit) => itemServiceUnit.code_item_service_unit === cliniciItemServiceUnit.code_mst_isu)
      itemServiceUnit.type_overwrite = isuExist ? Overwrites.PARTIAL_OVERWRITE : Overwrites.NEW
      itemServiceUnit.checked = isuExist ? true : false
      itemServiceUnit.id_item_service_unit_link = isuExist?.id_item_service_unit || null
      itemServiceUnit.disableSelection = !!isuExist
    })
    poolMedicine.isuOptions = response.item_service_unit_list.map((v) => ({ ...v, label: v.name_service_item_unit, value: v.id_item_service_unit }))
    poolMedicine.isuOptionsDefault.push(...poolMedicine.isuOptions)
    ++isuLinkKey.value
  }
}

const updatePoolMedicineSelection = (poolMedicine) => {

  poolMedicine.isuOptions.length = 0
  poolMedicine.isuOptionsDefault.length = 0

  poolMedicine.id_item_service_link = null
  poolMedicine.flg_disable_medicine_select = false
  poolMedicine.item_service_unit_list.forEach((itemServiceUnit) => {
    itemServiceUnit.type_overwrite = Overwrites.NEW
    itemServiceUnit.disableSelection = false
    itemServiceUnit.id_item_service_unit_link = null
  })
  ++isuLinkKey.value
}

onMounted(() => {
  itemStore.fetchItems({ flg_service: false })
  .then(() => {
    clinicExistingMedicines.value = [...getAllItems.value]
    clinicExistingMedicinesDefault.push(...clinicExistingMedicines.value)
  })

  nextTick(() => {
    if(scrollContainer.value) {
      scrollContainer.value.$el.addEventListener('scroll', handleBodyScroll)
    }
  })

  event_bus.on('medicine-imported', closeModal)
})

onUnmounted(() => {
  if(scrollContainer.value) {
    scrollContainer.value.$el.removeEventListener('scroll', handleBodyScroll)
  }
  event_bus.off('medicine-imported', closeModal)
})
</script>
<template>
 <q-form @submit="openConfirmTargetMedicineModal">
  <MtModalHeader @closeModal="closeModal">
    <q-toolbar-title
        class="row no-wrap items-center text-grey-900 title2 bold"
      >
      登録条件の設定
    </q-toolbar-title>
  </MtModalHeader>
  <q-card-section class="content relative-position" ref="scrollContainer">
    <div class="q-mt-sm q-px-md flex gap-4 medicine-nav">
      <template v-for="(poolMedicineId, idx) in selectedMedicinesPool.length" :key="poolMedicineId">
        <span @click="scrollToMedicine(poolMedicineId)" style="font-size: 30px;" class="cursor-pointer text-weight-bold">
          {{ poolMedicineId }} 
          <span class="text-body2">
            {{ showNameItemService(selectedMedicinesPool[idx].name_item_service) }}
          </span>
        </span>
      </template>
    </div>
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-md-3"></div>
      <div class="col-md-9">
        <div class="row q-col-gutter-md">
          <div class="col-8"></div>
          <div class="col-2 text-center">料金</div>
          <div class="col-2 text-center">単位</div>
        </div>
      </div>
    </div>
    <template v-for="(poolMedicine, index) in selectedMedicinesPool" :key="poolMedicine.id_item_service">
       <div class="row q-col-gutter-md relative-position q-mx-sm q-pb-md" :id="`medicine-${index + 1}`"
         :style="{'background-color': getRowBgColor(poolMedicine), 'margin-top': 'unset'}">
         <div class="absolute medicine-number">
           <span style="font-size: 30px;" class="cursor-pointer text-weight-bold">{{ index + 1 }}</span>
         </div>
        <div @click="confirmMedicineDelete(index)" class="absolute-top-right remove-medicine cursor-pointer">
          <q-icon name="close" class="text-red" style="font-size: 24px;" />
        </div>
         <div class="col-md-3 flex items-start" style="flex-wrap: nowrap;">
           <div>
             <MtFormCheckBox
               v-model:checked="poolMedicine.checked"
              />
           </div>
           <div>
             <div class="font-weight-bold"> {{ poolMedicine.name_item_service }} </div>
             <div>
               {{ poolMedicine.medicine_category }}
               <span v-if="poolMedicine.medicine_category && poolMedicine.medicine_regulation">
                 <q-icon name="arrow_right" />
               </span>
               {{ poolMedicine.medicine_regulation }}
             </div>
             <div>
               <MtFormRadiobtn 
                 label="新規" 
                 :val="Overwrites.NEW" 
                 v-model:selected="poolMedicine.type_overwrite"
                 @update:selected="(val) => {
                  if(!val) poolMedicine.type_overwrite = Overwrites.NEW
                  else poolMedicine.id_item_service_link = null
                  updatePoolMedicineSelection(poolMedicine)
                 }"
               />
               <MtFormRadiobtn 
                 label="上書き" 
                 :val="Overwrites.FULL_OVERWRITE" 
                 v-model:selected="poolMedicine.type_overwrite"
                 @update:selected="(val) => {
                  if(!val) poolMedicine.type_overwrite = Overwrites.FULL_OVERWRITE
                  else poolMedicine.id_category1 = poolMedicine.id_category2 = null;
                  getMedicinceByMstCode(poolMedicine)
                 }"
               />
             </div>
             <template v-if="poolMedicine.type_overwrite == Overwrites.NEW">
               <div class="q-ml-md">
                 <MtCategorySelectionComponent
                   :flg_for_medicine="true"
                   :data="{'id_category1': poolMedicine.id_category1, 'id_category2': poolMedicine.id_category2}"
                   @category1Selected="(val) => category1Selected(val, poolMedicine)"
                   @category2Selected="(val) => category2Selected(val, poolMedicine)"
                   @category1="(nameCategory1) => selectNameCategory1(nameCategory1, poolMedicine)"
                   @category2="(nameCategory2) => selectNameCategory2(nameCategory2, poolMedicine)"
                   @codeCategory="(codeCategory) => selectCodeCategory(codeCategory, poolMedicine)"
                   col_class="col-6"
                   :rules="[aahValidations.validationRequired]"
                   required
                  />
               </div>
             </template>
             <template v-if="poolMedicine.type_overwrite == Overwrites.FULL_OVERWRITE">
               <div class="q-ml-md">
                 <MtSearchItemService
                  :applyDefaultClass="false"
                  :preSelectedId="poolMedicine.id_item_service_link"
                  :flgMedicine="true"
                  label="登録商品名 "
                  @update:selecting-whole="
                    (value) => {
                      if(value) {
                        poolMedicine.id_item_service_link = value.id_item_service
                        fetchMedicine(value.id_item_service, poolMedicine)
                      }
                    }
                  "
                  :search-icon="true"
                  :rules="[aahValidations.validationRequired]"
                  :disable="poolMedicine.flg_disable_medicine_select"
                />
               </div>
             </template>
             <MtFormCheckBox
               class="q-mt-md"
               label="Overwrite dosage table"
               v-model:checked="poolMedicine.flg_overwrite_dosage_table"
             />
           </div>
         </div>

         <div class="col-md-9">
           <template v-for="(itemServiceUnit, isuIndex) in poolMedicine.item_service_unit_list" :key="itemServiceUnit.id_item_service_unit">
             <div class="row q-col-gutter-md isu-row" :class="isuIndex !== 0 ? 'q-mt-xs' : ''">
               <div class="col-3 isu-col">
                 <div class="flex items-center">
                   <MtFormCheckBox
                     v-model:checked="itemServiceUnit.checked"
                    />
                    <div>
                      <div class="flex items-center gap-5">
                        {{ itemServiceUnit.name_service_item_unit }}
                      </div>
                      <template 
                        v-if="itemServiceUnit.checked && (itemServiceUnit.type_overwrite == Overwrites.FULL_OVERWRITE || itemServiceUnit.type_overwrite == Overwrites.PARTIAL_OVERWRITE)">
                        <div>
                          <MtFilterSelect
                            :options="poolMedicine.isuOptions"
                            :options-default="poolMedicine.isuOptionsDefault"
                            v-model:selecting="itemServiceUnit.id_item_service_unit_link"
                            :key="isuLinkKey"
                            :rules="[aahValidations.validationRequired]"
                            required
                            :disable="itemServiceUnit.disableSelection"
                          />
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
                <div class="col-5 isu-col">
                  <MtFormRadiobtn 
                    label="New" 
                    :val="Overwrites.NEW" 
                    v-model:selected="itemServiceUnit.type_overwrite"
                    :disable="poolMedicine.type_overwrite === Overwrites.NEW || itemServiceUnit.disableSelection"
                    @update:selected="(val) => {
                      if(!val) itemServiceUnit.type_overwrite = Overwrites.NEW
                    }"
                  />
                  <MtFormRadiobtn 
                    label="Full OW" 
                    :val="Overwrites.FULL_OVERWRITE" 
                    v-model:selected="itemServiceUnit.type_overwrite"
                    :disable="poolMedicine.type_overwrite === Overwrites.NEW"
                    @update:selected="(val) => {
                      if(!val) itemServiceUnit.type_overwrite = Overwrites.FULL_OVERWRITE
                    }"
                  />
                  <MtFormRadiobtn 
                    label="Partial OW" 
                    :val="Overwrites.PARTIAL_OVERWRITE" 
                    v-model:selected="itemServiceUnit.type_overwrite"
                    :disable="poolMedicine.type_overwrite === Overwrites.NEW"
                    @update:selected="(val) => {
                      if(!val) itemServiceUnit.type_overwrite = Overwrites.PARTIAL_OVERWRITE
                    }"
                  />
                </div>
                <div class="col-2 isu-col">
                  <MtFormInputNumber2
                    v-if="poolMedicine.type_overwrite === Overwrites.NEW"
                    :disable="poolMedicine.type_overwrite != Overwrites.NEW || itemServiceUnit.type_overwrite != Overwrites.NEW"
                    :class="(poolMedicine.type_overwrite != Overwrites.NEW || itemServiceUnit.type_overwrite != Overwrites.NEW) ? 'bg-grey-300' : ''"
                    class="unit-price-input"
                    v-model:value="itemServiceUnit.unit_price"
                    inputClass="text-right"
                    label=""
                    outlined
                  />
                </div>
                <div class="col-2 isu-col text-center relative-position gap-4">
                  /錠
                </div>
              </div>
           </template>
         </div>
       </div>
       <q-separator class="q-my-sm q-mx-sm" v-if="index !== (selectedMedicinesPool.length - 1)" />
    </template>
    <q-icon 
      name="arrow_circle_up" 
      v-if="showScrollTop" 
      @click="scrollToBodyTop" 
      class="scroll-top-btn" 
      size="40px" 
    />
  </q-card-section>
  <q-card-section class="q-bt bg-white">
    <div class="flex justify-between">
      <div>{{ selectedPoolMedicines }}件が追加対象です</div>
      <div>
        <q-btn
          color="primary"
          :disable="selectedPoolMedicines.length"
          type="submit"
        >
          追加する
        </q-btn>
      </div>
    </div>
  </q-card-section>
 </q-form>
</template>
<style lang="scss" scoped>
.medicine-nav {
  position: sticky;
  top: -20px;
  background: white;
  z-index: 1000;
  flex-wrap: nowrap;
  overflow-x: auto;
  white-space: nowrap;
}
.unit-price-input {
  :deep(.q-field__control) {
    height: 26px;
    input {
      padding-top: 6px;
    }
  }
}
.isu-row {
  &:not(:first-child) {
    .isu-col {
      padding-top: 0;
    }
  }
}
.medicine-number {
  top: 50%;
  transform: translateY(-50%);
}
.remove-medicine {
  border: 2px solid red;
  border-radius: 50%;
  right: 10px;
  top: 16px;
  z-index: 999;
  padding-top: unset;
  padding-left: unset;
}
.scroll-top-btn {
  position: fixed;
  bottom: 90px;
  right: 30px;
  padding: 10px 15px;
  background: $primary;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: opacity 0.3s;
  z-index: 9999;
}
</style>