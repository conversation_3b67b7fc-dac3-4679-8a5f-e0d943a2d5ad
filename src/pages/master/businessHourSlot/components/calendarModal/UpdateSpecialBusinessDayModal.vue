<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import useBusinessHourSlot from '@/stores/business-hour-slots'
import useSpecialBusinessDayStore from '@/stores/special-business-days'
import OptionModal from '@/components/OptionModal.vue'
import { storeToRefs } from 'pinia'
import mtUtils from '@/utils/mtUtils'
import { getDateByFormat } from '@/utils/aahUtils'
import { timeHour, timeMinute } from '@/utils/enum'

const props = defineProps({
  data: Object
})
const businessHourSlot = useBusinessHourSlot()
const specialBusinessDayStore = useSpecialBusinessDayStore()
const { getAllBusinessHourSlots, getBusinessHourSlot } = storeToRefs(businessHourSlot)
const { getSpecialBusinessDays } = storeToRefs(specialBusinessDayStore)

const isEdit = ref(false)

const businessForm = reactive({
  id_special_business_day: '',
  datetime_business_day: null,
  memo_business_day: '',
  id_business_hour_slot: '',
  id_clinic: null
})
const businessSlot = ref({
  //id_business_hour_slot: null,

  type_business_day: 1,
  id_clinic: null,
  name_business_hour: null,
  ticket_limit_upper1: null,
  time_business_start1: null,
  time_business_start1hour: null,
  time_business_start1minute: null,
  time_business_end1hour: null,
  time_business_end1minute: null,
  time_business_end1: null,

  time_checkin_start1: null,
  time_checkin_start1hour: null,
  time_checkin_start1minute: null,
  time_checkin_end1hour: null,
  time_checkin_end1minute: null,
  time_checkin_end1: null,

  time_ticket_issue_start1: null,
  time_ticket_issue_start1hour: null,
  time_ticket_issue_start1minute: null,
  time_ticket_issue_end1hour: null,
  time_ticket_issue_end1minute: null,
  time_ticket_issue_end1: null,

  flg_add_slot2_UI: false,
  ticket_limit_upper2: null,
  time_business_start2: null,
  time_business_start2hour: null,
  time_business_start2minute: null,
  time_business_end2hour: null,
  time_business_end2minute: null,
  time_business_end2: null,

  time_checkin_start2: null,
  time_checkin_start2hour: null,
  time_checkin_start2minute: null,
  time_checkin_end2hour: null,
  time_checkin_end2minute: null,
  time_checkin_end2: null,

  time_ticket_issue_start2: null,
  time_ticket_issue_start2hour: null,
  time_ticket_issue_start2minute: null,
  time_ticket_issue_end2hour: null,
  time_ticket_issue_end2minute: null,
  time_ticket_issue_end2: null,

  flg_add_slot3_UI: false,
  ticket_limit_upper3: null,
  time_business_start3: null,
  time_business_start3hour: null,
  time_business_start3minute: null,
  time_business_end3hour: null,
  time_business_end3minute: null,
  time_business_end3: null,

  time_checkin_start3: null,
  time_checkin_start3hour: null,
  time_checkin_start3minute: null,
  time_checkin_end3hour: null,
  time_checkin_end3minute: null,
  time_checkin_end3: null,

  time_ticket_issue_start3: null,
  time_ticket_issue_start3hour: null,
  time_ticket_issue_start3minute: null,
  time_ticket_issue_end3hour: null,
  time_ticket_issue_end3minute: null,
  time_ticket_issue_end3: null,

  display_order: '999'
})

const emits = defineEmits(['close'])

const submit = () => {
  businessForm.datetime_business_day = getDateByFormat(
    businessForm.datetime_business_day,
    'YYYY/MM/DD HH:mm:ss'
  )
  if (isEdit.value) {
    specialBusinessDayStore
      .updateSpecialBusinessDay(
        businessForm.id_special_business_day,
        businessForm
      )
      .then(async () => {
        await specialBusinessDayStore.fetchSpecialBusinessDays()
        emits('close')
        mtUtils.autoCloseAlert(aahMessages.success)
      })
  } else {
    if (isExistBusinessDay() === false) {
      delete businessForm.id_special_business_day
      specialBusinessDayStore
        .submitSpecialBusinessDay(businessForm)
        .then(async () => {
          await specialBusinessDayStore.fetchSpecialBusinessDays()
          emits('close')
          mtUtils.autoCloseAlert(aahMessages.success)
        })
    } else {
      mtUtils.autoCloseAlert('指定日の診療時間枠は既に存在しています。')
    }
  }
}
const isExistBusinessDay = () => {
  if (
    getSpecialBusinessDays.value.find(
      (item: any) =>
        item.datetime_business_day === businessForm.datetime_business_day &&
        item.id_business_hour_slot === businessForm.id_business_hour_slot
    )
  ) {
    return true
  }
  return false
}
const openMenu = async () => {
  let menuOptions = [
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]
  await mtUtils.littlePopup(OptionModal, { options: menuOptions })
  let selectedOption = menuOptions.find((i) => i.isChanged == true)
  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      await mtUtils
        .confirm(aahMessages.delete_ask, aahMessages.delete)
        .then((confirmation) => {
          if (confirmation) {
            specialBusinessDayStore
              .destroySpecialBusinessDay(businessForm.id_special_business_day)
              .then(async () => {
                await mtUtils.autoCloseAlert(aahMessages.success)
                await specialBusinessDayStore.fetchSpecialBusinessDays()
                emits('close')
              })
          }
        })
    }
  }
}
const handleBusinessHourSlot = async (value: string) => {
  businessHourSlot.selectBusinessHourSlot(value)
  if (getBusinessHourSlot.value) {
    businessSlot.value = getBusinessHourSlot.value
    if(getBusinessHourSlot.value.type_business_day != 90){
      businessSlot.value.time_business_start1hour =
        getBusinessHourSlot.value.time_business_start1.split(':')[0]
      businessSlot.value.time_business_start1minute =
        getBusinessHourSlot.value.time_business_start1.split(':')[1]
      businessSlot.value.time_business_end1hour =
        getBusinessHourSlot.value.time_business_end1.split(':')[0]
      businessSlot.value.time_business_end1minute =
        getBusinessHourSlot.value.time_business_end1.split(':')[1]
      businessSlot.value.time_checkin_start1hour =
        getBusinessHourSlot.value.time_checkin_start1.split(':')[0]
      businessSlot.value.time_checkin_start1minute =
        getBusinessHourSlot.value.time_checkin_start1.split(':')[1]
      businessSlot.value.time_checkin_end1hour =
        getBusinessHourSlot.value.time_checkin_end1.split(':')[0]
      businessSlot.value.time_checkin_end1minute =
        getBusinessHourSlot.value.time_checkin_end1.split(':')[1]
      businessSlot.value.time_ticket_issue_start1hour =
        getBusinessHourSlot.value.time_ticket_issue_start1.split(':')[0]
      businessSlot.value.time_ticket_issue_start1minute =
        getBusinessHourSlot.value.time_ticket_issue_start1.split(':')[1]
      businessSlot.value.time_ticket_issue_end1hour =
        getBusinessHourSlot.value.time_ticket_issue_end1.split(':')[0]
      businessSlot.value.time_ticket_issue_end1minute =
        getBusinessHourSlot.value.time_ticket_issue_end1.split(':')[1]

      if (getBusinessHourSlot.value.time_business_start1 !== null && getBusinessHourSlot.value.time_business_start2 === null) {
        businessSlot.value.flg_add_slot2_UI = false
      }
      if (getBusinessHourSlot.value.time_business_start1 !== null && getBusinessHourSlot.value.time_business_start2 !== null && getBusinessHourSlot.value.time_business_start3 === null) {
        businessSlot.value.flg_add_slot2_UI = true
        businessSlot.value.flg_add_slot3_UI = false
      }
      if (getBusinessHourSlot.value.time_business_start1 !== null && getBusinessHourSlot.value.time_business_start2 !== null && getBusinessHourSlot.value.time_business_start3 !== null) {
        businessSlot.value.flg_add_slot2_UI = true
        businessSlot.value.flg_add_slot3_UI = true
      }        
      if (getBusinessHourSlot.value.time_business_start2 !== null) {
        businessSlot.value.flg_add_slot2_UI = true
        businessSlot.value.time_business_start2hour =
          getBusinessHourSlot.value.time_business_start2.split(':')[0]
        businessSlot.value.time_business_start2minute =
          getBusinessHourSlot.value.time_business_start2.split(':')[1]
        businessSlot.value.time_business_end2hour =
          getBusinessHourSlot.value.time_business_end2.split(':')[0]
        businessSlot.value.time_business_end2minute =
          getBusinessHourSlot.value.time_business_end2.split(':')[1]
        businessSlot.value.time_checkin_start2hour =
          getBusinessHourSlot.value.time_checkin_start2.split(':')[0]
        businessSlot.value.time_checkin_start2minute =
          getBusinessHourSlot.value.time_checkin_start2.split(':')[1]
        businessSlot.value.time_checkin_end2hour =
          getBusinessHourSlot.value.time_checkin_end2.split(':')[0]
        businessSlot.value.time_checkin_end2minute =
          getBusinessHourSlot.value.time_checkin_end2.split(':')[1]
        businessSlot.value.time_ticket_issue_start2hour =
          getBusinessHourSlot.value.time_ticket_issue_start2.split(':')[0]
        businessSlot.value.time_ticket_issue_start2minute =
          getBusinessHourSlot.value.time_ticket_issue_start2.split(':')[1]
        businessSlot.value.time_ticket_issue_end2hour =
          getBusinessHourSlot.value.time_ticket_issue_end2.split(':')[0]
        businessSlot.value.time_ticket_issue_end2minute =
          getBusinessHourSlot.value.time_ticket_issue_end2.split(':')[1]
      }
      if (getBusinessHourSlot.value.time_business_start3 !== null) {
        businessSlot.value.flg_add_slot3_UI = true
        businessSlot.value.time_business_start3hour =
          getBusinessHourSlot.value.time_business_start3.split(':')[0]
        businessSlot.value.time_business_start3minute =
          getBusinessHourSlot.value.time_business_start3.split(':')[1]
        businessSlot.value.time_business_end3hour =
          getBusinessHourSlot.value.time_business_end3.split(':')[0]
        businessSlot.value.time_business_end3minute =
          getBusinessHourSlot.value.time_business_end3.split(':')[1]
        businessSlot.value.time_checkin_start3hour =
          getBusinessHourSlot.value.time_checkin_start3.split(':')[0]
        businessSlot.value.time_checkin_start3minute =
          getBusinessHourSlot.value.time_checkin_start3.split(':')[1]
        businessSlot.value.time_checkin_end3hour =
          getBusinessHourSlot.value.time_checkin_end3.split(':')[0]
        businessSlot.value.time_checkin_end3minute =
          getBusinessHourSlot.value.time_checkin_end3.split(':')[1]
        businessSlot.value.time_ticket_issue_start3hour =
          getBusinessHourSlot.value.time_ticket_issue_start3.split(':')[0]
        businessSlot.value.time_ticket_issue_start3minute =
          getBusinessHourSlot.value.time_ticket_issue_start3.split(':')[1]
        businessSlot.value.time_ticket_issue_end3hour =
          getBusinessHourSlot.value.time_ticket_issue_end3.split(':')[0]
        businessSlot.value.time_ticket_issue_end3minute =
          getBusinessHourSlot.value.time_ticket_issue_end3.split(':')[1]
      }
    }
  }
}
function assignPageData(data: any) {
  if (data) {
    for (let key in data) {
      businessForm[key] = data[key]
    }
  }
}
const closeModal = () => {
  emits('close')
}
onMounted(() => {
  if (props.data) {
    assignPageData(props.data)
  }
})
</script>
<template>
  <q-form @submit="submit">
    <MtModalHeader @close-modal="closeModal" :closeBtn="true">
      <q-toolbar-title class="text-grey-900 title2 bold">
        診療日 特別スケジュール
      </q-toolbar-title>
      <q-btn v-if="isEdit" flat round @click="openMenu" class="q-mx-sm">
        <q-icon name="more_horiz" size="xs" />
      </q-btn>
    </MtModalHeader>
    <q-card-section class="content q-px-lg">
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-lg-6 col-md-6 col-sm-12">
          <MtFormInputDate
            v-model:date="businessForm.datetime_business_day"
            type="date"
            label="対象日 *"
            required
            :rules="[aahValidations.validationRequired]"
            autofocus
            tabindex="1"
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12">
          <MtFormPullDown
            v-model:selected="businessForm.id_business_hour_slot"
            v-model:options="getAllBusinessHourSlots"
            @update:modelValue="handleBusinessHourSlot"
            required
            :rules="[aahValidations.validationSelection]"
            label="診療時間枠*"
            tabindex="2"
          />
        </div>
      </div>
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-12">
          <MtInputForm
            type="text"
            v-model="businessForm.memo_business_day"
            label="オーナー表示用：診療時間案内"
            autogrow
            tabindex="3"
          />
        </div>
      </div>
      <div v-if="businessSlot && businessForm.id_business_hour_slot" class="row q-col-gutter-md q-mb-md bg-grey-100 q-ma-md">
        <div class="col-12">
          <div class="row q-col-gutter-md">
            <div class="col-3">
              <MtInputForm
                type="text"
                v-model="businessSlot.name_business_hour"
                label="時間枠表示名 *"
                disable
                :rules="[aahValidations.validationRequired]"
              />
            </div>
          </div>
          <div class="row q-col-gutter-md q-mb-md">
            <div class="col-3">
              <MtFormInputNumber
                type="number"
                v-model:value="businessSlot.ticket_limit_upper1"
                disable
                label="整理券 発行上限数1"
              />
            </div>
          </div>
          <div class="row items-center q-col-gutter-md q-mb-sm">
            <div class="col-2">
              <MtFormPullDown
                label="診療開始時刻 1"
                v-model:selected="businessSlot.time_business_start1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="診療開始時刻 1"
                v-model:selected="businessSlot.time_business_start1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
            <span class="q-mx-md">～</span>
            <div class="col-2">
              <MtFormPullDown
                label="診療終了時刻 1"
                v-model:selected="businessSlot.time_business_end1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="診療終了時刻 1"
                v-model:selected="businessSlot.time_business_end1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
          </div>
          <div class="row items-center q-col-gutter-md q-mb-sm">
            <div class="col-2">
              <MtFormPullDown
                label="受付開始時刻 1"
                v-model:selected="businessSlot.time_checkin_start1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="受付開始時刻 1"
                v-model:selected="businessSlot.time_checkin_start1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
            <span class="q-mx-md">～</span>
            <div class="col-2">
              <MtFormPullDown
                label="受付終了時刻 1"
                v-model:selected="businessSlot.time_checkin_end1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="受付終了時刻 1"
                v-model:selected="businessSlot.time_checkin_end1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
          </div>
          <div class="row items-center q-col-gutter-md q-mb-md">
            <div class="col-2">
              <MtFormPullDown
                label="整理券発行開始時刻 1"
                v-model:selected="businessSlot.time_ticket_issue_start1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="整理券発行開始時刻 1"
                v-model:selected="businessSlot.time_ticket_issue_start1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
            <span class="q-mx-md">～</span>
            <div class="col-2">
              <MtFormPullDown
                label="整理券 発行終了時刻 1"
                v-model:selected="businessSlot.time_ticket_issue_end1hour"
                disable
                :options="timeHour"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>時</span>
            <div class="col-2">
              <MtFormPullDown
                label="整理券 発行終了時刻 1"
                v-model:selected="businessSlot.time_ticket_issue_end1minute"
                disable
                :options="timeMinute"
                :rules="[aahValidations.validationSelection]"
              />
            </div>
            <span>分</span>
          </div>
        
          <!-- <div class="row items-center q-col-gutter-md q-mb-sm">
            <div class="col-3 q-my-lg">
              <MtInputForm
                type="checkbox"
                :items="[{ label: '診療時間枠 2 の追加' }]"
                v-model="businessSlot.flg_add_slot2_UI"
                disable
              />
            </div>
          </div> -->
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn
          unelevated
          color="primary"
          tabindex="5"
          class="q-ml-md"
          type="submit"
        >
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
