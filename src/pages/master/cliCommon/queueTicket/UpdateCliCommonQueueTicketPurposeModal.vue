<script lang="ts" setup>
import { computed, onMounted, reactive, ref, h } from 'vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import mtUtils from '@/utils/mtUtils'
import { formatDate, getDaysBefore } from '@/utils/aahUtils'
import selectOptions from '@/utils/selectOptions'
import aahMessages from '@/utils/aahMessages'
import useCliCommonStore from '@/stores/cli-common'
import aahValidations from '@/utils/aahValidations'
import MtFormInputNumber from '@/components/form/MtFormInputNumber.vue'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'

import { 
  typeUnitBit, 
  typeOccupation, 
  typeProcessTime 
} from '@/utils/enum'
import { CliCommon } from '@/types/types'
import { storeToRefs } from 'pinia'
import MtFilterMultipleSelect from '@/components/form/MtFilterMultipleSelect.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'

type FormLabelsType = {
  codeCliLabel: string
  nameCliLabel: string
  flgFunc1CliLabel: string
  codeFunc1CliLabel: string,
  flgFunc2CliLabel?: string
  codeFunc2CliLabel?: string,
  text1CliLabel: string
  text2CliLabel?: string
}

const cliCommonStore = useCliCommonStore()
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)

const emits = defineEmits(['close'])
const props = withDefaults(
  defineProps<{
    modalTitle?: string
    commonObj?: CliCommon
    formLabels?: FormLabelsType
    code_cli_common?: number
    lastCD: number
  }>(),
  {
    modalTitle: '受付区分',
    commonObj: () => {
      return {} as CliCommon
    },
    formLabels: () => {
      return {
        codeCliLabel: '共通CD',
        nameCliLabel: '名称',
        flgFunc1CliLabel: '機能1',
        codeFunc1CliLabel: '受付CD',
        text1CliLabel: '機能名1',
        flgFunc2CliLabel: '機能2',
        codeFunc2CliLabel: '機能CD2',
        text2CliLabel: '機能名2'
      } as FormLabelsType
    },
    code_cli_common: 0,
    lastCD: 0
  }
)
const cliCommonFormCheckBox = ref([])
const cliCommonForm = reactive({
  id_cli_common: null,
  date_start: formatDate(new Date(1000, 1, 1), 'YYYY/MM/DD'),
  date_end: formatDate(new Date(9999, 1, 1), 'YYYY/MM/DD'),
  code_cli_common: props.code_cli_common,
  name_cli_common: '',
  // code_func1: {
  //   default: null,
  //   type: String | Number
  // },
  code_func1: null as null | string,
  code_func2: '',
  flg_func1: false,
  flg_func2: false,
  flg_etc1: false,
  flg_etc2: false,
  flg_etc3: false,
  memo_etc1: '',
  memo_etc2: '',
  memo_etc3: '',
  comment: '',
  text1: '',
  text2: '',
  code_clinic: JSON.parse(localStorage.getItem('clinic'))?.value,
  id_clinic: JSON.parse(localStorage.getItem('id_clinic')),
  display_order: 9999
})
const isEdit = ref(false)
const typeOcupationNew: any = ref([])
const typeOcupationNewDefault: any = reactive([])
const selectedTypeOccupations: any = ref([])
const allowFlagEtc1 = ref<boolean>(cliCommonForm.flg_etc1)


const assignPageData = (newData: any) => {
  if (newData?.id_cli_common) {
    for (let key in cliCommonForm) {
      cliCommonForm[key] = newData[key]
    }
  }
}

const closeModal = () => {
  emits('close')
}
const model = ref('')
const save = async () => {
  // create case

   if (isDuplicateCodeFunc1Computed.value && !isEdit.value) {
    await mtUtils.autoCloseAlert('番号が重複しています！');
    return
  }

  if (!cliCommonForm.flg_etc1) {
    cliCommonForm.code_func2 = ''
  }

  if(selectedTypeOccupations.value.length > 0) {
    selectedTypeOccupations.value = selectedTypeOccupations.value.map((s) => {
      if(typeof s == 'object'){
        return s.value
      }else{
        return s
      }
    })
    cliCommonForm.text1 = selectedTypeOccupations.value.join(',');
  }else{
    cliCommonForm.text1 = ""
  }

  if (isEdit.value) {
    const res = await mtUtils.callApi(
      selectOptions.reqMethod.PUT,
      `mst/clinic_common/${cliCommonForm.id_cli_common}`,
      { ...cliCommonForm }
    )
    if (res) {
      emits('close')
      await mtUtils.autoCloseAlert(aahMessages.success)
    }
    return
  }
  if (parseInt(cliCommonForm.code_func1) === props.lastCD) {
    return mtUtils.autoCloseAlert("番号が重複しています！")
  }
  const res = await mtUtils.callApi(
    selectOptions.reqMethod.POST,
    'mst/clinic_common',
    { ...cliCommonForm }
  )
  if (res) {
    await mtUtils.autoCloseAlert(aahMessages.success)
    emits('close')
  }
}

const isBitSet = (number: any, bitValue: any) => {
  return (number & bitValue) === bitValue
}

const deleteRecord = async () => {
  await mtUtils.confirm(aahMessages.delete_ask, aahMessages.delete).then((confirmation) => {
    if (confirmation) {
      cliCommonForm.date_end = getDaysBefore(1)
      save()
    }
  })
}

const isError = computed(() => {
  return !isEdit.value && parseInt(cliCommonForm.code_func1) === props.lastCD
})

const isDuplicateCodeFunc1Computed = computed(() => {
  return mtUtils.isDuplicateCodeFunc1(
    getCliCommonQTVisitPurposeList.value,
    cliCommonForm.code_func1,
    isEdit.value
  )
})

const prepareTypeOcc = () => {
  typeOcupationNew.value.length = 0
  typeOcupationNewDefault.length = 0
  typeOcupationNew.value = [... typeOccupation]
  typeOcupationNewDefault.push(... typeOccupation)
}

const renderColorPeek = (colorValue: string) => {
  return h(
    'div',
    {
      class: 'flex items-end full-height',
      style: { paddingBottom: '0px', marginTop: '4px' }
    },
    [
      h('div', {
        class: 'rounded-borders',
        style: {
          height: '15px',
          width: '15px',
          backgroundColor: colorValue
        }
      })
    ]
  )
}

const handleFlagEtc1 = (val: boolean) => {
  cliCommonForm.flg_etc1 =  allowFlagEtc1.value = val
}

onMounted(() => {
  cliCommonFormCheckBox.value = [...typeUnitBit]

  prepareTypeOcc()

  cliCommonForm.code_func1 = mtUtils.getNextCodeFunc1(
    getCliCommonQTVisitPurposeList.value
  ).toString()

  if (props.commonObj && props.commonObj.id_cli_common) {
    assignPageData(props.commonObj)
    isEdit.value = true
    if (props.commonObj.code_cli_common == 4) {
      cliCommonFormCheckBox.value = cliCommonFormCheckBox.value.map(
        (o: any) => ({
          ...o,
          checked: isBitSet(props.commonObj.code_func1, o.value)
        })
      )
      selectedTypeOccupations.value = typeOcupationNew.value.filter(i => i.value && props.commonObj.text1.split(',').includes(i.value.toString())) 
    }
  }
  if (props?.commonObj?.code_cli_common == '11') {
    cliCommonForm.flg_etc1 = true
  }
  handleFlagEtc1(cliCommonForm.flg_etc1)
})
</script>

<template>
  <q-form @submit="save">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        {{ modalTitle }}
      </q-toolbar-title>
    </MtModalHeader>
    <q-scroll-area class="modal-content">
      <q-card-section class="row q-col-gutter-md">
        <div class="col-lg-6 col-md-6 col-sm-6">
          <MtInputForm
            v-model="cliCommonForm.code_func1"
            autogrow
            :label="formLabels.codeFunc1CliLabel"
            type="text"
            :readonly="isEdit"
            :is-error="isDuplicateCodeFunc1Computed">
            <template #error>
              番号が重複しています！
            </template>
          </MtInputForm>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <MtInputForm
            v-model="cliCommonForm.name_cli_common"
            :label="formLabels.nameCliLabel"
            autofocus
            type="text"
            tabindex="1"
            :rules="[aahValidations.validationRequired]" />
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <MtFilterMultipleSelect
            v-model="selectedTypeOccupations"
            v-model:options="typeOcupationNew"
            :options-default="typeOcupationNewDefault"
            :outlined="false"
            label="対応職種" />
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <q-input v-model="cliCommonForm.text2" label="背景色" class="my-input">
            <template #label v-if="cliCommonForm.text2">
              <span class="row" style="column-gap: 5px">
                背景色
                <component :is="renderColorPeek(cliCommonForm.text2)" />
              </span>
            </template>
            <template v-slot:append>
              <q-icon name="colorize" class="cursor-pointer">
                <q-popup-proxy
                  cover
                  transition-show="scale"
                  transition-hide="scale">
                  <q-color v-model="cliCommonForm.text2" />
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <!-- <MtFormPullDown
            label="想定対応時間"
            v-model:selected="cliCommonForm.code_func2"
            :options="typeProcessTime" /> -->
          <div class="row gap-4">
            <div class="col-auto flex">
              <MtFormCheckBox label="呼出予想" @update:checked="handleFlagEtc1" :checked="cliCommonForm.flg_etc1" />
            </div>
            <div class="col">
              <MtFormInputNumber
                type="number"
                v-model:value="cliCommonForm.code_func2"
                label="デフォルト想定時間"
                class="col"
                v-if="allowFlagEtc1"
              />
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <MtInputForm
            v-model="cliCommonForm.display_order"
            label="表示順序（0~999; 小を上位表示）"
            type="text"
            tabindex="2" />
        </div>
        <div v-if="isEdit" class="col text-right">
          <q-btn flat class="text-darkred" @click="deleteRecord">
            <q-icon name="delete_forever" />
            削除する
          </q-btn>
        </div>
      </q-card-section>
    </q-scroll-area>
    <q-card-section class="q-bt bg-white">
      <div class="">
        <div class="text-center modal-btn">
          <q-btn
            class="bg-grey-100 text-grey-800"
            outline
            @click="closeModal()">
            <span>キャンセル</span>
          </q-btn>
          <q-btn class="q-ml-md" color="primary" tabindex="5" type="submit" unelevated>
            <span>保存</span>
          </q-btn>
        </div>
        <div></div>
      </div>
    </q-card-section>
  </q-form>
</template>

<style lang="scss" scoped>
.c-grid {
  display: grid;
  grid-template-columns: 120px auto 120px;
  flex-direction: column;
}

.first-item {
  max-width: 100px;
}

.q-item {
  min-height: auto !important;
  padding: 2px 0 !important;
}

.modal-content {
  height: 45vh;
}
</style>
