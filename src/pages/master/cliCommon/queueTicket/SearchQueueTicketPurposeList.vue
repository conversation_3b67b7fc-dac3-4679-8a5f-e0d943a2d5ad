<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import useCliCommonStore from '@/stores/cli-common'
import UpdateCliCommonQueueTicketPurposeModal from './UpdateCliCommonQueueTicketPurposeModal.vue'
import mtUtils from '@/utils/mtUtils'
import { storeToRefs } from 'pinia'
import { setPageTitle } from '@/utils/pageTitleHelper'
import useClinicStore from '@/stores/clinics'
import { isDateOutOfToday } from '@/utils/aahUtils'
import { typeOccupation } from '@/utils/enum'

const cliCommonStore = useCliCommonStore()
const clinicStore = useClinicStore()
const { getCliCommonQTVisitPurposeList } = storeToRefs(cliCommonStore)
const pagination = ref({ rowsPerPage: 0 })

const columns = [
  // {
  //   name: 'code_cli_common',
  //   label: 'CODE',
  //   field: 'code_cli_common',
  //   align: 'left',
  //   style: 'width: 100px'
  // },
  {
    name: '  ',
    label: ' ',
    field: ' ',
    align: ' '
  },
  {
    name: 'code_func1',
    label: '受付CD',
    field: 'code_func1',
    align: 'left'
  },
  {
    name: 'name_cli_common ',
    label: '受付区分',
    field: 'name_cli_common',
    align: 'left'
  },
  {
    name: 'text1 ',
    label: '対応職種',
    field: 'text1',
    align: 'left'
  },
  {
    name: 'flg_etc1',
    label: '呼出予想',
    field: 'flg_etc1',
    align: 'center'
  },
  {
    name: 'code_func2 ',
    label: 'デフォルト想定時間',
    field: 'code_func2',
    align: 'center'
  },
  {
    name: 'display_order',
    label: '表示順',
    field: 'display_order',
    align: 'center'
  }
]

const search = ref({
  id_clinic: JSON.parse(localStorage.getItem('id_clinic')!) as number,
  code_cli_common: 4,
  code_func1: 1,
  type_unit_bit: null
})

const cliCommonOptionList: any = ref([])

const destructOccupation = (joinedStrings: string) => {
  const occupations: string[] = joinedStrings.split('')
  console.log(occupations)
  return occupations.map( o => typeOccupation.find(to => to.value.toString() == o )?.label).filter( o => o)
}

const openAddModal = async (params = null) => {
  await mtUtils.smallPopup(UpdateCliCommonQueueTicketPurposeModal, {
    id_clinic: search.value.id_clinic,
    code_cli_common: search.value.code_cli_common,
    lastCD: parseInt(
      cliCommonOptionList.value[cliCommonOptionList.value.length - 1]?.code_func1
    )
  })
  init()
}

const onRowClick = async (row: any) => {
  await mtUtils.smallPopup(UpdateCliCommonQueueTicketPurposeModal, {
    commonObj: row,
    lastCD: parseInt(
      getCliCommonQTVisitPurposeList.value[cliCommonOptionList.value.length - 1].code_func1
    ),
    id_clinic: search.value.id_clinic
  })
  init()
}

const init = async () => {
  cliCommonOptionList.value = []
  const response = await cliCommonStore.fetchPreparationCliCommonList(
    {
      code_cli_common: [search.value.code_cli_common],
      id_clinic: [search.value.id_clinic]
    },
    true
  )
  if (response) {
    cliCommonOptionList.value = getCliCommonQTVisitPurposeList.value.filter((v: any) => v.id_clinic == search.value.id_clinic)
  }
}

onMounted(async () => {
  await clinicStore.fetchClinics()
  await init()

  setPageTitle('受付区分')
})
</script>

<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900">
          受付区分
        </q-toolbar-title>
        <div class="row">
          <div class="col-12">
            <div class="flex items-center">
              <form class="flex items-center no-wrap">
                <q-btn
                  class="q-ml-sm"
                  color="grey-800"
                  text-color="white"
                  unelevated
                  @click="openAddModal"
                >
                  <q-icon name="add" size="20px" />
                  受付区分
                </q-btn>
              </form>
            </div>
          </div>
        </div>
      </q-toolbar>
    </MtHeader>
    <div class="row items-center justify-between q-px-lg tableBox">
      <div class="body1 regular text-grey-700">
        表示：<span class="q-ml-sm"> {{ cliCommonOptionList.length }} 件</span>
      </div>
      <div class="caption1 regular text-danger">
        <q-icon name="warning" />
        受付CDの重複禁止、マスタデータの為、責任者のみ編集可能です。
      </div>
    </div>
    <q-table
      :columns="columns"
      :rows="cliCommonOptionList"
      :rowsBg="true"
      :style="{ height: 'calc(100dvh - 90px)' }"
      flat
      :rows-per-page-options="[0]"
      hide-bottom
      v-model:pagination="pagination"
    >
      <template v-slot:header="props">
        <q-tr :props="props" class="sticky-header">
          <q-th v-for="col in props.cols" :key="col.name" :props="props">
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>
      <template v-slot:body="props">
        <q-tr :props="props" class="cursor-pointer">
          <q-td
            v-for="(col, index) in props.cols"
            :style="col.style"
            @click="onRowClick(props.row)"
          >
            <div class="row full-width full-height items-center justify-center" v-if="col.field === 'flg_etc1'">
              <q-icon name="check" style="color:#16761a;" size="26px" v-if="props.row[col.field]"/>
            </div>
            <div class="row full-width full-height items-center" v-else-if="col.field === 'text1'">
              <q-chip color="grey-300" text-color="black"  v-for="(occupation) in destructOccupation(props.row[col.field])">
                {{ occupation }}
              </q-chip>
            </div>
            <div class="body1 regular text-grey-900 text-center" v-else-if="col.field === 'code_func2'">
              {{ props.row[col.field] }}
            </div>
            <div class="body1 regular text-grey-900" v-else>
              {{ props.row[col.field] }}
            </div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-page>
</template>

<style lang="scss" scoped>
.tableBox {
  margin-top: 20px;
}

.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}
</style>
