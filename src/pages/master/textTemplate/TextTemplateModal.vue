<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import MtModalHeader from '@/components/MtModalHeader.vue'
import useTextTemplateStore from '@/stores/text-template'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import OptionModal from '@/components/OptionModal.vue'
import { typeTextTemplate } from '@/utils/enum'
import MtCarousel from '@/components/MtCarousel.vue'
import { groupBy, orderBy, sortBy } from 'lodash'

const props = defineProps({
  singleOption: {
    type: Boolean,
    default: true
  },
  type_text_template: {
    type: Number,
    default: null
  }
})
const templateStore = useTextTemplateStore()
const emits = defineEmits(['close'])

const isEdit = ref(false)
const scrollArea = ref()
const titleTemplate = ref([])
const data = ref({
  type_text_template: null,
  flg_title: false,
  memo_template: '',
  img_file_path_template: null,
  display_order: null
})
const closeModal = () => {
  emits('close')
}

const scrollUpdate = async (value) => {
  const item = typeTextTemplate.find((item) => item.value === value)
  if(!item) {
    return
  }
  data.value.type_text_template = item.value
  moveToSelectedGroup(item)
  await templateStore.fetchTemplates({ type_text_template: item.value })
}

const moveToSelectedGroup = (item) => {
  let elements = []
  if(window.innerWidth < 1024) {
    elements = document.getElementsByClassName('mobile-group-'+ item.value) || []
  }else {
    elements = document.getElementsByClassName('group-'+ item.value) || []
  }
  Array.from(elements).forEach((element) => {
    const yOffset = -150; 
    const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

    window.scrollTo({
      top: y,
      behavior: 'smooth'
    });
  })
}

const getTitleTemplates = computed(() => {
  if (templateStore.getTemplates.length) {
    return templateStore.getTemplates?.filter((item) => item.flg_title)
  }
  return []
})

const getContentTemplates = computed(() => {
  const templates: any[] = []
  let currentParentId = ''
  sortBy(templateStore.getTemplates, 'display_order').forEach((item) => {
    if (item.flg_title) {
      currentParentId = item.id_text_template
    }
    templates.push({
      ...item,
      parentId: item.flg_title ? null : currentParentId
    })
  })

  return templates
})

const toggleTitleTemplate = (item) => {
  if (!scrollArea.value) return
  const index = getContentTemplates.value.findIndex((v) => v.id_text_template == item.id_text_template)
  if (index !== -1) {
    const targetEl = document.getElementById(`template-${index}`)
    const scrollTarget = scrollArea.value.getScrollTarget()
    if (!targetEl || !scrollTarget) return

    const topPosition = targetEl.offsetTop
    scrollArea.value.setScrollPosition('vertical', topPosition, 300)
  }
}
const useTemplate = (selectedData) => {
  if (props.singleOption) {
    const selectedOption = templateStore.getTemplates.find((item) => item.id_text_template == selectedData.id_text_template);

    if (selectedOption) {
      const templatesText = selectedOption?.memo_template; 
      
      if(templatesText){
        templateStore.setTemplateContent(templatesText)
        closeModal()
      }
    }
  } else {
    let selectedTemplates = templateStore.getTemplates.filter((item: any) => item.isSelected == true)

    if (selectedTemplates.length) {
      let templatesText = selectedTemplates.map((template: any) => { return template.memo_template }).join('')

      templateStore.setTemplateContent(templatesText)
    }
  }
  mtUtils.autoCloseAlert(aahMessages.success)
}
onMounted(async () => {
  if (props.type_text_template) {
    await scrollUpdate(props.type_text_template)
  }
})
</script>

<template>
  <q-form>
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold">
        テキストテンプレート
      </q-toolbar-title>
    </MtModalHeader>
    <q-card-section class="content q-px-xl">
      <q-card class="q-mb-md">
        <div class="collapsable-container q-pa-sm">
          <div class="carousel q-px-sm">
            <MtCarousel :options="typeTextTemplate" @update:model-value="scrollUpdate" v-model="data.type_text_template"  />
          </div>
        </div>
      </q-card>
      <div class="row q-col-gutter-md">
        <div class="col-3">
          <q-scroll-area style="height: calc(100vh - 290px)">
            <div v-if="getTitleTemplates" class="flex flex-wrap">
              <div class="q-ml-sm q-mb-md cursor-pointer q-py-sm q-px-md q-ba" @click="toggleTitleTemplate(item)" v-for="item in getTitleTemplates" :key="item.id_text_template">
                <span v-html="item.memo_template" />
              </div>
            </div>
          </q-scroll-area>
        </div>
        <div class="col-9">
          <q-scroll-area ref="scrollArea" style="height: calc(100vh - 290px)">
            <div class="row q-col-gutter-md">
              <template v-for="(item, index) in getContentTemplates" :key="index">
                <div v-if="!item.parentId" class="col-md-12 col-sm-12 col-xs-12 q-mb-sm" :id="`template-${index}`">
                  <div class="bg-grey-300 q-pa-md q-ba cursor-pointer" @click="useTemplate(item)">
                    <div class="ellipsis">
                      <span v-html="item.memo_template" />
                    </div>
                  </div>
                </div>
                <div v-else class="col-md-3 col-sm-4 col-xs-6 q-mb-sm">
                  <div class="q-pa-md q-ba cursor-pointer" @click="useTemplate(item)" style="height: 100px">
                    <div class="ellipsis-3-lines">
                      <span v-html="item.memo_template" />
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </q-scroll-area>
        </div>
      </div>
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn outline class="bg-grey-100 text-grey-800" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>
</template>
