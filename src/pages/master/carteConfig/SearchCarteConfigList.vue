<script setup lang="ts">
import { ref, onMounted } from 'vue'
import MtHeader from '@/components/layouts/MtHeader.vue'
import UpdateCarteConfigButtonNameModal from './UpdateCarteConfigParentButtonModal.vue'
import UpdateCarteConfigListModal from './UpdateCarteConfigChildListModal.vue'
import MtTable2 from '@/components/MtTable2.vue'
import mtUtils from '@/utils/mtUtils'
import { storeToRefs } from 'pinia'

import {
  CarteConfig,
} from '@/types/types'

import useCarteConfigStore from '@/stores/carteConfig'
import { sortBy } from 'lodash'
import useCliCommonStore from '@/stores/cli-common'

const carteConfigStore = useCarteConfigStore()
const cliCommonStore = useCliCommonStore()

const { getAllCarteConfig } = storeToRefs(carteConfigStore)

const labPrintCols = ref([]), labPrintRows = ref([])

const addParentRecord = () => {
  mtUtils.smallPopup(UpdateCarteConfigButtonNameModal, {
    callback: fetchCarteConfigRecords
  })
}

const addChildRecord = () => {
  mtUtils.mediumPopup(UpdateCarteConfigListModal, {
    callback: fetchCarteConfigRecords
  })  
}

const fetchCarteConfigRecords = async () => {
  labPrintRows.value.length = 0
  await carteConfigStore.fetchCarteConfig({ type_config_layer: 1 })
  labPrintRows.value.push(...getAllCarteConfig.value)
  labPrintRows.value = sortBy(labPrintRows.value, 'display_order')
}

const openParentRecord = (row: CarteConfig) => {
  mtUtils.smallPopup(UpdateCarteConfigButtonNameModal, {
    idCarteConfig: row.id_carte_config,
    carteConfig: row,
    callback: fetchCarteConfigRecords
  })
}

const openChildRecord = (row: CarteConfig) => {
  mtUtils.mediumPopup(UpdateCarteConfigListModal, {
    idCarteConfig: row.id_carte_config,
    callback: fetchCarteConfigRecords
  })
}

onMounted(async () => {
  await mtUtils.promiseAllWithLoader([
    fetchCarteConfigRecords(),
    cliCommonStore.fetchPreparationCliCommonList({ code_cli_common: [14] }),
  ])
})
</script>
<template>
  <q-page :style="{ 'min-height': 'unset !important' }">
    <MtHeader>
      <q-toolbar class="text-primary q-pa-none">
        <q-toolbar-title class="title2 bold text-grey-900 mobile-margin">
          検査結果 出力テンプレート
        </q-toolbar-title>
        <div class="row">
          <div class="col-12">
            <div class="flex items-center">
              <q-btn color="primary" class="q-mr-md" @click="addParentRecord" >
                <q-icon name="add" class="q-mr-sm"/>
                出力ボタン名
              </q-btn>
              <q-btn color="primary" @click="addChildRecord" >
                <q-icon name="add" class="q-mr-sm"/>
                出力検査項目
              </q-btn>
            </div>
          </div>
        </div>  
      </q-toolbar>
    </MtHeader>
    <div class="q-mt-md q-px-md">
      <MtTable2
        :columns="labPrintCols"
        :rows="labPrintRows"
        :rowsBg="true"
        :style="{ height: 'calc(100dvh - 90px)' }"
        flat
      >
        <template v-slot:row="{ row }">
          <td @click="openChildRecord(row)">
            <div class="flex justify-between">
              {{ row.label_button || '-' }}
              <div><q-icon size="20px" class="cursor-pointer" name="edit" @click.stop="openParentRecord(row)" /></div>
            </div>
          </td>
        </template>
      </MtTable2>
    </div>
  </q-page> 
</template>