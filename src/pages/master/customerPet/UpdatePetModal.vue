<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import MtInputForm from '@/components/form/MtInputForm.vue'
import MtFormInputDate from '@/components/form/MtFormInputDate.vue'
import MtFilterSelect from '@/components/MtFilterSelect.vue'
import usePetStore from '@/stores/pets'
import mtUtils from '@/utils/mtUtils'
import aahMessages from '@/utils/aahMessages'
import aahValidations from '@/utils/aahValidations'
import useCustomerStore from '@/stores/customers'
import InputEmployeeOptGroup from '@/components/form/InputEmployeeOptGroup.vue'
import MtFormInputText from '@/components/form/MtFormInputText.vue'
import { storeToRefs } from 'pinia'
import OptionModal from '@/components/OptionModal.vue'
import { imageResize } from '@/utils/helper'
import MtModalHeader from '@/components/MtModalHeader.vue'
import {
  blank,
  dateFormat,
  formatDate,
  formatJPPhoneNumber,
  getCurrentPetAge,
  getCustomerName,
  getDateToday,
  getFullPetName,
  getPetFirstNameOnly
} from '@/utils/aahUtils'
import MtFormCheckBox from '@/components/form/MtFormCheckBox.vue'
import MtFormPullDown from '@/components/form/MtFormPullDown.vue'
import MtTable2 from '@/components/MtTable2.vue'
import UpdatePetInsuranceModal from '@/pages/master/customerPet/UpdatePetInsuranceModal.vue'
import usePetInsuranceStore from '@/stores/pet-insurances'
import useInsuranceStore from '@/stores/insurances'
import useCommonStore from '@/stores/common'
import SelectPrintTemplate from './detail/SelectPrintTemplate.vue'
import ImageCropper from '@/components/ImageCropper.vue'
import {
  typeBlood,
  typeInsertedBodyPart,
  typePetExcludeReason,
  typePetGender,
  typeTitlePetCustomerUpdated
} from '@/utils/enum'
import { event_bus } from '@/utils/eventBus'
import useCliCommonStore from '@/stores/cli-common'
import dayjs from 'dayjs'
import AddTextTemplateModal from '@/pages/task/AddTextTemplateModal.vue'
import useModalStore from '@/stores/modal'
import ExtraConfirmationModal from '@/components/ExtraConfirmationModal.vue'
import selectOptions from '@/utils/selectOptions'
import MtPdfFiller, { HtmlFilledDataArray } from '@/components/MtPdfFiller.vue'
import useClinicStore from '@/stores/clinics'

const { getCommonTypeGeneralInsurerOptionList } = storeToRefs(useCommonStore())

const props = defineProps({
  idEmployeeDoctor: String,
  idEmployee: String,
  typeDiscRate: String,
  openInsuranceTab: Boolean,
  callBackClose: Function
})

// change data into petForm
const data = ref({
  id_customer: 0,
  code_pet: '',
  name_pet: '',
  name_kana_pet: '',
  type_title_pet_customer_updated: 1,
  name_pet_customer_updated: '',
  type_pet_gender: 10,
  type_animal: null,
  name_breed: '',
  id_cm_breed: '',
  id_cm_hair: '',
  name_hair_color: '',
  date_birth: null,
  flg_unknown_dob: false,
  type_blood: null,
  flg_transfusion_ok: false,
  memo_transfusion: null,
  pet_alert: '00000000',
  id_employee_doctor: '',
  memo_pet: '',
  memo_pet_info: '',
  image_path1: '',
  image_path2: '',
  id_employee_main_doctor: '',
  id_employee_main: '',
  flg_insurance_plan: true,
  memo_pet_by_customer: '',
  code_insurance: '',
  date_insurance_start: null,
  date_insurance_end: null,
  memo_insurance: '',
  flg_microchip: false,
  microchip_id: '',
  flg_deceased: false,
  datetime_deceased: null,
  flg_send_flower: false,
  date_send_flower: null,
  memo_send_flower: '',
  status_pet: null,
  flg_delete_by_customer: false,
  flg_existence: false,
  display_order: 0,
  flg_pet_excluded: false,
  date_excluded: null,
  flg_unlist: false,
  code_city_hall: '',
  datetime_licensed: null,
  license_id: '',
  date_last_visit: null,
  date_register: dayjs().format('YYYY/MM/DD'),
  id_cm_animal: null,
  type_disc_rate: null,
  id_disease_insurer_out1: null,
  id_disease_insurer_out2: null,
  id_disease_insurer_out3: null,
  memo_excluded: '',
  microchip_place: '',
  flg_allergy: false,
  flg_sideeffect: false,
  flg_dm: true,
  datetime_customer_delete: null
})

const flg_type_alert_ui = ref([
  false,
  false,
  false,
  false,
  false,
  false,
  false,
  false
])

const addExcludeReasonModal = ref(false)
const addMicrochipPlaceModal = ref(false)

const defaultEmployee = JSON.parse(localStorage.getItem('id_employee'))

const emits = defineEmits(['close'])
const cliCommonStore = useCliCommonStore()
const petStore = usePetStore()
const customerStore = useCustomerStore()
const petInsuranceStore = usePetInsuranceStore()
const insuranceStore = useInsuranceStore()
const { getCustomer, getPet } = storeToRefs(customerStore)
const { getPetInsurances } = storeToRefs(petInsuranceStore)
const { getInsurances } = storeToRefs(insuranceStore)

// Sort pet insurances by datetime_update, fallback to datetime_insert if datetime_update is falsy
const sortedPetInsurances = computed(() => {
  if (!getPetInsurances.value) return []
  
  return [...getPetInsurances.value].sort((a, b) => {
    const dateA = a.datetime_update || a.datetime_insert
    const dateB = b.datetime_update || b.datetime_insert
    
    // Sort in descending order (newest first)
    return new Date(dateB).getTime() - new Date(dateA).getTime()
  })
})
const {
  getCliCommonHairColorList,
  getCliCommonDiscountRateList,
  getCliCommonTypePetNatureList
} = storeToRefs(cliCommonStore)
const petImagePreview = ref()
const selectedBreedsDefault: any = reactive([])
const selectedBreeds: any = ref([])
const hairColorListDefault: any = reactive([])
const hairColorList: any = ref([])
const commonTypeAnimalOptionList = ref([])
const isLoading = ref(false)
const petBirthDuration = ref(null)
const discountRateList = ref<{ label: string; value: string }[]>([])

const isEdit = ref(false)
const isBreed = ref(false)
const tab = ref('tab1')
const f1_status = ref('unchanged')
const showDogFields = ref(false)
const blankField = (row: any) => (data.value = blank(data.value, row))

const petInsuranceColumns = [
  {
    name: 'id_cm_insurer',
    label: '保険会社',
    field: 'id_cm_insurer',
    align: 'left'
  },
  {
    name: 'id_insurance_plan',
    label: '保険プラン',
    field: 'id_insurance_plan',
    align: 'left'
  },
  {
    name: 'code_insurance',
    label: '証券番号',
    field: 'code_insurance',
    align: 'left'
  },
  {
    name: 'date_insurance',
    label: '保険期間',
    field: 'date_insurance',
    align: 'left'
  }
]

function checkedTypeAlertFlg(value: any, flg_index: any) {
  if (data.value.pet_alert === '' || data.value.pet_alert === null)
    data.value.pet_alert = '00000000'
  if (value) {
    data.value.pet_alert = replaceCharAtIndex(
      data.value.pet_alert,
      flg_index,
      '1'
    )
  } else {
    data.value.pet_alert = replaceCharAtIndex(
      data.value.pet_alert,
      flg_index,
      '0'
    )
  }
}

function replaceCharAtIndex(str: any, index: any, newChar: any) {
  if (index < 0 || index >= str.length) {
    return str // index is out of bounds
  }

  let chars = str.split('')
  chars[index] = newChar
  return chars.join('')
}

const onFilePicked = (e: any, type: any) => {
  const files = e.target.files
  const reader = new FileReader()
  reader.onload = (e) => {
    mtUtils.smallPopup(ImageCropper, { image: e.target.result })
  }
  reader.readAsDataURL(files[0])
}

const objectData = (obj: any) => {
  const formData = {} as any
  Object.entries(obj).forEach(([key]) => {
    if (
      getPet.value?.image_path2 !== null &&
      f1_status.value !== 'changed' &&
      f1_status.value !== 'removed'
    ) {
      // in update case if image not changed or removed then image_path2 key will not pass to BE
      if (key !== 'image_path2' && key !== 'thumbnail_path2') {
        formData[key] = obj[key]
      }
    } else {
      // in update case if there is no image or image changed else removed then image_path2 key will pass to BE
      formData[key] = obj[key]
    }
  })
  delete formData.image_path1
  return formData
}

const submit = async () => {
  if (data.value.datetime_deceased) {
    data.value.datetime_deceased = data.value.datetime_deceased + ' 00:00:00'
  }
  if (data.value.datetime_licensed) data.value.datetime_licensed = dateFormat(data.value.datetime_licensed, 'YYYY-MM-DD HH:mm:ss')
  if (data.value.date_register) {
    data.value.date_register =
      data.value.date_register
  }
  if(data.value.datetime_microchip) {
    data.value.datetime_microchip = dateFormat(data.value.datetime_microchip, 'YYYY-MM-DD HH:mm:ss')
  }
  if (getPet.value) {
    if(data.value.flg_dm && (data.value.flg_unlist || data.value.flg_deceased || data.value.flg_pet_excluded)) {
      const confirmation = await mtUtils.confirm(
        `除外、死亡、またはその他除外されているペットにDMを許可しますか？`,
        'DM許可',
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        true
      )
      if (!confirmation) {
        return
      }
    }
    isLoading.value = true
    const filteredData = objectData(data.value)

    delete filteredData.thumbnail_path1
    delete filteredData.thumbnail_path2
    delete filteredData.id_customer

    petStore
      .updatePet(data.value.id_pet, data.value.id_customer, filteredData)
      .then(async () => {
        await customerStore.selectCustomer(data.value.id_customer, true)
        customerStore.selectPet(data.value.id_pet)
        isLoading.value = false
        emits('close')
        mtUtils.autoCloseAlert(aahMessages.success)
      })
  } else {
    isLoading.value = true
    petStore
      .submitPet(data.value.id_customer, data.value)
      .then(async () => {
        await customerStore.selectCustomer(data.value.id_customer)
         isLoading.value = false
        emits('close')
        mtUtils.autoCloseAlert(aahMessages.success)
      })
      .catch((error) => {
         isLoading.value = false
        let errorJson = JSON.parse(error.request.response)
        mtUtils.autoCloseAlert('Error :\t' + errorJson.message)
      })
  }
}

const modalStore = useModalStore()

const openMenu = async () => {
  let menuOptions = [
  {
      title: '印刷',
      name: 'certificate_issuance',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    },
    {
      title: '削除する',
      name: 'delete',
      isChanged: false,
      attr: {
        class: null,
        clickable: true
      }
    }
  ]

  await mtUtils.littlePopup(OptionModal, { options: menuOptions })

  let selectedOption = menuOptions.find((i) => i.isChanged == true)

  if (selectedOption) {
    if (selectedOption.name == 'delete') {
      
      modalStore.open({
        component: ExtraConfirmationModal,
        data: {
          submitFn: async () => {
            modalStore.toggleLoading(true)
            petStore
              .destroyPet(data.value.id_pet, data.value.id_customer)
              .then(async () => {
                modalStore.close()
                props.callBackClose()
                await customerStore.selectCustomer(data.value.id_customer)
                emits('close')
                mtUtils.autoCloseAlert(aahMessages.success)
              })
              .finally(() => {
                modalStore.toggleLoading(false)
              })
          },
        }
      })
    } else if (selectedOption.name == 'certificate_issuance') {
      const customerData = getCustomer.value
      mtUtils.smallPopup(SelectPrintTemplate, { customerData: customerData })
    }
  }
}

const closeModal = () => {
  emits('close')
}

const selectingBreed = (value: any, update: boolean = false) => {
  isBreed.value = false
  selectedBreeds.value.length = 0
  selectedBreedsDefault.length = 0
  const animalType = useCommonStore().getCommonTypeAnimalOptionList.find(
    (breed: any) => breed.id_common == value
  )?.code_func1

  handleDogTypeFields(animalType)
  if (animalType) {
    useCommonStore().getCommonBreedOptionList.map((breed: any) => {
      if (breed.code_func1 == animalType) {
        selectedBreedsDefault.push({
          label: breed.name_common,
          value: breed.id_common,
          code_func1: breed.code_func1
        })
      }
    })
    selectedBreeds.value = [...selectedBreedsDefault]

    isBreed.value = true
    if (update) {
      data.value.id_cm_breed = ''
    }
  } else {
    data.value.id_cm_breed = ''
  }
}

const processPetAlertString = () => {
  const typePetAlerts = data.value.pet_alert.split('')
  typePetAlerts.forEach((value, index) => {
    if (value == '1') {
      flg_type_alert_ui.value[index] = true
    } else {
      flg_type_alert_ui.value[index] = false
    }
  })
}

const handleDogTypeFields = (value: any) => {
  const dogType = [2, '2', 11, '11', 12, '12', 13, '13']
  if (value && dogType.includes(value)) {
    showDogFields.value = true
  } else {
    showDogFields.value = false
  }
}

const getMicrochipPdfFormBytes = async () => {
  const response = await mtUtils.callApi( selectOptions.reqMethod.GET, 'mst/get-file-from-s3', {
    file_path: '1/1/1/master/pdf/マイクロチップ装着証明書.pdf'
  }) as any
  return response
}

const fillMicrochipData = async () => {
  const fontSize = '14'
  
  // Calculate Reiwa year and current date
  const getCurrentReiwaDate = () => {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() + 1 // getMonth() returns 0-11
    const currentDay = now.getDate()
    
    // Reiwa year = current year - 2018
    const reiwaYear = currentYear - 2018
    
    return {
      year: reiwaYear.toString(),
      month: currentMonth.toString(), // No leading zero
      day: currentDay.toString() // No leading zero
    }
  }
  
  const currentReiwaDate = getCurrentReiwaDate()
  const currentReiwaYearData = currentReiwaDate.year
  const currentReiwaMonthData = currentReiwaDate.month
  const currentReiwaDayData = currentReiwaDate.day
  
  const microchipIdData = data.value?.microchip_id
  const petNameData = (() => {
    const namePet = data.value?.name_pet
    const nameKanaPet = data.value?.name_kana_pet
    return `${namePet}${nameKanaPet ? ` ( ${nameKanaPet} )` : ''}`
  })()
  
  const petAnimalDataCodeFunc1 = commonTypeAnimalOptionList.value.find(
    (v: any) => v.value == data.value?.id_cm_animal )?.code_func1 ?? 0
  const petAnimalData = ['11', '12', '13', '2'].includes((petAnimalDataCodeFunc1).toString()) ? '✓' : ''
  const petBreedData = (() => {
    const breed = useCommonStore().getCommonBreedOptionList.find((breed: any) => breed.id_common == data.value?.id_cm_breed)
    return breed?.name_common ?? ''
  })()
  const petHairData = (() => {
    const hair = getCliCommonHairColorList.value.find((hair: any) => hair.value == data.value?.id_cm_hair)
    return hair?.name_cli_common ?? ''
  })()
  const petDobData = data.value?.flg_unknown_dob ? 'Unknown' : data.value?.date_birth
  const petGenderData = ['1', '4', '2', '5'].includes((data.value?.type_pet_gender).toString()) ? '✓' : ''
  const petBlankData = ''
  
  // Parse microchip date from datetime_microchip
  const parseMicrochipDate = () => {
    if (!data.value?.datetime_microchip) {
      return { year: '', month: '', day: '' }
    }

    try {
      let dateStr = data.value?.datetime_microchip
      // If contains time part, remove it
      if (dateStr.includes('T')) {
        dateStr = dateStr.split('T')[0]
      } else if (dateStr.includes(' ')) {
        dateStr = dateStr.split(' ')[0]
      }

      let year = '', month = '', day = ''
      if (dateStr.includes('/')) {
        [year, month, day] = dateStr.split('/')
      } else if (dateStr.includes('-')) {
        [year, month, day] = dateStr.split('-')
      }

      return {
        year: year || '',
        month: month ? parseInt(month, 10).toString() : '',
        day: day ? parseInt(day, 10).toString() : ''
      }
    } catch (error) {
      console.warn('Failed to parse microchip date:', data.value?.datetime_microchip, error)
      return { year: '', month: '', day: '' }
    }
  }

  const clinicData = useClinicStore().getClinic as any
  
  const microchipDate = parseMicrochipDate()
  const petMicrochipDateYearData = microchipDate.year
  const petMicrochipDateMonthData = microchipDate.month
  const petMicrochipDateDayData = microchipDate.day
  const clinicNameData = clinicData?.name_clinic_display
  const clinicZipCodeData = clinicData?.zip_code || ''
  const clinicAddressData = (() => {
    const prefecture = clinicData?.address_city
    const city = clinicData?.address_prefecture
    const other = clinicData?.address_other
    return `${prefecture}${city ? ` ${city}` : ''}${ other ? ` ${other}` : ''}`
  })()
  const clinicPhoneData = formatJPPhoneNumber(clinicData?.phone1 || clinicData?.phone2 || '')
  const clinicDirectorData = clinicData?.name_director || ''
  return {
    pdf: await getMicrochipPdfFormBytes(),
    data: [
      {
        top: '65',
        left: '447',
        size: 12,
        html: currentReiwaYearData ?? '',
        color: '#000000'
      },
      {
        top: '65',
        left: '487',
        size: 12,
        html: currentReiwaMonthData ?? '',
        color: '#000000'
      },
      {
        top: '65',
        left: '527',
        size: 12,
        html: currentReiwaDayData ?? '',
        color: '#000000'
      },
      {
        top: '184',
        left: '264',
        size: fontSize,
        html: microchipIdData ?? '',
        color: '#000000'
      },
      {
        top: '228',
        left: '264',
        size: fontSize,
        html: petNameData ?? '',
        color: '#000000',
      },
      {
        top: '266',
        left: ['11', '12', '13'].includes(petAnimalDataCodeFunc1.toString()) ? '271' : '408',
        size: '19',
        html: petAnimalData ?? '',
        color: '#000000'
      },
      {
        top: '301',
        left: '264',
        size: fontSize,
        html: petBreedData ?? '',
        color: '#000000'
      },
      {
        top: '337',
        left: '264',
        size: fontSize,
        html: petHairData ?? '',
        color: '#000000'
      },
      {
        top: '373',
        left: '264',
        size: fontSize,
        html: petDobData ?? '',
        color: '#000000'
      },
      {
        top: '413',
        left: ['1', '4'].includes((data.value?.type_pet_gender ?? 0).toString()) ? '271' : '408',
        size: '19',
        html: petGenderData ?? '',
        color: '#000000'
      },
      {
        top: '447',
        left: '264',
        size: fontSize,
        html: petBlankData ?? '',
        color: '#000000'
      },
      {
        top: '486',
        left: '265',
        size: fontSize,
        html: '',
        color: '#000000',
        bgColor: '#ffffff',
        width: '30'
      },
      {
        top: '486',
        left: '305',
        size: fontSize,
        html: petMicrochipDateYearData ?? '',
        color: '#000000'
      },
      {
        top: '486',
        left: '394',
        size: fontSize,
        html: petMicrochipDateMonthData ?? '',
        color: '#000000'
      },
      {
        top: '486',
        left: '461',
        size: fontSize,
        html: petMicrochipDateDayData ?? '',
        color: '#000000'
      },
      {
        top: '516',
        left: '264',
        size: fontSize,
        html: clinicNameData ?? '',
        color: '#000000',
        bgColor: '#ffffff',
        width: '280'
      },
      {
        top: '536',
        left: '264',
        size: fontSize,
        html: clinicZipCodeData ? `〒 ${clinicZipCodeData}` : '',
        color: '#000000'
      },
      {
        top: '553',
        left: '264',
        size: fontSize,
        html: clinicAddressData ?? '',
        color: '#000000'
      },
      {
        top: '587',
        left: '264',
        size: fontSize,
        html: clinicPhoneData ?? '',
        color: '#000000'
      },
      {
        top: '645',
        left: '274',
        size: fontSize,
        html: clinicDirectorData ?? '',
        color: '#000000'
      },
    ] as HtmlFilledDataArray
  }
}

const init = () => {
  if (getCustomer.value) {
    data.value.id_customer = getCustomer.value.id_customer
  }
  hairColorList.value.length = 0
  hairColorListDefault.length = 0
  hairColorList.value = [...getCliCommonHairColorList.value]
  hairColorListDefault.push(...getCliCommonHairColorList.value)

  discountRateList.value = getCliCommonDiscountRateList.value
    .filter((cli) => {
      return cli.code_cli_common === '7'
    })
    .map((obj) => ({
      label: obj.name_cli_common,
      value: obj.code_func1
    }))

  selectingBreed(data.value.id_cm_animal)
  // insuranceStore.fetchInsurers()
  // insuranceStore.fetchInsurances()
  // selectingTypeBlood(data.value.type_blood)
}

const removeImage = () => {
  data.value.image_path2 = ''
  petImagePreview.value = ''
  f1_status.value = 'removed'
}

const openPetInsuaranceModal = async () => {
  await mtUtils.mediumPopup(UpdatePetInsuranceModal, {
    id_pet: data.value.id_pet,
    pet_name: getPetFirstNameOnly(getPet.value),
    customer_name: getCustomerName(getCustomer.value),
    pet_birthday: getPet.value.date_birth
  })
}

const onRowPetInsuranceClick = async (row: any) => {
  await mtUtils.mediumPopup(UpdatePetInsuranceModal, {
    data: row,
    id_pet: data.value.id_pet
  })
}

const getInsurerName = (id: any) => {
  return getCommonTypeGeneralInsurerOptionList.value.find(
    (item: any) => item.id_common === id
  )?.name_common
}
const getInsurancePlanName = (value: any) =>
  getInsurances.value.find((i) => i.id_insurance_plan === value)
    ?.name_insurance_plan

const updateBirthDate = (value: any) => {
  if (value) petBirthDuration.value = getCurrentPetAge(data.value)
  else petBirthDuration.value = ''
}

/** 今日が start～end の範囲外なら true を返す */
const isExpired = (row: any): boolean => {
  const today = new Date()
  const start = new Date(row.date_insurance_start)
  const end   = new Date(row.date_insurance_end)
  return today < start || today > end
}

const selectDefaultEmployee = (key: string) => {
  data.value[key] = defaultEmployee
}

const toTodaysDate = () => {
  data.value.date_last_visit = getDateToday()
}

const onUpdateFlgExcluded = (value: boolean) => {
  if (!value) {
    data.value.memo_excluded = ''
    data.value.date_excluded = ''
    onUpdateFlagDmRelation()
    return
  }
  onUpdateFlagDmRelation()
  data.value.date_excluded = dayjs().format('YYYY/MM/DD')
  return
}

const handleUpdateExcludeReason = (value: string) => {
  data.value.memo_excluded = `${data.value.memo_excluded} ${value}`
}
const handleInsertBodyPart = (value: string) => {
  data.value.microchip_place = `${
    data.value.microchip_place ? data.value.microchip_place + ' ' : ''
  }${value}`
}

const onUpdateFlagDmRelation = (reflectWhenDmShouldBeTrue?: boolean) => {
  if(data.value.flg_pet_excluded || data.value.flg_deceased || data.value.flg_unlist) {
    data.value.flg_dm = false
  } else if(reflectWhenDmShouldBeTrue) {
    data.value.flg_dm = true
  }
}

const onUpdateFlgUnlist = (value: boolean) => {
  if (!value) {
    data.value.flg_pet_excluded = false
    data.value.memo_excluded = ''
    data.value.date_excluded = ''
    data.value.datetime_deceased = ''
    data.value.flg_deceased = false
    data.value.flg_send_flower = false
    data.value.date_send_flower = ''
    data.value.memo_send_flower = ''
    onUpdateFlagDmRelation()
    return
  }
  onUpdateFlagDmRelation()
  return
}

const onUpdateFlgDeceased = (value: boolean) => {
  if (!value) {
    data.value.datetime_deceased = ''
    data.value.flg_deceased = false
    data.value.flg_send_flower = false
    data.value.date_send_flower = ''
    data.value.memo_send_flower = ''
    onUpdateFlagDmRelation()
    return
  }
  data.value.datetime_deceased = dayjs().format('YYYY/MM/DD')
  onUpdateFlagDmRelation()
  return
}

const onUpdateFlgSendFlower = (value: boolean) => {
  if (!value) {
    data.value.date_send_flower = ''
    data.value.memo_send_flower = ''
    return
  }
  data.value.date_send_flower = dayjs().format('YYYY/MM/DD')
  return
}

event_bus.on('cropped-image', (image) => {
  imageResize(image.blob)
    .then((i) => {
      data.value.image_path2 = i
      petImagePreview.value = URL.createObjectURL(i)
      f1_status.value = 'changed'
    })
    .catch((error) => {
      console.error('Failed to resize image:', error)
    })
})

const showAlert = computed(() => {
  const codeFuncToInclude = [11, '11', 12, '12', 13, '13']
  const codeFunc = commonTypeAnimalOptionList.value.find(
    (v: any) => v.id_common == data.value.id_cm_animal
  )?.code_func1
  const today = dayjs()
  const diff = today.diff(data.value.date_birth, 'days')

  if (
    codeFuncToInclude.includes(codeFunc) &&
    diff > 90 &&
    !data.value.license_id
  ) {
    return true
  }
  return false
})

const excludeReasons = computed(() => {
  return typePetExcludeReason.map((t) => {
    return {
      ...t,
      title: t.label,
      flg_title: false,
      isSelected: false
    }
  })
})
const insertedBodyPart = computed(() => {
  return typeInsertedBodyPart.map((t) => {
    return {
      ...t,
      title: t.label,
      flg_title: false,
      isSelected: false
    }
  })
})

onMounted(async () => {
  let codeCommonList = [1, 2, 3, 10, 29]

  if (useCommonStore().commonDiscountRatesList.length === 0)
    codeCommonList.push(30)

  await mtUtils.promiseAllWithLoader([
    useCommonStore().fetchPreparationCommonList({
      code_common: codeCommonList
    }),
    useCliCommonStore().fetchPreparationCliCommonList({
      code_cli_common: [5]
    })
  ])

  commonTypeAnimalOptionList.value = [
    ...useCommonStore().getCommonTypeAnimalOptionList
  ].filter((data) => {
    return dayjs(data.date_end).isAfter(
      dayjs().subtract(1, 'days').format('YYYY/MM/DD')
    )
  })
  if (getPet.value?.id_pet) {
    isEdit.value = true
    data.value = JSON.parse(JSON.stringify(getPet.value))
    data.value.date_birth = data.value.date_birth
      ? formatDate(data.value.date_birth)
      : null
    data.value.datetime_deceased =
      data.value.flg_deceased && data.value.datetime_deceased
        ? formatDate(data.value.datetime_deceased)
        : null
    handleDogTypeFields(data.value.type_animal)
    petImagePreview.value = data.value?.image_path2 ?? data.value.image_path1
    processPetAlertString()
    petInsuranceStore.fetchPetInsurances({
      id_pet: data.value?.id_pet
    })
    petBirthDuration.value = getCurrentPetAge(data.value)

    if (
      commonTypeAnimalOptionList.value.find(
        (v: any) => v.id_common == data.value.id_cm_animal
      )?.code_func1 &&
      [2, '2', 11, '11', 12, '12', 13, '13'].includes(
        commonTypeAnimalOptionList.value.find(
          (v: any) => v.id_common == data.value.id_cm_animal
        )?.code_func1
      )
    ) {
      showDogFields.value = true
    }
    if (getPetInsurances.value.length > 0) data.value.flg_insurance_plan = true
    if (!data.value.date_register) {
      data.value.date_register = dayjs((data.value as typeof data.value & { datetime_insert: string }).datetime_insert).format('YYYY/MM/DD')
    }
  } else {
    isEdit.value = false

    const response = await mtUtils.callApi(selectOptions.reqMethod.GET, 'mst/get_pet_code_by_customer', {
      'id_customer': getCustomer.value.id_customer
    })
    
    data.value.type_disc_rate = props.typeDiscRate
    data.value.type_title_pet_customer_updated = 1
    data.value.code_pet = response?.code_pet ?? `${getCustomer.value?.code_customer}_${
      getCustomer.value?.pets?.length + 1
    }`
  }

  if (props.idEmployeeDoctor)
    data.value.id_employee_main_doctor = props.idEmployeeDoctor
  if (props.idEmployee) data.value.id_employee_main = props.idEmployee
  data.value.id_clinic = JSON.parse(localStorage.getItem('id_clinic'))
  init()

  if (props.openInsuranceTab) {
    tab.value = 'tab2'
  }
})

const updatePetTitle = (genderType: number) => {
  switch(genderType) {
    case 1:
    case 4:
      data.value.type_title_pet_customer_updated = 2
      break
    case 2:
    case 5:
      data.value.type_title_pet_customer_updated = 1
      break
    case 10:
      data.value.type_title_pet_customer_updated = 3
      break
    default:
      data.value.type_title_pet_customer_updated = 3
      break
  }
}
</script>

<template>
  <q-form @submit="submit">
    <MtModalHeader @closeModal="closeModal">
      <q-toolbar-title
        class="row no-wrap items-center text-grey-900 title2 bold"
      >
        <span v-if="isEdit">
          <span v-if="data.flg_unlist" class="remove-pet q-mr-sm">除外</span>
          <span v-if="data.flg_insurance_plan" class="light-green-chip"
            >保険</span
          >
          {{ getFullPetName(customerStore.getPet, customerStore.getCustomer) }}
        </span>
        <span v-else>ペット 新規登録</span>
        <q-tabs v-model="tab" class="tab-style2 q-ml-lg">
          <q-tab class="tabsBox" name="tab1" label="基本" @click.stop="scrollTo('tab1')" />
          <q-tab class="tabsBox" name="tab2" label="保険" @click.stop="scrollTo('tab2')" />
          <q-tab class="tabsBox" name="tab3" label="他" @click.stop="scrollTo('tab3')" />
        </q-tabs>
      </q-toolbar-title>
      <div v-if="isEdit" class="q-mr-lg">
        <!--<span class="q-pr-lg">
          <small class="text-grey-600 q-pr-sm">初診日 :</small>
          {{ data?.date_register }}
        </span>-->
        <span v-if="data?.date_last_visit">
          <small class="text-grey-600 q-pr-sm">最終来院日 :</small>
          {{ data?.date_last_visit }}
        </span>
      </div>
      <q-btn v-if="getPet" round flat @click="openMenu" class="q-mx-sm">
        <q-icon size="xs" name="more_horiz" />
      </q-btn>
    </MtModalHeader>
    <q-card-section class="content update-page-background q-px-lg">
      <div id="basic">
        <div class="row q-col-gutter-md q-mb-md">
          <div class="col-md-4 col-sm-12">
            <div class="card-layout card-box-white">
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-12 text-center">
                  <div v-if="petImagePreview" class="row no-wrap items-start responsive-square-250 q-mx-auto">
                    <q-img
                      :src="petImagePreview"
                      spinner-color="white"
                      ratio="1"
                      class="roundedImage cursor-pointer full-width full-height"
                    />
                    <q-badge
                      color="red"
                      class="badge-position cursor-pointer"
                      transparent
                      @click="removeImage"
                    >
                      <q-icon name="close" />
                    </q-badge>
                  </div>
                  <q-icon
                    v-else
                    @click="$refs.pet_image.click()"
                    name="add"
                    size="lg"
                    color="white"
                    class="bg-grey-300 roundedImage q-pa-xl cursor-pointer"
                  />
                  <input
                    type="file"
                    style="display: none"
                    ref="pet_image"
                    accept="image/*"
                    @change="onFilePicked($event, 'pet_image')"
                  />
                </div>                  
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.name_pet"
                    :isKatakana="false"
                    label="ペット名 *"
                    autofocus
                    tabindex="1"
                    required
                    input-class="text-bold-20px"
                    :rules="[aahValidations.validationRequired]"
                  />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.name_kana_pet"
                    label="カナ名 *"
                    tabindex="2"
                    required
                    :rules="[aahValidations.validationRequired]"
                  />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12">
                  <MtFormPullDown
                    type="selection"
                    label="ペット敬称"
                    v-model:selected="data.type_title_pet_customer_updated"
                    :options="typeTitlePetCustomerUpdated"
                    :rules="[aahValidations.validationRequired]"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtFormInputDate
                    type="date"
                    v-model:date="data.date_birth"
                    label="生年月日"
                    @update:date="updateBirthDate"
                    tabindex="10"
                  />
                </div>
                <div class="col-lg-8 col-md-6 col-sm-12" v-if="!data.flg_pet_excluded && !data.flg_deceased">
                  <MtFormCheckBox
                    label="推定"
                    v-model:checked="data.flg_unknown_dob"
                  />
                  <span class="text-bold-20px q-ml-lg">{{ petBirthDuration }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 col-sm-12">
            <div class="card-layout card-box-white">
              <div class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">基本</h4>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-lg-7 col-md-7 col-sm-12 flex items-center">
                  <small class="text-grey-500 q-mr-md">ペットCD</small>
                  <span class="code-pet">{{ data.code_pet }}</span>
                </div>
                <div class="col-lg-5 col-md-5 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.code_pet_ex"
                    label="外部ペットCD（旧カルテ番号）"
                  />
                </div>
              </div> 
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-md-4 col-sm-12">
                  <MtFormPullDown
                    v-model:options="commonTypeAnimalOptionList"
                    v-model:selected="data.id_cm_animal"
                    label="動物種別 *"
                    tabindex="5"
                    required
                    custom-option
                    @update:selected="selectingBreed($event, true)"
                  >
                    <template #selectedItem="{ slotProps }">
                      <q-item-label>
                        {{ slotProps.opt.label }}
                      </q-item-label>
                    </template>
                    <template #option="{ slotProps }">
                      <q-item v-bind="slotProps.itemProps">
                        <q-item-section>
                          <div class="flex items-center gap-4">
                            {{ slotProps.opt.label }}
                            <q-icon
                              v-if="slotProps.opt.text1"
                              name="circle"
                              size="16px"
                              :color="slotProps.opt.text1"
                              :style="{ color: slotProps.opt.text1 }"
                            />
                          </div>
                        </q-item-section>
                      </q-item>
                    </template>
                  </MtFormPullDown>
                </div>
                <div v-if="isBreed" class="col-md-8 col-sm-12">
                  <MtFilterSelect
                    :options-default="selectedBreedsDefault"
                    v-model:options="selectedBreeds"
                    v-model:selecting="data.id_cm_breed"
                    label="品種名"
                    tabindex="6"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-auto">
                  <MtInputForm
                    type="radio"
                    class="pet-gender q-mr-sm"
                    v-model="data.type_pet_gender"
                    :items="typePetGender"
                    tabindex="3"
                    required
                    :rules="[aahValidations.validationRequired]"
                    @update:model-value="updatePetTitle"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-md-6 col-sm-12">
                  <MtFilterSelect
                    :options-default="hairColorListDefault"
                    v-model:options="hairColorList"
                    v-model:selecting="data.id_cm_hair"
                    label="毛色"
                    tabindex="7"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-md-6 col-sm-12">
                  <InputEmployeeOptGroup
                    v-model:selected="data.id_employee_main_doctor"
                    type-occupation="doctor"
                    label="デフォルト 主治医"
                    show-select-default-employee
                    @update:select-default-employee="
                      selectDefaultEmployee('id_employee_main_doctor')
                    "
                    tabindex="25"
                  />
                </div>
                <div class="col-md-6 col-sm-12">
                  <InputEmployeeOptGroup
                    v-model:selected="data.id_employee_main"
                    label="デフォルト 主担当者"
                    show-select-default-employee
                    @update:select-default-employee="
                      selectDefaultEmployee('id_employee_main')
                    "
                    tabindex="26"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 col-sm-12">
            <div class="card-layout card-box-white">
              <div class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">性格・特徴</h4>
              </div>
              <div class="row q-col-gutter-md q-pb-sm">
                <div class="col-sm-12">
                  <div class="col flex">
                    <div
                      v-for="(
                        checkbox, index
                      ) in getCliCommonTypePetNatureList.filter((data) => {
                        return dayjs(data.date_end).isSame(dayjs('9999/12/31'))
                      })"
                      :key="`${index}-${checkbox.id_cli_common}`"
                      class="flex items-center q-mb-sm"
                      :style="{ opacity: flg_type_alert_ui[index] ? 1 : 0.2 }"
                    >
                      <q-btn
                        class="text-grey-900 relative-position"
                        :class="[`bg-${checkbox.text1}`]"
                        :style="{
                          'background-color': checkbox.text1,
                          width: '16px',
                          height: '16px'
                        }"
                        rounded
                        size="12px"
                      >
                        <MtFormCheckBox
                          v-model:checked="flg_type_alert_ui[index]"
                          @update:checked="
                            (value) => checkedTypeAlertFlg(value, index)
                          "
                          class="absolute pet-alert-checkbox"
                        />
                        <q-icon v-if="flg_type_alert_ui[index]" name="done" size="16px" />
                      </q-btn>
                      <span class="q-ml-sm q-mr-lg">{{ checkbox.label }}</span>
                    </div>
                  </div>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <q-chip  
                    outline
                    text-color="white"
                    clickable
                    @click="data.flg_allergy = !data.flg_allergy"
                    :class="data.flg_allergy
                      ? 'bg-red-9 text-white'
                      : 'grey-1 text-grey-6'"
                    class="q-mr-sm"
                  >
                    <span>{{ data.flg_allergy ? 'アレルギーあり' : 'アレルギーなし' }}</span>
                  </q-chip>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <q-chip  
                    outline
                    text-color="white"
                    clickable
                    @click="data.flg_sideeffect = !data.flg_sideeffect"
                    :class="data.flg_sideeffect
                      ? 'bg-red-9 text-white'
                      : 'grey-1 text-grey-6'"
                    class="q-mr-sm"
                  >
                    <span>{{ data.flg_sideeffect ? '副作用あり' : '副作用なし' }}</span>
                  </q-chip>
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.memo_pet"
                    label="基本メモ（RQページ表示）"
                    autogrow
                    tabindex="21"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.memo_pet_info"
                    label="特記事項"
                    autogrow
                    tabindex="22"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row q-col-gutter-md q-mb-md">
          <div class="col-md-4 col-sm-12">
            <div class="card-layout card-box-white">
              <div class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">追加情報</h4>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-sm-12">
                  <q-chip  
                    outline
                    text-color="white"
                    clickable
                    @click="data.flg_dm = !data.flg_dm"
                    :class="data.flg_dm
                      ? 'bg-blue-8 text-white'
                      : 'bg-red text-white'"
                    class="q-mr-sm"
                    :disable="(data.flg_pet_excluded || data.flg_deceased || data.flg_unlist)"
                  >
                    <span>{{ data.flg_dm ? 'DM 可' : 'DM 不可' }}</span>
                  </q-chip>
                  <!-- <MtFormCheckBox
                    :label="`DM対象に含める${ data.flg_pet_excluded || data.flg_deceased || data.flg_unlist ? '(他タブで管理してください)' : ''}`"
                    v-model:checked="data.flg_dm"
                    :disable="(data.flg_pet_excluded || data.flg_deceased || data.flg_unlist)"
                  /> -->
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtFormPullDown
                    v-model:selected="data.type_blood"
                    v-model:options="typeBlood"
                    label="血液型"
                    tabindex="11"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <q-chip  
                    outline
                    text-color="white"
                    clickable
                    @click="data.flg_transfusion_ok = !data.flg_transfusion_ok"
                    :class="data.flg_transfusion_ok
                      ? 'bg-blue-8 text-white'
                      : 'grey-1 text-grey-6'"
                    class="q-mr-sm"
                  >
                    <span>{{ data.flg_transfusion_ok ? '供血可能' : '供血なし' }}</span>
                  </q-chip>
                </div>
                <div class="col-md-8 col-sm-12" v-if="data.flg_transfusion_ok">
                  <MtInputForm
                    type="text"
                    v-model="data.memo_transfusion"
                    label="輸血/供血メモ"
                    autogrow
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-sm-12" v-if="isEdit">
                  <MtInputForm
                    type="text"
                    v-model="data.memo_pet_by_customer"
                    label="オーナーペットメモ（myVetty）"
                    autogrow
                    readonly
                    class="bg-pink-100"
                  />
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtFormPullDown
                    type="selection"
                    v-model="data.type_disc_rate"
                    :options="discountRateList"
                    label="デフォルト割引率"
                    tabindex="45"
                  />
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.display_order"
                    label="表示順"
                    tabindex="60"
                  />                    
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 col-sm-12">
            <div class="card-layout card-box-white">
              <div v-if="showAlert" class="q-mb-md">
                <small class="text-darkred">
                  <q-icon name="warning_amber" /> 鑑札番号が未登録です。
                </small>
              </div>
              <div class="q-mb-md items-center" v-if="showDogFields">
                <h4 class="text-white bg-grey-600 title4">狂犬病</h4>
              </div>
              <div class="row q-col-gutter-md q-mb-sm" v-if="showDogFields">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.license_id"
                    label="鑑札番号"
                    input-class="text-bold-20px"
                    tabindex="32"
                  />
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtFormInputDate
                    v-model:date="data.datetime_licensed"
                    label="鑑札登録日"
                    tabindex="31"
                  />
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtInputForm
                    type="text"
                    v-model="data.name_registered"
                    label="鑑札登録者"
                    tabindex="33"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-md" v-if="showDogFields">
                <div class="col-md-6 col-sm-12">
                  <MtFormPullDown
                    v-model:selected="data.code_city_hall"
                    label="保健所名/CD"
                    tabindex="30"
                    :options="
                      useCliCommonStore().getCliCommonPublicHealthCenterList.map(
                        (v) => ({
                          label: v?.code_func1 + ' ' + v?.name_cli_common,
                          value: v?.code_func1 + ' ' + v?.name_cli_common
                        })
                      )
                    "
                  />
                </div>
                <div class="col-md-6 col-sm-12">
                  <ComponentFormAddress
                    v-if="data.name_registered"
                    :form-data="data"
                  />
                </div>
              </div>
              <hr class="light" v-if="showDogFields" />
              <div class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">マイクロチップ（MC）</h4>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <q-chip  
                    outline
                    text-color="white"
                    clickable
                    @click="
                      data.flg_microchip = !data.flg_microchip;
                      if (!data.flg_microchip) blankField(['microchip_id','microchip_place','datetime_microchip']);
                    "
                    :class="data.flg_microchip
                      ? 'bg-blue-8 text-white'
                      : 'grey-1 text-grey-6'"
                    class="q-mr-sm"                      
                  >
                    <span>{{ data.flg_microchip ? 'MC装着済' : 'MC未対応' }}</span>
                  </q-chip>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtInputForm
                    v-if="data.flg_microchip"
                    type="text"
                    v-model="data.microchip_id"
                    label="MC登録番号"
                    tabindex="41"
                  />
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtFormInputDate
                    v-model:date="data.datetime_microchip"
                    label="MC装着日"
                    tabindex="43"
                    dense
                    type="date"
                    v-if="data.flg_microchip"
                  />
                </div>
              </div>
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtInputForm
                    v-if="data.flg_microchip"
                    type="text"
                    v-model="data.microchip_place"
                    label="挿入部位"
                    tabindex="42"
                  >
                    <template v-slot:append>
                      <q-btn flat dense>
                        <q-icon
                          name="add"
                          unelevated
                          @click="addMicrochipPlaceModal = true"
                        />
                      </q-btn>
                    </template>
                  </MtInputForm>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                  <MtPdfFiller
                    :fillData="fillMicrochipData"
                    action="print"
                    :fileName="`装着証明書_${data.name_pet}`"
                    containerClass="full-height"
                    v-if="data.flg_microchip"
                  >
                    <template #default="{ onExport, isLoading }">
                      <q-btn outline @click="onExport" size="sm" @touchstart="onExport" :loading="isLoading" :disabled="!data.microchip_id">
                        <q-icon name="print" class="text-grey-900 q-mr-xs" />
                        証明書
                      </q-btn>
                    </template>
                  </MtPdfFiller>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 col-sm-12" v-if="isEdit">
            <div class="card-layout card-box-white">
              <div class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">予防歴</h4>
                <span class="caption1 regular text-grey-500 q-ml-md">リクエスト表示の最終予防歴</span>
              </div>
              <div class="row q-col-gutter-md q-mb-md">
                <div class="col-md-6 col-sm-12" v-if="isEdit">
                  <MtFormInputDate
                    type="date"
                    v-model:date="data.date_last_visit"
                    label="最終来院日時"
                    tabindex="44"
                  />
                </div>
                <div v-if="isEdit" class="col-md-6 col-sm-12">
                  <q-btn size="10px" outline class="q-pa-none q-px-xs" @click="toTodaysDate()">
                    <q-icon name="arrow_left" />
                    今日
                  </q-btn>
                </div>
              </div>
              <hr class="light" />
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-md-6 col-sm-12">
                  <MtFormInputDate
                    type="date"
                    v-model:date="data.date_last_type_prev_4"
                    label="混合ワクチン 最終日"
                    tabindex="51"
                  />
                </div>
                <div class="col-md-6 col-sm-12">
                  <MtFormInputDate
                    type="date"
                    v-model:date="data.date_last_type_prev_2"
                    label="ノミダニ 最終日"
                    tabindex="52"
                  />
                </div>
                <div class="col-md-6 col-sm-12">
                  <MtFormInputDate
                    v-model:date="data.date_last_type_prev_3"
                    label="フィラリア 最終日"
                    tabindex="53"
                    dense
                    type="date"
                  />
                </div>
                <div class="col-md-6 col-sm-12">
                  <MtFormInputDate
                    v-model:date="data.date_last_type_prev_1"
                    label="狂犬病 最終日"
                    tabindex="54"
                    dense
                    type="date"
                  />
                </div>
                <div class="col-md-6 col-sm-12 q-mb-sm">
                  <MtFormInputDate
                    type="date"
                    v-model:date="data.date_last_type_prev_5"
                    label="健診 最終日"
                    tabindex="55"
                  />
                </div>
              </div>
              <hr class="light" />
              <div class="row q-col-gutter-md q-mb-sm">
                <div class="col-md-6 col-sm-12">
                  <MtFormInputDate
                    v-model:date="data.date_register"
                    label="カルテ作成日時"
                    tabindex="43"
                    dense
                    type="date"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Pet Insurance & Excluded Sections -->
      <div v-if="isEdit" class="row q-col-gutter-md q-mb-md">
        <div class="col-md-6 col-sm-12">
          <div class="card-layout card-box-white">
            <div class="col-12 flex items-center justify-between">
              <div id="insurance-section" class="q-mb-md items-center">
                <h4 class="text-white bg-grey-600 title4">ペット保険</h4>
              </div>
              <q-btn flat @click="openPetInsuaranceModal">
                <q-icon name="add" />
                保険
              </q-btn>
            </div>
            <div class="row q-col-gutter-md q-mb-sm">
              <div v-if="!isEdit" class="col-12">初回保存後にペット保険を追加できます。</div>
              <div v-else class="col-12">
                <MtFormCheckBox
                  label="ペット保険"
                  v-model:checked="data.flg_insurance_plan"
                  @update:checked="
                    blankField([
                      'code_insurance',
                      'date_insurance_start',
                      'date_insurance_end',
                      'memo_insurance'
                    ])
                  "
                />
              </div>
              <div class="col-lg-12 col-md-12 col-sm-12" v-if="data.flg_insurance_plan">
                <MtTable2
                  :columns="petInsuranceColumns"
                  :rows="sortedPetInsurances"
                  :rowsBg="true"
                  flat
                  no-data-message="登録がありません。"
                  no-result-message="該当のデータが見つかりませんでした"
                  show-pagination
                  :rows-per-page-options="[3]"
                  :pagination="{
                    page: 1,
                    rowsPerPage: 3
                  }"
                >
                  <template v-slot:row="{ row }">      
                    <td
                      v-for="(col, index) in petInsuranceColumns"
                      @click="onRowPetInsuranceClick(row)"
                      class="cursor-pointer"
                      :class="{'bg-red-1': isExpired(row)}"
                    >
                      <div v-if="col.field === 'id_cm_insurer'">
                        {{ getInsurerName(row.id_cm_insurer) }}
                      </div>
                      <div v-else-if="col.field === 'id_insurance_plan'">
                        {{ getInsurancePlanName(row.id_insurance_plan) }}
                      </div>
                      <div v-else-if="col.field === 'code_insurance'">
                        {{ row.code_insurance }}
                      </div>
                      <div v-else-if="col.field === 'date_insurance'">
                        {{ row.date_insurance_start }} ~
                        {{ row.date_insurance_end }}
                        <q-chip
                          v-if="isExpired(row)"
                          label="失効"
                          color="grey-4"
                          dense
                          flat
                          class="text-grey-700 q-ml-sm"
                        />
                      </div>
                    </td>           
                  </template>
                </MtTable2>
              </div>
            </div>
          </div>
        </div>
        <div id="exclude-section" class="col-md-6 col-sm-12">
          <div class="card-layout card-box-white">
            <div class="q-mb-md items-center">
              <h4 class="text-white bg-grey-600 title4">他</h4>
            </div>
            <div class="row q-col-gutter-md q-mb-sm">
              <div class="col-lg-4 col-md-6 col-sm-12">
                <q-chip  
                  outline
                  text-color="white"
                  clickable
                  @click="
                    data.flg_dm = !data.flg_dm"
                  :class="data.flg_dm
                    ? 'bg-blue-8 text-white'
                    : 'grey-1 text-grey-6'"
                  class="q-mr-sm"                      
                >
                  <span>{{ data.flg_dm ? 'DM含める' : 'DM含めない' }}</span>
                </q-chip>
              </div>
            </div>
            <div class="row q-col-gutter-md q-mb-sm">
              <div class="col-auto" v-if="isEdit">
                <q-chip  
                  outline
                  text-color="white"
                  clickable
                  @click="data.flg_unlist = !data.flg_unlist"
                  :class="data.flg_unlist
                    ? 'bg-red-8 text-white'
                    : 'grey-1 text-grey-6'"
                  class="q-mr-sm"
                  @update:checked="onUpdateFlgUnlist"
                >
                  <span>{{ data.flg_unlist ? 'システム除外中' : '除外なし' }}</span>
                </q-chip>
              </div>
              <div class="col" v-if="data.flg_unlist">
                <span class="caption1 text-darkred q-ml-md">
                  システム内の検索から除外
                </span>
              </div>
            </div>
            <div class="row items-center exclude-data q-mb-sm" v-if="isEdit && data.flg_unlist">
              <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="row q-col-gutter-md q-mb-sm">
                  <div class="col-lg-4 col-md-6 col-sm-12">
                    <q-chip  
                      outline
                      text-color="white"
                      clickable
                      @click="data.flg_deceased = !data.flg_deceased"
                      :class="data.flg_deceased
                        ? 'bg-red-8 text-white'
                        : 'grey-1 text-grey-6'"
                      class="q-mr-sm"
                      @update:checked="onUpdateFlgDeceased"
                    >
                      <span>{{ data.flg_deceased ? '死亡' : '死亡' }}</span>
                    </q-chip>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-12" v-if="data.flg_deceased">
                    <MtFormInputDate
                      v-model:date="data.datetime_deceased"
                      label="死亡日"
                    />
                  </div>
                </div>
                <div class="row q-col-gutter-md " v-if="data.flg_deceased">
                  <div class="col-lg-4 col-md-6 col-sm-12">
                    <q-chip  
                      outline
                      text-color="white"
                      clickable
                      @click="data.flg_send_flower = !data.flg_send_flower"
                      :class="data.flg_send_flower
                        ? 'bg-red-8 text-white'
                        : 'grey-1 text-grey-6'"
                      class="q-mr-sm"
                      @update:checked="onUpdateFlgSendFlower"
                    >
                      <span>{{ data.flg_send_flower ? '花送付' : '花未送付' }}</span>
                    </q-chip>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-12" v-if="data.flg_send_flower">
                      <MtFormInputDate
                        v-model:date="data.date_send_flower"
                        label="花送付日"
                      />
                  </div>
                  <div class="col-lg-12 col-md-12 col-sm-12" v-if="data.flg_deceased && data.flg_send_flower">
                    <MtInputForm
                      type="text"
                      v-model="data.memo_send_flower"
                      label="花送付メモ"
                      autogrow
                    />
                  </div>
                </div>
                <div class="row q-col-gutter-md " v-if="data.flg_unlist">
                  <div class="col-lg-4 col-md-6 col-sm-12">
                    <q-chip  
                      outline
                      text-color="white"
                      clickable
                      @click="data.flg_pet_excluded = !data.flg_pet_excluded"
                      :class="data.flg_pet_excluded
                        ? 'bg-red-8 text-white'
                        : 'grey-1 text-grey-6'"
                      class="q-mr-sm"
                      @update:checked="onUpdateFlgExcluded"
                    >
                      <span>{{ data.flg_pet_excluded ? 'その他除外' : 'その他除外' }}</span>
                    </q-chip>
                  </div>
                  <div class="col-lg-4 col-md-6 col-sm-12" v-if="data.flg_pet_excluded">
                    <MtFormInputText
                      v-model="data.memo_excluded"
                      class="q-pa-none q-mr-xs"
                      label="その他除外理由"
                    >
                      <template v-slot:append>
                        <q-btn flat dense>
                          <q-icon
                            name="add"
                            unelevated
                            @click="addExcludeReasonModal = true"
                          />
                        </q-btn>
                      </template>
                    </MtFormInputText>
                  </div>
                  <div class="col-md-6 col-sm-12" v-if="data.flg_deceased && data.flg_pet_excluded">
                    <MtFormInputDate
                      v-model:date="data.date_excluded"
                      label="その他除外日"
                    />
                  </div>
                </div>
              </div>
              <div class="col-lg-4 col-md-6 col-sm-12" v-if="isEdit">
                <q-chip  
                  outline
                  text-color="white"
                  clickable
                  @click="data.flg_delete_by_customer = !data.flg_delete_by_customer"
                  :class="data.flg_delete_by_customer
                    ? 'bg-red-8 text-white'
                    : 'grey-1 text-grey-6'"
                  class="q-mr-sm"
                  @update:checked="onUpdateFlgExcluded"
                >
                  <span>{{ data.flg_petflg_delete_by_customer_excluded ? 'オーナーによる除外報告' : 'オーナーによる除外' }}</span>
                </q-chip>
              </div>
            </div>
            <div class="row">
              <div class="col" v-if="data.datetime_customer_delete">
                {{ formatDate(data.datetime_customer_delete) }}
              </div>
            </div>
          </div>
        </div>
      </div>

     
        <!-- <q-tab-panel name="tab2">

        </q-tab-panel>
        <q-tab-panel name="tab3">
        </q-tab-panel> -->
    </q-card-section>
    <q-card-section class="q-bt bg-white">
      <div class="text-center modal-btn">
        <q-btn outline @click="closeModal()" class="bg-grey-100 text-grey-800">
          <span>キャンセル</span>
        </q-btn>
        <q-btn type="submit" color="primary" tabindex="100" :loading="isLoading" :disable="isLoading" class="q-ml-md">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-form>

  <AddTextTemplateModal
    v-model:value="addExcludeReasonModal"
    modelTitle="除外理由"
    :options="excludeReasons"
    :data="data"
    :single-option="true"
    @update:memo="handleUpdateExcludeReason"
  />
  <AddTextTemplateModal
    v-model:value="addMicrochipPlaceModal"
    modelTitle="挿入部位を選択"
    :options="insertedBodyPart"
    :data="data"
    :single-option="true"
    @update:memo="handleInsertBodyPart"
  />
</template>

<style lang="scss" scoped>
.badge-position {
  margin-bottom: -50px !important;
}
.pet-alert-checkbox {
  width: 100%;
  height: 100%;
  z-index: 999;
  opacity: 0;
}
.allergy-unchecked, .sideeffect-unchecked {
  background-color: #ffdce1; /* Light pink */
  color: black; /* Optional: Change text color */
}
.code-pet {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}
</style>
