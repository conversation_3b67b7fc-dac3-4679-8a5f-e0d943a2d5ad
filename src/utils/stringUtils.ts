/**
 * Extracts the leading alphabetical prefix (category code) from a string.
 *
 * This function is useful for parsing codes where a category is represented
 * by one or more letters at the start of the string, followed by numbers.
 *
 * Examples:
 * - "TRA100" => "TRA"
 * - "TR9"    => "TR"
 * - "A1"     => "A"
 * - "123ABC" => "" (no leading letters)
 *
 * @param input - The input string containing the category prefix and numbers.
 * @returns The leading letters from the beginning of the string.
 */
export function extractCategoryPrefix(input: string): string {
  const match = input.match(/^([A-Za-z]+)/);
  return match?.[1] || '';
}
