<template>
  <q-dialog v-model="flgShow" @hide="close" maximized persistent>
    <div class="countdown-overlay flex flex-center">
      <div ref="elm" class="countdown-container" />
      <div class="text-black text-h4 q-mt-md">
        「３２１終了」と喋ると録音を終了します。
      </div>

    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  popup?: Object
}>()

const flgShow = ref(true)
const emit = defineEmits(['close'])

function close() {
  emit('close')
}

defineExpose({
  flgShow
})
</script>

<style lang="scss" scoped>
.countdown-overlay {
  background: rgba(208, 208, 208, 0.5); // #D0D0D0 with 50% opacity
  width: 100vw;
  height: 100vh;
  flex-direction: column;
}

.countdown-container {
  width: 84px;  // 70% of 120px
  height: 84px; // 70% of 120px
  border-radius: 50%;
  background: var(--q-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
