<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Platform } from 'quasar'
import MtModalHeader from '@/components/MtModalHeader.vue'

interface Props {
  imagePath: string
  fileName?: string
}

const props = defineProps<Props>()
const emits = defineEmits(['close'])

const closeModal = () => emits('close')

const openImageInNewTab = () => {
  window.open(props.imagePath, '_blank')
}

// Image Tools Module - Exact same as UpdateClinicalFileModal
let defaultImageState = {
  rotation: 0,
  scale: 1,
  translateX: 0,
  translateY: 0,
  dragging: false,
  startX: 0,
  startY: 0,
  lastTouchDistance: 0,
  lastTouchX: 0,
  lastTouchY: 0,
  lastTapTime: 0
}

const imageState = reactive({...defaultImageState})
const maxBoundary = 360
const clamp = (value: number, min: number, max: number) => {
  return Math.min(Math.max(value, min), max)
}

const imageStyle = computed(() => {
  return {
    transform: `rotate(${imageState.rotation}deg) scale(${imageState.scale}) translate(${imageState.translateX}px, ${imageState.translateY}px)`,
    transition: imageState.dragging ? "none" : "transform 0.3s ease-in-out",
    cursor: imageState.scale > 1 ? (imageState.dragging ? 'grabbing' : 'grab') : 'zoom-in'
  }
})

const onClickZoomImage = () => {
  if (imageState.scale === 1) {
    imageState.scale = 3
  }
}

const zoomImage = (event: WheelEvent) => {
  event.preventDefault()
  let zoomFactor = event.deltaY < 0 ? 0.5 : -0.5
  let newScale = clamp(imageState.scale + zoomFactor, 1, 4) // Limit zoom between 1x and 4x
  imageState.scale = newScale

  // Reset panning if zoom is at 1
  if (imageState.scale === 1) {
    resetPosition()
  }

  applyBoundaries()
}

const applyBoundaries = () => {
  imageState.translateX = clamp(imageState.translateX, -maxBoundary, maxBoundary)
  imageState.translateY = clamp(imageState.translateY, -maxBoundary, maxBoundary)
}

const startPan = (event: MouseEvent) => {
  if (imageState.scale > 1) {
    imageState.dragging = true
    imageState.startX = event.clientX - imageState.translateX
    imageState.startY = event.clientY - imageState.translateY
    document.addEventListener("mousemove", panImage)
    document.addEventListener("mouseup", stopPan)
  }
}

const panImage = (event: MouseEvent) => {
  if (imageState.dragging) {
    imageState.translateX = clamp(event.clientX - imageState.startX, -maxBoundary, maxBoundary)
    imageState.translateY = clamp(event.clientY - imageState.startY, -maxBoundary, maxBoundary)
  }
}

const stopPan = () => {
  imageState.dragging = false
  document.removeEventListener("mousemove", panImage)
  document.removeEventListener("mouseup", stopPan)
}

const resetZoom = () => {
  imageState.scale = 1
  resetPosition()
}

const resetPosition = () => {
  imageState.translateX = 0
  imageState.translateY = 0
}

// Touch Gestures (Pinch Zoom & Dragging)
const DOUBLE_TAP_DELAY = 300

const touchStart = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    const currentTime = new Date().getTime()
    const timeDiff = currentTime - (imageState.lastTapTime || 0)

    if (timeDiff < DOUBLE_TAP_DELAY) {
      // Double tap detected
      resetPosition()
      imageState.lastTapTime = 0
      return
    }

    imageState.lastTapTime = currentTime

    if (imageState.scale > 1) {
      imageState.lastTouchX = event.touches[0].clientX - imageState.translateX
      imageState.lastTouchY = event.touches[0].clientY - imageState.translateY
    }
  } else if (event.touches.length === 2) {
    imageState.lastTouchDistance = getTouchDistance(event.touches)
  }
}

const touchMove = (event: TouchEvent) => {
  if (event.touches.length === 2) {
    const newDistance = getTouchDistance(event.touches)
    const zoomFactor = (newDistance - imageState.lastTouchDistance) * 0.005
    let newScale = Math.min(Math.max(imageState.scale + zoomFactor, 1), 4)
    imageState.scale = newScale
    imageState.lastTouchDistance = newDistance

    if (imageState.scale === 1) {
      resetPosition()
    }

    applyBoundaries()
  } else if (event.touches.length === 1 && imageState.scale > 1) {
    imageState.translateX = clamp(event.touches[0].clientX - imageState.lastTouchX, -maxBoundary, maxBoundary)
    imageState.translateY = clamp(event.touches[0].clientY - imageState.lastTouchY, -maxBoundary, maxBoundary)
  }
}

const touchEnd = () => {
  // Touch end logic if needed
}

const getTouchDistance = (touches: TouchList) => {
  const dx = touches[0].clientX - touches[1].clientX
  const dy = touches[0].clientY - touches[1].clientY
  return Math.sqrt(dx * dx + dy * dy)
}
</script>

<template>
  <section class="column bg-black full-height clinical-files">
    <MtModalHeader class="col-auto" style="display: none" @closeModal="closeModal">
      <q-toolbar-title class="text-grey-900 title2 bold q-pa-none">
        関連資料
      </q-toolbar-title>
    </MtModalHeader>

    <q-card-section class="col row gap-4 full-height text-white q-pa-none relative-position">
      <!-- Header controls -->
      <div
        class="row justify-between items-start q-pa-sm absolute-top"
        style="z-index: 2;"
      >
        <section class="col flex">
          <q-card
            style="
              background: rgba(255, 255, 255, 0.65);
              backdrop-filter: blur(4px);
              -webkit-backdrop-filter: blur(4px);
              border-radius: 8px;
            "
          >
            <q-card-section class="q-pa-sm" style="opacity: 1">
              <div class="text-body1 text-grey-900 ellipsis full-width">
                {{ fileName || 'Image Preview' }}
              </div>
            </q-card-section>
          </q-card>
        </section>
        <section class="col-auto text-right">
          <q-btn
            v-if="Platform.is.ipad"
            flat
            round
            @click="openImageInNewTab"
          >
            <q-icon size="xs" name="open_in_new" />
          </q-btn>
          <q-btn
            flat
            round
            @click="closeModal"
          >
            <q-icon size="xs" color="white" name="close" />
          </q-btn>
        </section>
      </div>

      <!-- Image Content -->
      <section
        class="col"
        style="height: 100%"
      >
        <div
          id="image-preview"
          class="col full-height row justify-center items-center relative-position img-wrapper"
          @click="onClickZoomImage"
          @wheel="zoomImage"
          @mousedown="startPan"
          @dblclick="resetZoom"
          @touchstart="touchStart"
          @touchmove="touchMove"
          @touchend="touchEnd"
        >
          <q-img
            :src="imagePath"
            fit="contain"
            spinner-color="white"
            class="modal-image col"
            :style="imageStyle"
          />
        </div>
      </section>

      <!-- Bottom Controls - Removed since we now have click-to-zoom like UpdateClinicalFileModal -->
      <div class="absolute-bottom-right q-pa-md">
        <div class="row items-center q-gutter-sm">
          <!-- Click image to zoom, double-click to reset, wheel to zoom in/out -->
        </div>
      </div>
    </q-card-section>
  </section>
</template>

<style lang="scss" scoped>
.clinical-files {
  border: 2px dashed transparent;
  transition: border-color 0.3s ease;
  position: relative;
  background-color: black;

  .q-btn {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.modal-image {
  transition: transform 0.3s ease;
  cursor: zoom-in;
  position: absolute;
  bottom: 0;
  height: 100%;
  width: 60%;
}

.img-wrapper {
  cursor: zoom-in;
}

.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-to, .fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>