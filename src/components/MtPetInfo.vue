<script setup lang="ts">
import { computed } from 'vue'
// Store
import useCommonStore from '@/stores/common'
import useCustomerStore from '@/stores/customers'
// Utils
import { formatDate, getCurrentPetAge, getFullPetName } from '@/utils/aahUtils'
import { typePetGender } from '@/utils/enum'

const props = defineProps({
  version: { type: String, default: 'v1' },
  onTitleClick: { type: Function, default: () => {}},
  enableTitleClickFlg: {type: Function, default: false}
})
const customerStore = useCustomerStore()
const customerInfo = computed(() => {
  return customerStore.getCustomer
})
const petInfo = computed(() => {
  return customerStore.getPet
})

const typeGenderName = (value: number) => {
  const petGender = typePetGender.find((v) => v.value === value)
  if (petGender) {
    return petGender.label
  }
  return '-'
}

const commonStore = useCommonStore()
const breedName = (value: number) => {
  const breedByIdCommon = commonStore.getCommonBreedOptionList.find(
    (v: { id_common: number }) => v.id_common === value
  )
  if (breedByIdCommon) {
    return breedByIdCommon.name_common
  }
  return '-'
}

const petName = computed(() => {
  if (
    petInfo.value &&
    customerInfo.value &&
    customerInfo.value.name_kana_family
  ) {
    const customerKanaFamily = customerInfo.value.name_kana_family
    const petKana = petInfo.value.name_kana_pet

    return `${customerKanaFamily || '-'} ${petKana || '-'}`
  }

  return ''
})

const petProfile = computed(() => {
  // petData または customerData が存在しない場合は空文字を返す
  if (!petInfo.value || !customerInfo.value) return ''

  // 性別情報が未設定なら「未設定」を、それ以外は typeGenderName() の結果を返す
  const petGender = petInfo.value.type_pet_gender
    ? typeGenderName(petInfo.value.type_pet_gender)
    : '性別？'

  // 品種情報が未設定なら「未設定」を、それ以外は breedName() の結果を返す
  const petBreed = petInfo.value.id_cm_breed
    ? breedName(petInfo.value.id_cm_breed)
    : '品種？'

  return `${petGender} / ${petBreed}`
})

const showPetDetailModal = () => {
  if(!props.enableTitleClickFlg) {
    return
  }
  props.onTitleClick()
}
</script>

<template>
  <section v-if="customerInfo && petInfo" class="text-grey-900 title2 bold">
    <template v-if="props.version === 'v1'">
      <div style="display: block">
        <div class="ellipsis pet-name">
          <span class="text-body2 text-grey-600">{{ petName }}</span>
          <span class="text-body2 text-grey-800 q-ml-md" v-if="petProfile">{{ petProfile }}</span>
        </div>
         <div class="ellipsis pet-kana-name">
          <span :class="props.enableTitleClickFlg ? 'link' : ''" @click.prevent="showPetDetailModal" v-if="customerInfo.name_kana_family">
            {{ getFullPetName(petInfo, customerInfo) }}
          </span>
          <span v-if="petInfo.date_birth" class="text-body2 gt-md q-ml-md">
            <q-icon name="cake" class="text-grey-600" />
            {{ ` ${formatDate(petInfo.date_birth)}` }}
          </span>
          <span v-if="!petInfo.flg_pet_excluded && !petInfo.flg_deceased && petInfo.date_birth" class="text-body2 q-ml-sm gt-md">
            ( {{ getCurrentPetAge(petInfo) }} )
          </span>
          <span v-if="!petInfo?.date_birth" class="text-body2 gt-md q-ml-sm">( 年齢？)</span>
        </div>
      </div>
    </template>
    <template v-else-if="props.version === 'v2'">
      <div style="display: block">
        <div>
          {{ petName }}
        </div>
        <div>
          <span :class="props.enableTitleClickFlg ? 'link' : ''" @click.prevent="showPetDetailModal" v-if="customerInfo.name_kana_family">
            {{ getFullPetName(petInfo, customerInfo) }}
          </span>
          <span class="text-body2 q-ml-sm text-bold">
            {{ petProfile }}
            <span>{{ ` / 誕 ${formatDate(petInfo.date_birth)}` }}</span>
          </span>
          <span
            v-if="!petInfo.flg_pet_excluded && !petInfo.flg_deceased"
            class="text-body2 q-ml-sm text-bold"
          >
            （年齢：{{ getCurrentPetAge(petInfo) }}）
          </span>
        </div>
      </div>
    </template>
  </section>
  <section v-if="!customerInfo || !petInfo">
    <div class="text-danger">再読み込みしてください</div>
  </section>
</template>

<style scoped>
.link {
  color: rgb(85, 26, 139);
  font-size: 17px;
  cursor: pointer;
}
</style>
