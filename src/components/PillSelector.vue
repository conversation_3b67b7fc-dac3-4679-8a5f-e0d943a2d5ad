<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'

import useCliCommonStore from '@/stores/cli-common'
import { decimalToFraction } from '@/utils/aahUtils'

const emits = defineEmits(['close', 'onUpdated'])
const closeModal = () => emits('close')

const props = defineProps({
  wholePill: Number | String,
  partialPill: Number | String
})


const partialPillOptions = ref([])

const MAX_WHOLE_PILL = 20

const currentWholePill = ref(props.wholePill || 0)
const currentPartialPill = ref(props.partialPill || 0)
const selectedPartialPill = ref('')

const wholePillOptions = new Array(6).fill().map((_, idx) => {
  return {
    label: String(idx),
    value: idx
  }
})

const groupedPartialPillOptions = computed(() => {
  const groupedOptions = { 2: [{ label: '0', value: 0 }]}
  partialPillOptions.value.map((partialPill: any) => {
    if(partialPill.label){
      const copyPartialPill = JSON.parse(JSON.stringify(partialPill))

      const [_, denominator] = copyPartialPill.label.trim().split('/')
      if(groupedOptions[denominator] !== undefined) {
        groupedOptions[denominator].push(partialPill)
      }
      else groupedOptions[denominator] = [partialPill]
    }
  })
  return groupedOptions
})

const updateWholePill = (mode: 'increase' | 'decrease') => {
  if(mode == 'increase' && currentWholePill.value < MAX_WHOLE_PILL) {
    currentWholePill.value++
  }
  else if(mode == 'decrease' && currentWholePill.value > 0) {
    currentWholePill.value--
  }
}

const updatePartialPill = (mode: 'increase' | 'decrease') => {
  const currentPartialPillIndex = partialPillOptions.value.findIndex((pill) => approximatelyEqual(pill.value, currentPartialPill.value))
  if(currentPartialPillIndex > -1) {
    if (mode == 'increase' && currentPartialPillIndex < (partialPillOptions.value.length - 1)) {
      currentPartialPill.value = partialPillOptions.value[currentPartialPillIndex + 1].value
    }
    else if(mode == 'decrease' && currentPartialPillIndex > 0) {
      currentPartialPill.value = partialPillOptions.value[currentPartialPillIndex - 1].value
    }
  }
}

const updatePillOptions = () => {
  const values = {
    wholePill: currentWholePill.value,
    partialPill: currentPartialPill.value
  }
  emits('onUpdated', values)
}

const clearPillValues = () => {
  currentWholePill.value = currentPartialPill.value = 0
}

const approximatelyEqual = (a, b, epsilon = 0.001) => {
  return Math.abs(a - b) < epsilon;
}

watch(currentPartialPill, (value) => {
  let partialPill = partialPillOptions.value.find((pill) => approximatelyEqual(pill.value, value))
  selectedPartialPill.value = partialPill?.label || 0
})


onMounted(async () => {
  await useCliCommonStore().fetchPreparationCliCommonList({ code_cli_common: [24] }, true)

  useCliCommonStore().getCliCommonPillDivisionList.map((pill: any) => {
    if (pill.flg_func1) {
      partialPillOptions.value.push({
        label: pill.name_cli_common,
        value: pill.code_func1
      })
    }
  })

  let partialPill = partialPillOptions.value.find((pill) => approximatelyEqual(pill.value, currentPartialPill.value))
  selectedPartialPill.value = partialPill?.label || (currentPartialPill.value ? decimalToFraction(currentPartialPill.value) : 0)
})

</script>
<template>
<q-card style="width: 800px; height: 100%; max-height: calc(100vh - 110px); max-width: 80vw;">
  <div class="flex justify-center items-center text-center gap-5 relative-position q-my-sm">
    <span class="text-grey-700 pill-info">1回</span>
    <div class="flex column justify-center items-center gap-3">
      <div class="triangle up cursor-pointer" @click="updateWholePill('increase')"></div>
      <span class="whole-pill">{{currentWholePill}}</span>
      <div class="triangle down cursor-pointer" @click="updateWholePill('decrease')"></div>
    </div>
    <div style="font-size: 20px">+</div>
    <div class="flex column justify-center items-center gap-3">
      <div class="triangle up cursor-pointer" @click="updatePartialPill('increase')"></div>
      <span class="partial-pill">{{selectedPartialPill}}</span>
      <div class="triangle down cursor-pointer" @click="updatePartialPill('decrease')"></div>
    </div>
    <div class="text-grey-700 pill-info">錠</div>
    <div class="text-blue cursor-pointer" @click="clearPillValues">クリア</div>
    <div class="flex justify-end absolute close-modal">
      <q-btn flat round @click="closeModal">
        <q-icon size="xs" name="close" />
      </q-btn>
    </div>
  </div>
  <q-separator />
  <div class="row q-col-gutter-md">
    <div class="col-6 q-pa-md q-mt-md" style="border-right: 1px solid #ccc;">
      <div class="text-center text-grey-500">錠剤分割なし</div>
      <div class="wholepill-grid q-mt-md">
        <template v-for="(wholePill, idx) in wholePillOptions" :key="idx">
          <div class="flex justify-center">
            <q-btn @click="currentWholePill = wholePill.value" class="wholepill-btn bg-grey-200">{{wholePill.label || 0}}</q-btn>
          </div>
        </template>
      </div>
    </div>
    <div class="col-6 q-mt-md">
      <div class="text-center text-grey-500">錠剤分割あり</div>
      <div class="partial-pill-container">
        <template v-for="(partialPillGroup, idx) in Object.keys(groupedPartialPillOptions)" :key="idx">
            <div class="flex gap-5 q-pa-sm" :class="idx % 2 !== 0 ? 'bg-grey-200' : ''">
            <template v-for="partialPill in groupedPartialPillOptions[partialPillGroup]" :key="partialPill.value">
              <span class="cursor-pointer text-black partial-pill-label" @click="()=>{
                currentPartialPill = 0
                currentPartialPill = partialPill.value
              }">{{ partialPill.label }}</span>
            </template>
            </div>
        </template>
      </div>
    </div>
  </div>
  <q-card-section class="q-bt bg-white">
    <div class="text-center modal-btn">
      <q-btn 
        outline 
        class="bg-grey-100 text-grey-800" 
        @click="closeModal"
      >
        <span>キャンセル</span>
      </q-btn>
      <q-btn
        class="q-ml-md"
        color="primary"
        @click="updatePillOptions"
        unelevated
      >
        <span>適用</span>
      </q-btn>
      </div>
    </q-card-section>
  </q-card>
</template>
<style lang="scss" scoped>
.close-modal {
  right: 0;
  top: 0;
}
.pill-info {
  font-size: 24px;
}
.whole-pill, .partial-pill {
  font-size: 36px;
  font-weight: bold;
  display: inline-block;
  min-width: 50px;
}
.wholepill-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding-left: 14px;
  gap: 15px;
  overflow: auto;
  .wholepill-btn {
    border: 1px solid $grey-500;
    height: 70px;
    width: 70px;
    :deep(.q-btn__content) {
      font-size: 26px;
    }
  }
}
.partial-pill-container {
  height: calc(100vh - 110px - 230px);
  overflow: auto;
  .partial-pill-label {
    font-size: 26px;
  }
}
.triangle {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  &.up {
    border-bottom: 12px solid $grey-700;
  }
   &.down {
    border-top: 12px solid $grey-700;
  }
}

</style>