<script setup lang="ts">
import MtPetInfoLabel from '@/components/customers/MtPetInfoLabel.vue'
import useQueueTicketStore from '@/stores/queue_ticket'
import { PetType } from '@/types/types'
import { computed, onMounted, ref } from 'vue';

const queueTicketStore = useQueueTicketStore()
const props = defineProps<{
  petOptions: PetType[]
}>()
const emits = defineEmits(['close'])
const closeModal = () => {
  emits('close')
}

const storeSelectedPets = computed(() => queueTicketStore.getSelectedQtPets)
const selectedPets = ref<number[]>([])

// Check if pet is already selected
const isSelected = (pet: PetType): boolean => {
  return selectedPets.value?.some(p => p === pet.id_pet)
}
// Toggle selection (select or unselect)
const toggleSelect = (pet: PetType) => {
  if (isSelected(pet)) {
    selectedPets.value = selectedPets.value.filter(p => p !== pet.id_pet) // remove
  } else {
    selectedPets.value.push(pet.id_pet) // add
  }
}
const onSaveSelectedPets = () => {
  queueTicketStore.setSelectedQtPets(selectedPets.value)
  closeModal()
}

const init = () => {
  if (storeSelectedPets.value?.length) {
    selectedPets.value = [...storeSelectedPets.value]
  }
}

onMounted(() => {
  init()
})
</script>

<template>
  <q-card>
    <q-card-section class="q-bb q-py-none">
      <q-toolbar class="q-px-none">
        <q-toolbar-title class="text-grey-900 title2 bold">ペット選択</q-toolbar-title>
        <q-btn flat round dense icon="close" @click="closeModal" />
      </q-toolbar>
    </q-card-section>
    <q-card-section v-if="props.petOptions">
      <div class="text-grey-800 q-mb-md">対象ペットを選択してください。</div>
      <div class="row gap-4">
        <div
          v-for="pet in props.petOptions"
          :key="`${pet.code_pet}-item`"
          :class="['pet__box', isSelected(pet) ? 'pet__box--selected' : '']"
          @click="toggleSelect(pet)"
        >
          <MtPetInfoLabel :pet="pet" is-clickable />
        </div>
      </div>
    </q-card-section>
    <q-card-section>
      <div class="row justify-center gap-4">
        <q-btn outline class="bg-grey-100 text-grey-800 col-3" @click="closeModal()">
          <span>キャンセル</span>
        </q-btn>
        <q-btn ref="submitBtn" unelevated color="primary" class="col-3" :tabindex="100" @click="onSaveSelectedPets">
          <span>保存</span>
        </q-btn>
      </div>
    </q-card-section>
  </q-card>
</template>

<style scoped lang="scss">
.pet__box {
  width: calc(25% - 16px);
  padding: 16px;
  border-radius: 3px;
  background-color: #e0e0e0;
  border: 1px solid #e0e0e0;
  &--selected {
    background-color: #fffde5;
    border: 3px solid #dac651;
  }
}
</style>
