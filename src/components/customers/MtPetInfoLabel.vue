<script setup lang="ts">
import { getPetImageUrl, handleImageError } from '@/utils/aahUtils'
import { getTypeAnimalColor } from '@/utils/customersUtils'

const props = withDefaults(
  defineProps<{
    pet: {
      name_pet?: string
      name_kana_pet?: string
      id_cm_animal: number
      code_pet?: number
    }
    isClickable?: boolean
    showPetImage?: boolean
    hides?: Array<'avatar' | 'name' | 'kanaName' | 'codePet' | 'animalColor'>
  }>(),
  {
    showPetImage: true,
    hides: () => []
  }
)
</script>

<template>
  <div class="avatar-container">
    <template v-if="props.showPetImage && !hides.includes('avatar')">
      <img
        v-if="props.pet"
        :src="getPetImageUrl(props.pet)"
        @error="handleImageError($event, props.pet)"
        :alt="props.pet.name_pet"
        class="image-responsive"
        loading="lazy"
      />
      <div v-else class="default bg-grey-300" />
    </template>
    <div class="column gap-1">
      <span class="caption1 bold text-grey-800" v-if="!hides.includes('codePet')">
        <slot name="codePet">
          {{ props.pet.code_pet }}
        </slot>
      </span>
      <span class="caption2 regular text-grey-700" style="line-height: 14px;" v-if="!hides.includes('kanaName')">
        <slot name="kanaFullname">
          {{ props.pet.name_kana_pet }}
        </slot>
      </span>
      <span :class="{ 'text-blue text-bold': isClickable }" style="line-height: 14px;" v-if="!hides.includes('name') || !hides.includes('animalColor')">
        <slot name="fullname" v-if="!hides.includes('name')">
          {{ props.pet.name_pet }}
        </slot>
        <slot name="typeAnimalColor" v-if="!hides.includes('animalColor')">
          <q-icon
            size="10px"
            name="circle"
            class="q-ml-xs"
            :color="getTypeAnimalColor(props.pet?.id_cm_animal)"
            :style="{ color: getTypeAnimalColor(props.pet?.id_cm_animal) }"
          />
        </slot>
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.avatar-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
</style>
