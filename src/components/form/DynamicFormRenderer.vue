<template>
  <div class="dynamic-form-renderer">
    <div v-for="(field, index) in formFields" :key="index" class="q-mb-md">
      <div class="field-container">
        <div class="q-mb-sm text-subtitle1">
          {{ field.label }}
          <span v-if="field.required" class="text-negative">*</span>
        </div>
        
        <!-- Text input -->
        <q-input
          v-if="field.type === 'text'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
        />
        
        <!-- Textarea input -->
        <q-input
          v-else-if="field.type === 'textarea'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
          type="textarea"
          autogrow
        />
        
        <!-- Number input -->
        <q-input
          v-else-if="field.type === 'number'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
          type="number"
        />
        
        <!-- Date input -->
        <q-input
          v-else-if="field.type === 'date'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
          type="date"
        />
        
        <!-- Time input -->
        <q-input
          v-else-if="field.type === 'time'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
          type="time"
        />
        
        <!-- Select dropdown -->
        <q-select
          v-else-if="field.type === 'select'"
          v-model="fieldValues[field.key]"
          outlined
          :label="field.label"
          :required="field.required"
          :name="field.key"
          :options="getOptions(field)"
          emit-value
          map-options
        />
        
        <!-- Radio buttons -->
        <div v-else-if="field.type === 'radio'" class="q-mt-sm">
          <q-option-group
            v-model="fieldValues[field.key]"
            :options="getOptions(field)"
            type="radio"
            :required="field.required"
            :name="field.key"
          />
        </div>
        
        <!-- Checkboxes -->
        <div v-else-if="field.type === 'checkbox'" class="q-mt-sm">
          <q-option-group
            v-model="fieldValues[field.key]"
            :options="getOptions(field)"
            type="checkbox"
            :required="field.required"
            :name="field.key"
            multiple
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue';
import { FormField, FormFieldOption, FormValues } from '../../types/formTypes';

export default defineComponent({
  name: 'DynamicFormRenderer',
  props: {
    formFields: {
      type: Array as () => FormField[],
      default: () => []
    },
    modelValue: {
      type: Object as () => FormValues,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const fieldValues = ref<FormValues>({});

    // Initialize fieldValues based on formFields
    const initializeFieldValues = () => {
      const values: FormValues = {};
      
      props.formFields.forEach(field => {
        // If the value already exists in the model, keep it
        if (props.modelValue && props.modelValue[field.key] !== undefined) {
          values[field.key] = props.modelValue[field.key];
        } else {
          // Otherwise set default values based on field type
          switch (field.type) {
            case 'checkbox':
              values[field.key] = [];
              break;
            case 'radio':
            case 'select':
              values[field.key] = field.options && field.options.length > 0 ? field.options[0].value : '';
              break;
            default:
              values[field.key] = '';
          }
        }
      });
      
      fieldValues.value = values;
      emit('update:modelValue', values);
    };

    // Watch for changes in formFields and update fieldValues
    watch(() => props.formFields, () => {
      initializeFieldValues();
    }, { deep: true, immediate: true });

    watch(fieldValues, (newValues) => {
      emit('update:modelValue', newValues);
    }, { deep: true });

    // Helper function to format options for q-select and q-option-group
    const getOptions = (field: FormField) => {
      if (!field.options || !Array.isArray(field.options)) {
        return [];
      }
      return field.options.map(opt => ({
        label: opt.label,
        value: opt.value
      }));
    };

    return {
      fieldValues,
      getOptions
    };
  }
});
</script>

<style scoped>
.dynamic-form-renderer {
  width: 100%;
}
.field-container {
  padding: 8px;
}
</style> 