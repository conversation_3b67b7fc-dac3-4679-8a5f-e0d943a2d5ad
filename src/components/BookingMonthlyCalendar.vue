<script lang="ts" setup>
import dayjs from '@/boot/dayjs'
import { QCalendarMonth, Timestamp } from '@quasar/quasar-ui-qcalendar'
import { computed, ref, watch, onMounted } from 'vue'
import { Dictionary, find, groupBy, map } from 'lodash'
import { Dayjs } from 'dayjs'
import useCommonStore from '@/stores/common'
import { storeToRefs } from 'pinia'
import axios from 'axios'

interface Holiday {
  date: string
  localName: string
  name: string
  countryCode: string
  fixed: boolean
  global: boolean
  counties: string[] | null
  launchYear: number | null
  types: string[]
}

interface BookingMonthlyCalendarProps {
  selectedMonth: Dayjs
  bookingDaySlotList: any
  weeklyScheduleData?: any
  enableHolidays?: boolean
}

const props = defineProps<BookingMonthlyCalendarProps>()
const emit = defineEmits(['update:enableHolidays'])

const calendar = ref()
const currentMonth = ref(props.selectedMonth.clone())
const holidays = ref<Holiday[]>([])
const isLoadingHolidays = ref(false)
const holidayError = ref<string | null>(null)
const enableHolidaysLocal = ref(props.enableHolidays || false)

// Watch for month changes to fetch holidays for the new month
watch(currentMonth, () => {
  if (enableHolidaysLocal.value) {
    fetchHolidays()
  }
})

// Fetch holidays for the current month's year
const fetchHolidays = async () => {
  if (!enableHolidaysLocal.value) return

  const year = currentMonth.value.year()
  isLoadingHolidays.value = true
  holidayError.value = null

  try {
    const response = await axios.get(`https://date.nager.at/api/v3/PublicHolidays/${year}/JP`)
    holidays.value = response.data
  } catch (error) {
    console.error('Error fetching holidays:', error)
    holidayError.value = '祝日の取得中にエラーが発生しました'
  } finally {
    isLoadingHolidays.value = false
  }
}

// Check if a date is a holiday
const isHoliday = (date: string) => {
  return holidays.value.some((holiday) => holiday.date === date)
}

// Get holiday name if the date is a holiday
const getHolidayName = (date: string) => {
  const holiday = holidays.value.find((holiday) => holiday.date === date)
  return holiday ? holiday.localName : null
}

const getDayName = (timestamp: Timestamp) => {
  return dayjs(timestamp.date).format('dd')
}

const selectedDate = computed(() => {
  return currentMonth.value.format('YYYY-MM-DD')
})

const formattedMonth = computed(() => {
  return currentMonth.value.format('YYYY年 M月')
})

// Navigation methods
const goToPreviousMonth = () => {
  currentMonth.value = currentMonth.value.subtract(1, 'month')
}

const goToNextMonth = () => {
  currentMonth.value = currentMonth.value.add(1, 'month')
}

const goToCurrentMonth = () => {
  currentMonth.value = dayjs()
}

// Map weekday types to day numbers (0-6)
const dayMap: Record<string, number> = {
  sunday: 0,
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
  holiday: 99
}

// Helper function to get time slots from weeklyScheduleData
const getTimeSlotsFromWeeklyData = (dayOfWeek: number, date: string) => {
  // Check if it's a holiday first
  if (enableHolidaysLocal.value && isHoliday(date)) {
    const holidayData = props.weeklyScheduleData?.find((day: any) => day.id === 'holiday')
    if (holidayData && holidayData.timeRanges && holidayData.timeRanges.length > 0) {
      return holidayData.timeRanges.map((timeRange: any) => ({
        business_start: timeRange.from + ':00',
        business_end: timeRange.to + ':00',
        slot_max: timeRange.slot_max
      }))
    }
  }

  // Map day of week to the id in weeklyScheduleData
  let dayId: string = 'common'
  switch (dayOfWeek) {
    case 0:
      dayId = 'sunday'
      break
    case 1:
      dayId = 'monday'
      break
    case 2:
      dayId = 'tuesday'
      break
    case 3:
      dayId = 'wednesday'
      break
    case 4:
      dayId = 'thursday'
      break
    case 5:
      dayId = 'friday'
      break
    case 6:
      dayId = 'saturday'
      break
  }

  // Find the day data from weeklyScheduleData
  const dayData = props.weeklyScheduleData?.find((day: any) => day.id === dayId)
  if (!dayData || !dayData.timeRanges || dayData.timeRanges.length === 0) {
    return []
  }

  // Convert the timeRanges to the format needed
  return dayData.timeRanges.map((timeRange: any) => ({
    business_start: timeRange.from + ':00',
    business_end: timeRange.to + ':00',
    slot_max: timeRange.slot_max
  }))
}

const getSlotsByDate = (date: string) => {
  // Convert date string to Date object
  const dateObj = dayjs(date)
  const dayOfWeek = dateObj.day() // 0 = Sunday, 1 = Monday, etc.
  const formattedDate = dateObj.format('YYYY-MM-DD')
  const holidayName = enableHolidaysLocal.value ? getHolidayName(formattedDate) : null
  const isDateHoliday = !!holidayName

  let timeSlots = []

  // If weeklyScheduleData is provided, use it
  if (props.weeklyScheduleData) {
    const slotsFromWeekly = getTimeSlotsFromWeeklyData(dayOfWeek, formattedDate)
    if (slotsFromWeekly.length > 0) {
      timeSlots.push(...slotsFromWeekly)
    } else {
      // If no day-specific slots, check for common slots
      const commonSlots = getTimeSlotsFromWeeklyData(-1, formattedDate) // -1 for common
      if (commonSlots.length > 0) {
        timeSlots.push(...commonSlots)
      }
    }
  } else {
    // Original implementation using bookingDaySlotList
    const rawTimeSlots = []

    // 1. Check for special day slots
    if (props.bookingDaySlotList?.special_day_list) {
      const specialSlots = props.bookingDaySlotList.special_day_list.filter(
        (slot: any) => slot.date_booking_special === formattedDate
      )
      if (specialSlots.length > 0) {
        rawTimeSlots.push(
          ...specialSlots.map((specialSlot: any) => ({
            business_start: specialSlot.time_bookable_start,
            business_end: specialSlot.time_bookable_end,
            slot_max: specialSlot.slot_max
          }))
        )
      }
    }

    // 2. Check for day-specific slots
    const slotsByDay = [
      props.bookingDaySlotList?.sunday_slots,
      props.bookingDaySlotList?.monday_slots,
      props.bookingDaySlotList?.tuesday_slots,
      props.bookingDaySlotList?.wednesday_slots,
      props.bookingDaySlotList?.thursday_slots,
      props.bookingDaySlotList?.friday_slots,
      props.bookingDaySlotList?.saturday_slots
    ]

    if (slotsByDay[dayOfWeek] && slotsByDay[dayOfWeek].length > 0) {
      rawTimeSlots.push(
        ...slotsByDay[dayOfWeek].map((slot: any) => ({
          business_start: slot.time_bookable_start,
          business_end: slot.time_bookable_end,
          slot_max: slot.slot_max
        }))
      )
    }

    // 3. If no specific slots found, use common slots
    if (rawTimeSlots.length === 0 && props.bookingDaySlotList?.common_slots?.length > 0) {
      rawTimeSlots.push(
        ...props.bookingDaySlotList.common_slots.map((slot: any) => ({
          business_start: slot.time_bookable_start,
          business_end: slot.time_bookable_end,
          slot_max: slot.slot_max
        }))
      )
    }

    // Merge continuous time slots
    if (rawTimeSlots.length > 0) {
      // Convert directly to the expected format without merging
      timeSlots = rawTimeSlots.map((slot) => ({
        business_start: slot.business_start,
        business_end: slot.business_end,
        slot_max: slot.slot_max
      }))
    }
  }

  return {
    timeSlots,
    isToday: dayjs().format('YYYY-MM-DD') === formattedDate,
    isSaturday: dayOfWeek === 6,
    isSunday: dayOfWeek === 0,
    isHoliday: isDateHoliday,
    holidayName,
    isClosed: timeSlots.length === 0
  }
}

// Toggle holidays display
const toggleHolidays = (value: boolean) => {
  enableHolidaysLocal.value = value
  emit('update:enableHolidays', value)
  if (value) {
    fetchHolidays()
  }
}

const commonStore = useCommonStore()
const { getCommonTypeServiceOptionList } = storeToRefs(commonStore)

// Fetch holidays on component mount
onMounted(() => {
  if (enableHolidaysLocal.value) {
    fetchHolidays()
  }
})
</script>

<template>
  <div class="calendar-container">
    <!-- Calendar Navigation Header -->
    <div class="flex q-mt-md q-pb-md navigation-info row">
      <div class="items-center row">
        <q-btn flat dense icon="chevron_left" @click="goToPreviousMonth" />
        <div class="q-mx-md text-subtitle1">{{ formattedMonth }}</div>
        <q-btn flat dense icon="chevron_right" @click="goToNextMonth" />
        <q-btn flat dense no-caps label="今月" color="primary" class="q-ml-sm" @click="goToCurrentMonth" />
      </div>
      <div class="q-mt-sm q-ml-md text-subtitle2">myVettyの予約可能時間として表示されます。</div>
      <div class="q-ml-auto">
        <q-toggle v-model="enableHolidaysLocal" label="祝日表示" @update:model-value="toggleHolidays" />
      </div>
    </div>

    <!-- Loading indicator for holidays -->
    <div v-if="isLoadingHolidays" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" /> 祝日データを読み込み中...
    </div>

    <!-- Error message -->
    <div v-if="holidayError" class="text-negative text-center q-pa-md">
      {{ holidayError }}
    </div>

    <!-- Calendar Component -->
    <QCalendarMonth
      ref="calendar"
      v-model="selectedDate"
      animated
      bordered
      locale="ja"
      cell-width="50px"
      no-active-date
    >
      <template #day="{ scope: { timestamp } }">
        <div class="day-column">
          <div
            class="text-center day-header"
            :class="{
              'is-today': getSlotsByDate(timestamp.date)?.isToday,
              'is-holiday': getSlotsByDate(timestamp.date)?.isHoliday && enableHolidaysLocal
            }"
          >
            <div
              class="day-name"
              :class="{
                'is-closed': getSlotsByDate(timestamp.date)?.isClosed,
                'is-saturday': getSlotsByDate(timestamp.date)?.isSaturday,
                'is-sunday': getSlotsByDate(timestamp.date)?.isSunday,
                'is-holiday': getSlotsByDate(timestamp.date)?.isHoliday && enableHolidaysLocal
              }"
            >
              {{ timestamp.day }} ({{ getDayName(timestamp) }})
              <div v-if="getSlotsByDate(timestamp.date)?.holidayName && enableHolidaysLocal" class="holiday-name">
                {{ getSlotsByDate(timestamp.date)?.holidayName }}
              </div>
              <div class="day-status" :class="{ 'is-closed': getSlotsByDate(timestamp.date)?.isClosed }">
                <template v-if="getSlotsByDate(timestamp.date)?.isClosed"> 予約不可 </template>
                <template v-else> 予約可能 </template>
              </div>
            </div>
            <div class="day-time-slots">
              <div
                class="body2 regular"
                v-for="(timeSlot, index) in getSlotsByDate(timestamp.date).timeSlots"
                :key="index"
              >
                <span class="caption-1">
                  {{ index + 1 }}: {{ timeSlot.business_start.slice(0, -3) }} ~
                  {{ timeSlot.business_end.slice(0, -3) }}
                  <template v-if="timeSlot.slot_max"> ({{ timeSlot.slot_max }}) </template>
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </QCalendarMonth>
  </div>
</template>

<style scoped lang="scss">
.calendar-container {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
}

.calendar-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.calendar-title {
  font-weight: 500;
}

.day {
  &-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &-header {
    display: flex;
    border-bottom: 3px solid #e0e0e0;
    width: 100%;
    padding: 8px;
    min-height: 100px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &-content {
    max-height: 188px;
    min-height: 188px;
    overflow-y: auto;
    flex-shrink: 0;
    width: 100%;
    padding: 8px;
  }

  &-name {
    font-size: 16px;
    font-weight: 700;
    display: flex;
    flex-direction: row;
    align-items: end;
    gap: 4px;
  }

  &-status {
    font-size: 12px;
    font-weight: 400;
  }
}

.holiday-name {
  font-size: 12px;
  font-weight: 400;
  margin-top: 4px;
}

.is-closed {
  //   color: var(--q-positive);
}

.is-saturday {
  color: $blue;
}

.is-sunday {
  color: var(--q-negative);
}

.is-holiday {
  color: var(--q-negative) !important;
}

.is-today {
  color: $white !important;
  background-color: $green-200;
  font-size: 12px;
}

.navigation-info {
  background-color: #fff;
}

:deep(.q-calendar-month__day--label__wrapper) {
  display: none !important;
}
</style>
