<template>
  <div class="weekly-schedule-container">
    <!-- Week Table -->
    <div class="weekly-schedule-table">
      <div class="schedule-grid">
        <!-- Header Row with Delete Button if enabled -->
        <div v-for="day in scheduleData" :key="day.id" class="schedule-header-cell" :class="day.headerClass">
          <div class="flex items-center justify-center cursor-pointer" @click="$emit('header-click', day)">
            <span>{{ day.label }}</span>
          </div>
        </div>
        <!-- Time Row -->
        <div v-for="day in scheduleData" :key="day.id + '-cell'" class="schedule-cell">
          <template v-if="day.timeRanges && day.timeRanges.length">
            <div class="time-ranges-container">
              <div
                v-for="(timeRange, tIndex) in day.timeRanges"
                :key="tIndex"
                :class="['time-range-item', day.color ? `time-range-${day.color}` : '']"
              >
                {{ formatTimeRange(timeRange) }}
              </div>
            </div>
          </template>
          <div v-else class="text-grey">
            {{ noTimeLabel }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
const props = withDefaults(
  defineProps<{
    schedule?: Array<{
      id: string | number
      label: string
      timeRanges?: Array<{ from: string; to: string; slot_max: number }>
      color?: string
      headerClass?: string
    }>
    badgeColor?: string
    badgeTextColor?: string
    noTimeLabel?: string
    enableDelete?: boolean
    onDeleteDay?: (day: { id: string | number; label: string }) => void
  }>(),
  {
    schedule: () => [],
    badgeColor: 'primary',
    badgeTextColor: 'white',
    noTimeLabel: '-',
    enableDelete: false,
    onDeleteDay: undefined
  }
)

// Default day labels
const defaultLabels = [
  { id: 'monday', label: '月' },
  { id: 'tuesday', label: '火' },
  { id: 'wednesday', label: '水' },
  { id: 'thursday', label: '木' },
  { id: 'friday', label: '金' },
  { id: 'saturday', label: '土', color: 'blue', headerClass: 'text-blue' },
  { id: 'sunday', label: '日', color: 'red', headerClass: 'text-red' },
  { id: 'holiday', label: '祝日', color: 'red', headerClass: 'text-red' }
]

// Build schedule data
const scheduleData = computed(() => {
  if (props.schedule && props.schedule.length === defaultLabels.length) {
    return props.schedule
  }
  return defaultLabels.map((d) => ({
    ...d,
    timeRanges: props.schedule?.find((s) => s.id === d.id)?.timeRanges || []
  }))
})

// Month navigation state
const currentMonth = ref(new Date())
const displayMonth = computed(() => {
  const year = currentMonth.value.getFullYear()
  const month = currentMonth.value.getMonth() + 1
  return `${year}年${month}月`
})
function prevMonth() {
  const d = new Date(currentMonth.value)
  d.setMonth(d.getMonth() - 1)
  currentMonth.value = d
}
function nextMonth() {
  const d = new Date(currentMonth.value)
  d.setMonth(d.getMonth() + 1)
  currentMonth.value = d
}

// Format time ranges
function formatTimeRange(tr: { from: string; to: string; slot_max: number }) {
  let slot_max = tr.slot_max || 0
  return `${tr.from} – ${tr.to} (${slot_max})`
}
</script>

<style scoped>
.weekly-schedule-container {
  overflow-x: auto;
}

.schedule-grid {
  display: grid;
  grid-template-columns: repeat(8, minmax(80px, 1fr));
  border: 1px solid #e0e0e0;
}
.schedule-header-cell {
  padding: 8px;
  text-align: center;
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
}
.schedule-cell {
  background-color: #fafafa;
  padding: 12px 8px;
  border-right: 1px solid #e0e0e0;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  word-break: keep-all;
}
.schedule-grid > .schedule-header-cell:last-child,
.schedule-grid > .schedule-cell:last-child {
  border-right: none;
}

.delete-day-btn {
  opacity: 0.7;
}
.delete-day-btn:hover {
  opacity: 1;
}

.navigation-info {
  background-color: #fff;
}

.empty-state {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.time-badge {
  font-size: 0.875rem;
  padding: 0.2rem 0.5rem;
}

.time-ranges-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.time-range-item {
  margin-bottom: 4px;
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  font-size: 0.85rem;
}

.time-range-item:last-child {
  margin-bottom: 0;
}

.time-range-blue {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.time-range-red {
  background-color: #ffebee;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
}
</style>
