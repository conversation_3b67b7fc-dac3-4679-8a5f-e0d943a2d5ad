import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as path from 'path'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import VitePluginHtmlEnv from 'vite-plugin-html-env'
import viteCompression from 'vite-plugin-compression'

export default defineConfig({
  server: {
    proxy: {
      // Key is the path to match
      // Value is the proxy target
      '/searchzipcode': {
        target: 'https://searchaddress-api.motocle8.com', // Target API
        changeOrigin: true, // Needed for virtual hosted sites
        rewrite: (path) => path.replace(/^\/searchzipcode/, '') // Rewrite the API path to the target path
      }
    }
  },

  plugins: [
    vue({
      template: { transformAssetUrls }
    }),

    quasar({
      sassVariables: 'src/styles/quasar-variables.scss'
    }),
    VitePluginHtmlEnv({
      compiler: true
      // compiler: false // old
    }),
    viteCompression({
      threshold: 1024, // Only compress files > 1kb
      algorithm: 'gzip', // Or 'brotliCompress' for better performance
      ext: '.gz',
      deleteOriginFile: false // Set to true if you want to delete the original file
    })
  ],
  resolve: {
    alias: [{ find: '@', replacement: path.resolve(__dirname, './src') }]
  },
  base: process.env.VITE_BASE_URL,
  build: {
    assetsDir: 'assets',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router'],
          'quasar': ['quasar', '@quasar/extras', '@quasar/quasar-ui-qcalendar'],
          'charts': ['chart.js', 'vue-chartjs'],
          'utils': ['lodash', 'dayjs', 'axios', 'pinia', 'pinia-plugin-persistedstate'],
          'document': ['jspdf', 'html2pdf.js'],
          'media': ['fabric', 'vue-advanced-cropper', 'vue-draggable-resizable', 'vuedraggable'],
          'scanner': ['vue-qrcode-reader', 'jsbarcode']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },
    cssCodeSplit: true,
    reportCompressedSize: false,
    chunkSizeWarningLimit: 1000,
    sourcemap: false,
    commonjsOptions: {
      transformMixedEsModules: true
    }
  },
  optimizeDeps: {
    include: ['axios', 'vue', 'vue-router'],
    exclude: []
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "src/styles/quasar-variables.scss";`
      }
    },
    devSourcemap: false
  }
})
